{"ast": null, "code": "import GoTrueAdmin<PERSON><PERSON> from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN_MS, AUTO_REFRESH_TICK_DURATION_MS, AUTO_REFRESH_TICK_THRESHOLD, GOTRUE_URL, STORAGE_KEY, JW<PERSON>_TTL } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError, isAuthSessionMissingError, isAuthImplicitGrantRedirectError, AuthInvalidJwtError } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse } from './lib/fetch';\nimport { Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, parseParametersFromURL, getCodeChallengeAndMethod, getAlgorithm, validateExp, decodeJWT, userNotAvailableProxy, supportsLocalStorage } from './lib/helpers';\nimport { memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks';\nimport { stringToUint8Array, bytesToBase64URL } from './lib/base64url';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n  return await fn();\n}\n/**\n * Caches JWKS values for all clients created in the same environment. This is\n * especially useful for shared-memory execution environments such as Vercel's\n * Fluid Compute, AWS Lambda or Supabase's Edge Functions. Regardless of how\n * many clients are created, if they share the same storage key they will use\n * the same JWKS cache, significantly speeding up getClaims() with asymmetric\n * JWTs.\n */\nconst GLOBAL_JWKS = {};\nexport default class GoTrueClient {\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options) {\n    var _a, _b;\n    /**\n     * @experimental\n     */\n    this.userStorage = null;\n    this.memoryStorage = null;\n    this.stateChangeEmitters = new Map();\n    this.autoRefreshTicker = null;\n    this.visibilityChangedCallback = null;\n    this.refreshingDeferred = null;\n    /**\n     * Keeps track of the async client initialization.\n     * When null or not yet resolved the auth state is `unknown`\n     * Once resolved the the auth state is known and it's save to call any further client methods.\n     * Keep extra care to never reject or throw uncaught errors\n     */\n    this.initializePromise = null;\n    this.detectSessionInUrl = true;\n    this.hasCustomAuthorizationHeader = false;\n    this.suppressGetSessionWarning = false;\n    this.lockAcquired = false;\n    this.pendingInLock = [];\n    /**\n     * Used to broadcast state change events to other tabs listening.\n     */\n    this.broadcastChannel = null;\n    this.logger = console.log;\n    this.instanceID = GoTrueClient.nextInstanceID;\n    GoTrueClient.nextInstanceID += 1;\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n    }\n    const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n    this.logDebugMessages = !!settings.debug;\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug;\n    }\n    this.persistSession = settings.persistSession;\n    this.storageKey = settings.storageKey;\n    this.autoRefreshToken = settings.autoRefreshToken;\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch\n    });\n    this.url = settings.url;\n    this.headers = settings.headers;\n    this.fetch = resolveFetch(settings.fetch);\n    this.lock = settings.lock || lockNoOp;\n    this.detectSessionInUrl = settings.detectSessionInUrl;\n    this.flowType = settings.flowType;\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n    if (settings.lock) {\n      this.lock = settings.lock;\n    } else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n      this.lock = navigatorLock;\n    } else {\n      this.lock = lockNoOp;\n    }\n    if (!this.jwks) {\n      this.jwks = {\n        keys: []\n      };\n      this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n    }\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this)\n    };\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage;\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = globalThis.localStorage;\n        } else {\n          this.memoryStorage = {};\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n      }\n      if (settings.userStorage) {\n        this.userStorage = settings.userStorage;\n      }\n    } else {\n      this.memoryStorage = {};\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n    }\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n      } catch (e) {\n        console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n      }\n      (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async event => {\n        this._debug('received broadcast notification from other tab or client', event);\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n      });\n    }\n    this.initialize();\n  }\n  /**\n   * The JWKS used for verifying asymmetric JWTs\n   */\n  get jwks() {\n    var _a, _b;\n    return (_b = (_a = GLOBAL_JWKS[this.storageKey]) === null || _a === void 0 ? void 0 : _a.jwks) !== null && _b !== void 0 ? _b : {\n      keys: []\n    };\n  }\n  set jwks(value) {\n    GLOBAL_JWKS[this.storageKey] = Object.assign(Object.assign({}, GLOBAL_JWKS[this.storageKey]), {\n      jwks: value\n    });\n  }\n  get jwks_cached_at() {\n    var _a, _b;\n    return (_b = (_a = GLOBAL_JWKS[this.storageKey]) === null || _a === void 0 ? void 0 : _a.cachedAt) !== null && _b !== void 0 ? _b : Number.MIN_SAFE_INTEGER;\n  }\n  set jwks_cached_at(value) {\n    GLOBAL_JWKS[this.storageKey] = Object.assign(Object.assign({}, GLOBAL_JWKS[this.storageKey]), {\n      cachedAt: value\n    });\n  }\n  _debug() {\n    if (this.logDebugMessages) {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n    }\n    return this;\n  }\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize() {\n    if (this.initializePromise) {\n      return await this.initializePromise;\n    }\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize();\n      });\n    })();\n    return await this.initializePromise;\n  }\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  async _initialize() {\n    var _a;\n    try {\n      const params = parseParametersFromURL(window.location.href);\n      let callbackUrlType = 'none';\n      if (this._isImplicitGrantCallback(params)) {\n        callbackUrlType = 'implicit';\n      } else if (await this._isPKCECallback(params)) {\n        callbackUrlType = 'pkce';\n      }\n      /**\n       * Attempt to get the session from the URL only if these conditions are fulfilled\n       *\n       * Note: If the URL isn't one of the callback url types (implicit or pkce),\n       * then there could be an existing session so we don't want to prematurely remove it\n       */\n      if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n        const {\n          data,\n          error\n        } = await this._getSessionFromURL(params, callbackUrlType);\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error);\n          if (isAuthImplicitGrantRedirectError(error)) {\n            const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n            if (errorCode === 'identity_already_exists' || errorCode === 'identity_not_found' || errorCode === 'single_identity_not_deletable') {\n              return {\n                error\n              };\n            }\n          }\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession();\n          return {\n            error\n          };\n        }\n        const {\n          session,\n          redirectType\n        } = data;\n        this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n        await this._saveSession(session);\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session);\n          }\n        }, 0);\n        return {\n          error: null\n        };\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh();\n      return {\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          error\n        };\n      }\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error)\n      };\n    } finally {\n      await this._handleVisibilityChange();\n      this._debug('#_initialize()', 'end');\n    }\n  }\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials) {\n    var _a, _b, _c;\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n          gotrue_meta_security: {\n            captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken\n          }\n        },\n        xform: _sessionResponse\n      });\n      const {\n        data,\n        error\n      } = res;\n      if (error || !data) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: error\n        };\n      }\n      const session = data.session;\n      const user = data.user;\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials) {\n    var _a, _b, _c;\n    try {\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          xform: _sessionResponse\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n            channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponse\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error || !data) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: error\n        };\n      }\n      const session = data.session;\n      const user = data.user;\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(credentials) {\n    try {\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign({\n          user: data.user,\n          session: data.session\n        }, data.weak_password ? {\n          weakPassword: data.weak_password\n        } : null),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials) {\n    var _a, _b, _c, _d;\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n      scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n      queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n      skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect\n    });\n  }\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode) {\n    await this.initializePromise;\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode);\n    });\n  }\n  /**\n   * Signs in a user by verifying a message signed by the user's private key.\n   * Only Solana supported at this time, using the Sign in with Solana standard.\n   */\n  async signInWithWeb3(credentials) {\n    const {\n      chain\n    } = credentials;\n    if (chain === 'solana') {\n      return await this.signInWithSolana(credentials);\n    }\n    throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`);\n  }\n  async signInWithSolana(credentials) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    let message;\n    let signature;\n    if ('message' in credentials) {\n      message = credentials.message;\n      signature = credentials.signature;\n    } else {\n      const {\n        chain,\n        wallet,\n        statement,\n        options\n      } = credentials;\n      let resolvedWallet;\n      if (!isBrowser()) {\n        if (typeof wallet !== 'object' || !(options === null || options === void 0 ? void 0 : options.url)) {\n          throw new Error('@supabase/auth-js: Both wallet and url must be specified in non-browser environments.');\n        }\n        resolvedWallet = wallet;\n      } else if (typeof wallet === 'object') {\n        resolvedWallet = wallet;\n      } else {\n        const windowAny = window;\n        if ('solana' in windowAny && typeof windowAny.solana === 'object' && ('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function' || 'signMessage' in windowAny.solana && typeof windowAny.solana.signMessage === 'function')) {\n          resolvedWallet = windowAny.solana;\n        } else {\n          throw new Error(`@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`);\n        }\n      }\n      const url = new URL((_a = options === null || options === void 0 ? void 0 : options.url) !== null && _a !== void 0 ? _a : window.location.href);\n      if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n        const output = await resolvedWallet.signIn(Object.assign(Object.assign(Object.assign({\n          issuedAt: new Date().toISOString()\n        }, options === null || options === void 0 ? void 0 : options.signInWithSolana), {\n          // non-overridable properties\n          version: '1',\n          domain: url.host,\n          uri: url.href\n        }), statement ? {\n          statement\n        } : null));\n        let outputToProcess;\n        if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n          outputToProcess = output[0];\n        } else if (output && typeof output === 'object' && 'signedMessage' in output && 'signature' in output) {\n          outputToProcess = output;\n        } else {\n          throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value');\n        }\n        if ('signedMessage' in outputToProcess && 'signature' in outputToProcess && (typeof outputToProcess.signedMessage === 'string' || outputToProcess.signedMessage instanceof Uint8Array) && outputToProcess.signature instanceof Uint8Array) {\n          message = typeof outputToProcess.signedMessage === 'string' ? outputToProcess.signedMessage : new TextDecoder().decode(outputToProcess.signedMessage);\n          signature = outputToProcess.signature;\n        } else {\n          throw new Error('@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields');\n        }\n      } else {\n        if (!('signMessage' in resolvedWallet) || typeof resolvedWallet.signMessage !== 'function' || !('publicKey' in resolvedWallet) || typeof resolvedWallet !== 'object' || !resolvedWallet.publicKey || !('toBase58' in resolvedWallet.publicKey) || typeof resolvedWallet.publicKey.toBase58 !== 'function') {\n          throw new Error('@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API');\n        }\n        message = [`${url.host} wants you to sign in with your Solana account:`, resolvedWallet.publicKey.toBase58(), ...(statement ? ['', statement, ''] : ['']), 'Version: 1', `URI: ${url.href}`, `Issued At: ${(_c = (_b = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _b === void 0 ? void 0 : _b.issuedAt) !== null && _c !== void 0 ? _c : new Date().toISOString()}`, ...(((_d = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _d === void 0 ? void 0 : _d.notBefore) ? [`Not Before: ${options.signInWithSolana.notBefore}`] : []), ...(((_e = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _e === void 0 ? void 0 : _e.expirationTime) ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`] : []), ...(((_f = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _f === void 0 ? void 0 : _f.chainId) ? [`Chain ID: ${options.signInWithSolana.chainId}`] : []), ...(((_g = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _g === void 0 ? void 0 : _g.nonce) ? [`Nonce: ${options.signInWithSolana.nonce}`] : []), ...(((_h = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _h === void 0 ? void 0 : _h.requestId) ? [`Request ID: ${options.signInWithSolana.requestId}`] : []), ...(((_k = (_j = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _j === void 0 ? void 0 : _j.resources) === null || _k === void 0 ? void 0 : _k.length) ? ['Resources', ...options.signInWithSolana.resources.map(resource => `- ${resource}`)] : [])].join('\\n');\n        const maybeSignature = await resolvedWallet.signMessage(new TextEncoder().encode(message), 'utf8');\n        if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n          throw new Error('@supabase/auth-js: Wallet signMessage() API returned an recognized value');\n        }\n        signature = maybeSignature;\n      }\n    }\n    try {\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=web3`, {\n        headers: this.headers,\n        body: Object.assign({\n          chain: 'solana',\n          message,\n          signature: bytesToBase64URL(signature)\n        }, ((_l = credentials.options) === null || _l === void 0 ? void 0 : _l.captchaToken) ? {\n          gotrue_meta_security: {\n            captcha_token: (_m = credentials.options) === null || _m === void 0 ? void 0 : _m.captchaToken\n          }\n        } : null),\n        xform: _sessionResponse\n      });\n      if (error) {\n        throw error;\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign({}, data),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _exchangeCodeForSession(authCode) {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n    try {\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n        headers: this.headers,\n        body: {\n          auth_code: authCode,\n          code_verifier: codeVerifier\n        },\n        xform: _sessionResponse\n      });\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n      if (error) {\n        throw error;\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign(Object.assign({}, data), {\n          redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null\n        }),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials) {\n    try {\n      const {\n        options,\n        provider,\n        token,\n        access_token,\n        nonce\n      } = credentials;\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: {\n            captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n          }\n        },\n        xform: _sessionResponse\n      });\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials) {\n    var _a, _b, _c, _d, _e;\n    try {\n      if ('email' in credentials) {\n        const {\n          email,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        const {\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      if ('phone' in credentials) {\n        const {\n          phone,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n            create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms'\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params) {\n    var _a, _b;\n    try {\n      let redirectTo = undefined;\n      let captchaToken = undefined;\n      if ('options' in params) {\n        redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n        captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n      }\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: Object.assign(Object.assign({}, params), {\n          gotrue_meta_security: {\n            captcha_token: captchaToken\n          }\n        }),\n        redirectTo,\n        xform: _sessionResponse\n      });\n      if (error) {\n        throw error;\n      }\n      if (!data) {\n        throw new Error('An error occurred on token verification.');\n      }\n      const session = data.session;\n      const user = data.user;\n      if (session === null || session === void 0 ? void 0 : session.access_token) {\n        await this._saveSession(session);\n        await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params) {\n    var _a, _b, _c;\n    try {\n      let codeChallenge = null;\n      let codeChallengeMethod = null;\n      if (this.flowType === 'pkce') {\n        ;\n        [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, 'providerId' in params ? {\n          provider_id: params.providerId\n        } : null), 'domain' in params ? {\n          domain: params.domain\n        } : null), {\n          redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined\n        }), ((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken) ? {\n          gotrue_meta_security: {\n            captcha_token: params.options.captchaToken\n          }\n        } : null), {\n          skip_http_redirect: true,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod\n        }),\n        headers: this.headers,\n        xform: _ssoResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate() {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate();\n    });\n  }\n  async _reauthenticate() {\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) throw sessionError;\n        if (!session) throw new AuthSessionMissingError();\n        const {\n          error\n        } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials) {\n    try {\n      const endpoint = `${this.url}/resend`;\n      if ('email' in credentials) {\n        const {\n          email,\n          type,\n          options\n        } = credentials;\n        const {\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          type,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise;\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async result => {\n        return result;\n      });\n    });\n    return result;\n  }\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  async _acquireLock(acquireTimeout, fn) {\n    this._debug('#_acquireLock', 'begin', acquireTimeout);\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length ? this.pendingInLock[this.pendingInLock.length - 1] : Promise.resolve();\n        const result = (async () => {\n          await last;\n          return await fn();\n        })();\n        this.pendingInLock.push((async () => {\n          try {\n            await result;\n          } catch (e) {\n            // we just care if it finished\n          }\n        })());\n        return result;\n      }\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n        try {\n          this.lockAcquired = true;\n          const result = fn();\n          this.pendingInLock.push((async () => {\n            try {\n              await result;\n            } catch (e) {\n              // we just care if it finished\n            }\n          })());\n          await result;\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock];\n            await Promise.all(waitOn);\n            this.pendingInLock.splice(0, waitOn.length);\n          }\n          return await result;\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n          this.lockAcquired = false;\n        }\n      });\n    } finally {\n      this._debug('#_acquireLock', 'end');\n    }\n  }\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  async _useSession(fn) {\n    this._debug('#_useSession', 'begin');\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession();\n      return await fn(result);\n    } finally {\n      this._debug('#_useSession', 'end');\n    }\n  }\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  async __loadSession() {\n    this._debug('#__loadSession()', 'begin');\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n    }\n    try {\n      let currentSession = null;\n      const maybeSession = await getItemAsync(this.storage, this.storageKey);\n      this._debug('#getSession()', 'session from storage', maybeSession);\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession;\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid');\n          await this._removeSession();\n        }\n      }\n      if (!currentSession) {\n        return {\n          data: {\n            session: null\n          },\n          error: null\n        };\n      }\n      // A session is considered expired before the access token _actually_\n      // expires. When the autoRefreshToken option is off (or when the tab is\n      // in the background), very eager users of getSession() -- like\n      // realtime-js -- might send a valid JWT which will expire by the time it\n      // reaches the server.\n      const hasExpired = currentSession.expires_at ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS : false;\n      this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n      if (!hasExpired) {\n        if (this.userStorage) {\n          const maybeUser = await getItemAsync(this.userStorage, this.storageKey + '-user');\n          if (maybeUser === null || maybeUser === void 0 ? void 0 : maybeUser.user) {\n            currentSession.user = maybeUser.user;\n          } else {\n            currentSession.user = userNotAvailableProxy();\n          }\n        }\n        if (this.storage.isServer && currentSession.user) {\n          let suppressWarning = this.suppressGetSessionWarning;\n          const proxySession = new Proxy(currentSession, {\n            get: (target, prop, receiver) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver);\n            }\n          });\n          currentSession = proxySession;\n        }\n        return {\n          data: {\n            session: currentSession\n          },\n          error: null\n        };\n      }\n      const {\n        session,\n        error\n      } = await this._callRefreshToken(currentSession.refresh_token);\n      if (error) {\n        return {\n          data: {\n            session: null\n          },\n          error\n        };\n      }\n      return {\n        data: {\n          session\n        },\n        error: null\n      };\n    } finally {\n      this._debug('#__loadSession()', 'end');\n    }\n  }\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt) {\n    if (jwt) {\n      return await this._getUser(jwt);\n    }\n    await this.initializePromise;\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser();\n    });\n    return result;\n  }\n  async _getUser(jwt) {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse\n        });\n      }\n      return await this._useSession(async result => {\n        var _a, _b, _c;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        // returns an error if there is no access_token or custom authorization header\n        if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n          return {\n            data: {\n              user: null\n            },\n            error: new AuthSessionMissingError()\n          };\n        }\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n          xform: _userResponse\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n          await this._removeSession();\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        }\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(attributes) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options);\n    });\n  }\n  async _updateUser(attributes) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          throw sessionError;\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError();\n        }\n        const session = sessionData.session;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        const {\n          data,\n          error: userError\n        } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: Object.assign(Object.assign({}, attributes), {\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          }),\n          jwt: session.access_token,\n          xform: _userResponse\n        });\n        if (userError) throw userError;\n        session.user = data.user;\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('USER_UPDATED', session);\n        return {\n          data: {\n            user: session.user\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession);\n    });\n  }\n  async _setSession(currentSession) {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError();\n      }\n      const timeNow = Date.now() / 1000;\n      let expiresAt = timeNow;\n      let hasExpired = true;\n      let session = null;\n      const {\n        payload\n      } = decodeJWT(currentSession.access_token);\n      if (payload.exp) {\n        expiresAt = payload.exp;\n        hasExpired = expiresAt <= timeNow;\n      }\n      if (hasExpired) {\n        const {\n          session: refreshedSession,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!refreshedSession) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        session = refreshedSession;\n      } else {\n        const {\n          data,\n          error\n        } = await this._getUser(currentSession.access_token);\n        if (error) {\n          throw error;\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt\n        };\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user: session.user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession);\n    });\n  }\n  async _refreshSession(currentSession) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        if (!currentSession) {\n          const {\n            data,\n            error\n          } = result;\n          if (error) {\n            throw error;\n          }\n          currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n        }\n        if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n          throw new AuthSessionMissingError();\n        }\n        const {\n          session,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        return {\n          data: {\n            user: session.user,\n            session\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets the session data from a URL string\n   */\n  async _getSessionFromURL(params, callbackUrlType) {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.');\n      // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n      if (params.error || params.error_description || params.error_code) {\n        // The error class returned implies that the redirect is from an implicit grant flow\n        // but it could also be from a redirect error from a PKCE flow.\n        throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n          error: params.error || 'unspecified_error',\n          code: params.error_code || 'unspecified_code'\n        });\n      }\n      // Checks for mismatches between the flowType initialised in the client and the URL parameters\n      switch (callbackUrlType) {\n        case 'implicit':\n          if (this.flowType === 'pkce') {\n            throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n          }\n          break;\n        case 'pkce':\n          if (this.flowType === 'implicit') {\n            throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n          }\n          break;\n        default:\n        // there's no mismatch so we continue\n      }\n      // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n      if (callbackUrlType === 'pkce') {\n        this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n        const {\n          data,\n          error\n        } = await this._exchangeCodeForSession(params.code);\n        if (error) throw error;\n        const url = new URL(window.location.href);\n        url.searchParams.delete('code');\n        window.history.replaceState(window.history.state, '', url.toString());\n        return {\n          data: {\n            session: data.session,\n            redirectType: null\n          },\n          error: null\n        };\n      }\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type\n      } = params;\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL');\n      }\n      const timeNow = Math.round(Date.now() / 1000);\n      const expiresIn = parseInt(expires_in);\n      let expiresAt = timeNow + expiresIn;\n      if (expires_at) {\n        expiresAt = parseInt(expires_at);\n      }\n      const actuallyExpiresIn = expiresAt - timeNow;\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n        console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n      }\n      const issuedAt = expiresAt - expiresIn;\n      if (timeNow - issuedAt >= 120) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n      } else if (timeNow - issuedAt < 0) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n      }\n      const {\n        data,\n        error\n      } = await this._getUser(access_token);\n      if (error) throw error;\n      const session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user\n      };\n      // Remove tokens from URL\n      window.location.hash = '';\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n      return {\n        data: {\n          session,\n          redirectType: params.type\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  _isImplicitGrantCallback(params) {\n    return Boolean(params.access_token || params.error_description);\n  }\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  async _isPKCECallback(params) {\n    const currentStorageContent = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    return !!(params.code && currentStorageContent);\n  }\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      scope: 'global'\n    };\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options);\n    });\n  }\n  async _signOut() {\n    let {\n      scope\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      scope: 'global'\n    };\n    return await this._useSession(async result => {\n      var _a;\n      const {\n        data,\n        error: sessionError\n      } = result;\n      if (sessionError) {\n        return {\n          error: sessionError\n        };\n      }\n      const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n      if (accessToken) {\n        const {\n          error\n        } = await this.admin.signOut(accessToken, scope);\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401 || error.status === 403))) {\n            return {\n              error\n            };\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession();\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n      }\n      return {\n        error: null\n      };\n    });\n  }\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(callback) {\n    const id = uuid();\n    const subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id);\n        this.stateChangeEmitters.delete(id);\n      }\n    };\n    this._debug('#onAuthStateChange()', 'registered callback with id', id);\n    this.stateChangeEmitters.set(id, subscription);\n    (async () => {\n      await this.initializePromise;\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id);\n      });\n    })();\n    return {\n      data: {\n        subscription\n      }\n    };\n  }\n  async _emitInitialSession(id) {\n    return await this._useSession(async result => {\n      var _a, _b;\n      try {\n        const {\n          data: {\n            session\n          },\n          error\n        } = result;\n        if (error) throw error;\n        await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n      } catch (err) {\n        await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n        console.error(err);\n      }\n    });\n  }\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(email) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let codeChallenge = null;\n    let codeChallengeMethod = null;\n    if (this.flowType === 'pkce') {\n      ;\n      [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey, true // isPasswordRecovery\n      );\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: {\n            captcha_token: options.captchaToken\n          }\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities() {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this.getUser();\n      if (error) throw error;\n      return {\n        data: {\n          identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : []\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials) {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this._useSession(async result => {\n        var _a, _b, _c, _d, _e;\n        const {\n          data,\n          error\n        } = result;\n        if (error) throw error;\n        const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n          redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n          scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n          queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n          skipBrowserRedirect: true\n        });\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined\n        });\n      });\n      if (error) throw error;\n      if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n        window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n      }\n      return {\n        data: {\n          provider: credentials.provider,\n          url: data === null || data === void 0 ? void 0 : data.url\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            provider: credentials.provider,\n            url: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n          headers: this.headers,\n          jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  async _refreshAccessToken(refreshToken) {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      const startedAt = Date.now();\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(async attempt => {\n        if (attempt > 0) {\n          await sleep(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n        }\n        this._debug(debugName, 'refreshing attempt', attempt);\n        return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n          body: {\n            refresh_token: refreshToken\n          },\n          headers: this.headers,\n          xform: _sessionResponse\n        });\n      }, (attempt, error) => {\n        const nextBackOffInterval = 200 * Math.pow(2, attempt);\n        return error && isAuthRetryableFetchError(error) &&\n        // retryable only if the request can be sent before the backoff overflows the tick duration\n        Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS;\n      });\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  _isValidSession(maybeSession) {\n    const isValidSession = typeof maybeSession === 'object' && maybeSession !== null && 'access_token' in maybeSession && 'refresh_token' in maybeSession && 'expires_at' in maybeSession;\n    return isValidSession;\n  }\n  async _handleProviderSignIn(provider, options) {\n    const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams\n    });\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url);\n    }\n    return {\n      data: {\n        provider,\n        url\n      },\n      error: null\n    };\n  }\n  /**\n   * Recovers the session from LocalStorage and refreshes the token\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  async _recoverAndRefresh() {\n    var _a, _b;\n    const debugName = '#_recoverAndRefresh()';\n    this._debug(debugName, 'begin');\n    try {\n      const currentSession = await getItemAsync(this.storage, this.storageKey);\n      if (currentSession && this.userStorage) {\n        let maybeUser = await getItemAsync(this.userStorage, this.storageKey + '-user');\n        if (!this.storage.isServer && Object.is(this.storage, this.userStorage) && !maybeUser) {\n          // storage and userStorage are the same storage medium, for example\n          // window.localStorage if userStorage does not have the user from\n          // storage stored, store it first thereby migrating the user object\n          // from storage -> userStorage\n          maybeUser = {\n            user: currentSession.user\n          };\n          await setItemAsync(this.userStorage, this.storageKey + '-user', maybeUser);\n        }\n        currentSession.user = (_a = maybeUser === null || maybeUser === void 0 ? void 0 : maybeUser.user) !== null && _a !== void 0 ? _a : userNotAvailableProxy();\n      } else if (currentSession && !currentSession.user) {\n        // user storage is not set, let's check if it was previously enabled so\n        // we bring back the storage as it should be\n        if (!currentSession.user) {\n          // test if userStorage was previously enabled and the storage medium was the same, to move the user back under the same key\n          const separateUser = await getItemAsync(this.storage, this.storageKey + '-user');\n          if (separateUser && (separateUser === null || separateUser === void 0 ? void 0 : separateUser.user)) {\n            currentSession.user = separateUser.user;\n            await removeItemAsync(this.storage, this.storageKey + '-user');\n            await setItemAsync(this.storage, this.storageKey, currentSession);\n          } else {\n            currentSession.user = userNotAvailableProxy();\n          }\n        }\n      }\n      this._debug(debugName, 'session from storage', currentSession);\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid');\n        if (currentSession !== null) {\n          await this._removeSession();\n        }\n        return;\n      }\n      const expiresWithMargin = ((_b = currentSession.expires_at) !== null && _b !== void 0 ? _b : Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS;\n      this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`);\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const {\n            error\n          } = await this._callRefreshToken(currentSession.refresh_token);\n          if (error) {\n            console.error(error);\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n              await this._removeSession();\n            }\n          }\n        }\n      } else if (currentSession.user && currentSession.user.__isUserNotAvailableProxy === true) {\n        // If we have a proxy user, try to get the real user data\n        try {\n          const {\n            data,\n            error: userError\n          } = await this._getUser(currentSession.access_token);\n          if (!userError && (data === null || data === void 0 ? void 0 : data.user)) {\n            currentSession.user = data.user;\n            await this._saveSession(currentSession);\n            await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n          } else {\n            this._debug(debugName, 'could not get user data, skipping SIGNED_IN notification');\n          }\n        } catch (getUserError) {\n          console.error('Error getting user data:', getUserError);\n          this._debug(debugName, 'error getting user data, skipping SIGNED_IN notification', getUserError);\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err);\n      console.error(err);\n      return;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  async _callRefreshToken(refreshToken) {\n    var _a, _b;\n    if (!refreshToken) {\n      throw new AuthSessionMissingError();\n    }\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise;\n    }\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      this.refreshingDeferred = new Deferred();\n      const {\n        data,\n        error\n      } = await this._refreshAccessToken(refreshToken);\n      if (error) throw error;\n      if (!data.session) throw new AuthSessionMissingError();\n      await this._saveSession(data.session);\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n      const result = {\n        session: data.session,\n        error: null\n      };\n      this.refreshingDeferred.resolve(result);\n      return result;\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        const result = {\n          session: null,\n          error\n        };\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession();\n        }\n        (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n        return result;\n      }\n      (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n      throw error;\n    } finally {\n      this.refreshingDeferred = null;\n      this._debug(debugName, 'end');\n    }\n  }\n  async _notifyAllSubscribers(event, session) {\n    let broadcast = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    const debugName = `#_notifyAllSubscribers(${event})`;\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({\n          event,\n          session\n        });\n      }\n      const errors = [];\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async x => {\n        try {\n          await x.callback(event, session);\n        } catch (e) {\n          errors.push(e);\n        }\n      });\n      await Promise.all(promises);\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i]);\n        }\n        throw errors[0];\n      }\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  async _saveSession(session) {\n    this._debug('#_saveSession()', session);\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true;\n    // Create a shallow copy to work with, to avoid mutating the original session object if it's used elsewhere\n    const sessionToProcess = Object.assign({}, session);\n    const userIsProxy = sessionToProcess.user && sessionToProcess.user.__isUserNotAvailableProxy === true;\n    if (this.userStorage) {\n      if (!userIsProxy && sessionToProcess.user) {\n        // If it's a real user object, save it to userStorage.\n        await setItemAsync(this.userStorage, this.storageKey + '-user', {\n          user: sessionToProcess.user\n        });\n      } else if (userIsProxy) {\n        // If it's the proxy, it means user was not found in userStorage.\n        // We should ensure no stale user data for this key exists in userStorage if we were to save null,\n        // or simply not save the proxy. For now, we don't save the proxy here.\n        // If there's a need to clear userStorage if user becomes proxy, that logic would go here.\n      }\n      // Prepare the main session data for primary storage: remove the user property before cloning\n      // This is important because the original session.user might be the proxy\n      const mainSessionData = Object.assign({}, sessionToProcess);\n      delete mainSessionData.user; // Remove user (real or proxy) before cloning for main storage\n      const clonedMainSessionData = structuredClone(mainSessionData);\n      await setItemAsync(this.storage, this.storageKey, clonedMainSessionData);\n    } else {\n      // No userStorage is configured.\n      // In this case, session.user should ideally not be a proxy.\n      // If it were, structuredClone would fail. This implies an issue elsewhere if user is a proxy here\n      const clonedSession = structuredClone(sessionToProcess); // sessionToProcess still has its original user property\n      await setItemAsync(this.storage, this.storageKey, clonedSession);\n    }\n  }\n  async _removeSession() {\n    this._debug('#_removeSession()');\n    await removeItemAsync(this.storage, this.storageKey);\n    await removeItemAsync(this.storage, this.storageKey + '-code-verifier');\n    await removeItemAsync(this.storage, this.storageKey + '-user');\n    if (this.userStorage) {\n      await removeItemAsync(this.userStorage, this.storageKey + '-user');\n    }\n    await this._notifyAllSubscribers('SIGNED_OUT', null);\n  }\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()');\n    const callback = this.visibilityChangedCallback;\n    this.visibilityChangedCallback = null;\n    try {\n      if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n        window.removeEventListener('visibilitychange', callback);\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e);\n    }\n  }\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  async _startAutoRefresh() {\n    await this._stopAutoRefresh();\n    this._debug('#_startAutoRefresh()');\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS);\n    this.autoRefreshTicker = ticker;\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref();\n      // @ts-expect-error TS has no context of Deno\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-expect-error TS has no context of Deno\n      Deno.unrefTimer(ticker);\n    }\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise;\n      await this._autoRefreshTokenTick();\n    }, 0);\n  }\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()');\n    const ticker = this.autoRefreshTicker;\n    this.autoRefreshTicker = null;\n    if (ticker) {\n      clearInterval(ticker);\n    }\n  }\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._startAutoRefresh();\n  }\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._stopAutoRefresh();\n  }\n  /**\n   * Runs the auto refresh token tick.\n   */\n  async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin');\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now();\n          try {\n            return await this._useSession(async result => {\n              const {\n                data: {\n                  session\n                }\n              } = result;\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session');\n                return;\n              }\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS);\n              this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token);\n              }\n            });\n          } catch (e) {\n            console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end');\n        }\n      });\n    } catch (e) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available');\n      } else {\n        throw e;\n      }\n    }\n  }\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()');\n    if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh();\n      }\n      return false;\n    }\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n      window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true); // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error);\n    }\n  }\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  async _onVisibilityChanged(calledFromInitialize) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n    this._debug(methodName, 'visibilityState', document.visibilityState);\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh();\n      }\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise;\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n            // visibility has changed while waiting for the lock, abort\n            return;\n          }\n          // recover the session\n          await this._recoverAndRefresh();\n        });\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh();\n      }\n    }\n  }\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  async _getUrlForProvider(url, provider, options) {\n    const urlParams = [`provider=${encodeURIComponent(provider)}`];\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n    }\n    if (options === null || options === void 0 ? void 0 : options.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`\n      });\n      urlParams.push(flowParams.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.queryParams) {\n      const query = new URLSearchParams(options.queryParams);\n      urlParams.push(query.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n    }\n    return `${url}?${urlParams.join('&')}`;\n  }\n  async _unenroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _enroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        const body = Object.assign({\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType\n        }, params.factorType === 'phone' ? {\n          phone: params.phone\n        } : {\n          issuer: params.issuer\n        });\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n        if (error) {\n          return {\n            data: null,\n            error\n          };\n        }\n        if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n        }\n        return {\n          data,\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  async _verify(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          const {\n            data,\n            error\n          } = await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n            body: {\n              code: params.code,\n              challenge_id: params.challengeId\n            },\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n          if (error) {\n            return {\n              data: null,\n              error\n            };\n          }\n          await this._saveSession(Object.assign({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in\n          }, data));\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n          return {\n            data,\n            error\n          };\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  async _challenge(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          return await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n            body: {\n              channel: params.channel\n            },\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  async _challengeAndVerify(params) {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n    const {\n      data: challengeData,\n      error: challengeError\n    } = await this._challenge({\n      factorId: params.factorId\n    });\n    if (challengeError) {\n      return {\n        data: null,\n        error: challengeError\n      };\n    }\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  async _listFactors() {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: {\n        user\n      },\n      error: userError\n    } = await this.getUser();\n    if (userError) {\n      return {\n        data: null,\n        error: userError\n      };\n    }\n    const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n    const totp = factors.filter(factor => factor.factor_type === 'totp' && factor.status === 'verified');\n    const phone = factors.filter(factor => factor.factor_type === 'phone' && factor.status === 'verified');\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone\n      },\n      error: null\n    };\n  }\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  async _getAuthenticatorAssuranceLevel() {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              currentLevel: null,\n              nextLevel: null,\n              currentAuthenticationMethods: []\n            },\n            error: null\n          };\n        }\n        const {\n          payload\n        } = decodeJWT(session.access_token);\n        let currentLevel = null;\n        if (payload.aal) {\n          currentLevel = payload.aal;\n        }\n        let nextLevel = currentLevel;\n        const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter(factor => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2';\n        }\n        const currentAuthenticationMethods = payload.amr || [];\n        return {\n          data: {\n            currentLevel,\n            nextLevel,\n            currentAuthenticationMethods\n          },\n          error: null\n        };\n      });\n    });\n  }\n  async fetchJwk(kid) {\n    let jwks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      keys: []\n    };\n    // try fetching from the supplied jwks\n    let jwk = jwks.keys.find(key => key.kid === kid);\n    if (jwk) {\n      return jwk;\n    }\n    const now = Date.now();\n    // try fetching from cache\n    jwk = this.jwks.keys.find(key => key.kid === kid);\n    // jwk exists and jwks isn't stale\n    if (jwk && this.jwks_cached_at + JWKS_TTL > now) {\n      return jwk;\n    }\n    // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n    const {\n      data,\n      error\n    } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n      headers: this.headers\n    });\n    if (error) {\n      throw error;\n    }\n    if (!data.keys || data.keys.length === 0) {\n      return null;\n    }\n    this.jwks = data;\n    this.jwks_cached_at = now;\n    // Find the signing key\n    jwk = data.keys.find(key => key.kid === kid);\n    if (!jwk) {\n      return null;\n    }\n    return jwk;\n  }\n  /**\n   * Extracts the JWT claims present in the access token by first verifying the\n   * JWT against the server's JSON Web Key Set endpoint\n   * `/.well-known/jwks.json` which is often cached, resulting in significantly\n   * faster responses. Prefer this method over {@link #getUser} which always\n   * sends a request to the Auth server for each JWT.\n   *\n   * If the project is not using an asymmetric JWT signing key (like ECC or\n   * RSA) it always sends a request to the Auth server (similar to {@link\n   * #getUser}) to verify the JWT.\n   *\n   * @param jwt An optional specific JWT you wish to verify, not the one you\n   *            can obtain from {@link #getSession}.\n   * @param options Various additional options that allow you to customize the\n   *                behavior of this method.\n   */\n  async getClaims(jwt) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    try {\n      let token = jwt;\n      if (!token) {\n        const {\n          data,\n          error\n        } = await this.getSession();\n        if (error || !data.session) {\n          return {\n            data: null,\n            error\n          };\n        }\n        token = data.session.access_token;\n      }\n      const {\n        header,\n        payload,\n        signature,\n        raw: {\n          header: rawHeader,\n          payload: rawPayload\n        }\n      } = decodeJWT(token);\n      if (!(options === null || options === void 0 ? void 0 : options.allowExpired)) {\n        // Reject expired JWTs should only happen if jwt argument was passed\n        validateExp(payload.exp);\n      }\n      const signingKey = !header.alg || header.alg.startsWith('HS') || !header.kid || !('crypto' in globalThis && 'subtle' in globalThis.crypto) ? null : await this.fetchJwk(header.kid, (options === null || options === void 0 ? void 0 : options.keys) ? {\n        keys: options.keys\n      } : options === null || options === void 0 ? void 0 : options.jwks);\n      // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n      if (!signingKey) {\n        const {\n          error\n        } = await this.getUser(token);\n        if (error) {\n          throw error;\n        }\n        // getUser succeeds so the claims in the JWT can be trusted\n        return {\n          data: {\n            claims: payload,\n            header,\n            signature\n          },\n          error: null\n        };\n      }\n      const algorithm = getAlgorithm(header.alg);\n      // Convert JWK to CryptoKey\n      const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, ['verify']);\n      // Verify the signature\n      const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, stringToUint8Array(`${rawHeader}.${rawPayload}`));\n      if (!isValid) {\n        throw new AuthInvalidJwtError('Invalid JWT signature');\n      }\n      // If verification succeeds, decode and return claims\n      return {\n        data: {\n          claims: payload,\n          header,\n          signature\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n}\nGoTrueClient.nextInstanceID = 0;", "map": {"version": 3, "names": ["GoTrueAdminApi", "DEFAULT_HEADERS", "EXPIRY_MARGIN_MS", "AUTO_REFRESH_TICK_DURATION_MS", "AUTO_REFRESH_TICK_THRESHOLD", "GOTRUE_URL", "STORAGE_KEY", "JWKS_TTL", "AuthImplicitGrantRedirectError", "AuthPKCEGrantCodeExchangeError", "AuthInvalidCredentialsError", "AuthSessionMissingError", "AuthInvalidTokenResponseError", "AuthUnknownError", "isAuthApiError", "isAuthError", "isAuthRetryableFetchError", "isAuthSessionMissingError", "isAuthImplicitGrantRedirectError", "AuthInvalidJwtError", "_request", "_sessionResponse", "_sessionResponsePassword", "_userResponse", "_ssoResponse", "Deferred", "getItemAsync", "<PERSON><PERSON><PERSON><PERSON>", "removeItemAsync", "resolveFetch", "setItemAsync", "uuid", "retryable", "sleep", "parseParametersFromURL", "getCodeChallengeAndMethod", "getAlgorithm", "validateExp", "decodeJWT", "userNotAvailableProxy", "supportsLocalStorage", "memoryLocalStorageAdapter", "polyfillGlobalThis", "version", "LockAcquireTimeoutError", "navigator<PERSON><PERSON>", "stringToUint8Array", "bytesToBase64URL", "DEFAULT_OPTIONS", "url", "storageKey", "autoRefreshToken", "persistSession", "detectSessionInUrl", "headers", "flowType", "debug", "hasCustomAuthorizationHeader", "lockNoOp", "name", "acquireTimeout", "fn", "GLOBAL_JWKS", "GoTrueClient", "constructor", "options", "userStorage", "memoryStorage", "stateChangeEmitters", "Map", "autoRefreshTicker", "visibilityChangedCallback", "refreshing<PERSON><PERSON>erred", "initializePromise", "suppressGetSessionWarning", "lockAcquired", "pendingInLock", "broadcastChannel", "logger", "console", "log", "instanceID", "nextInstanceID", "warn", "settings", "Object", "assign", "logDebugMessages", "admin", "fetch", "lock", "_a", "globalThis", "navigator", "locks", "jwks", "keys", "jwks_cached_at", "Number", "MIN_SAFE_INTEGER", "mfa", "verify", "_verify", "bind", "enroll", "_enroll", "unenroll", "_unenroll", "challenge", "_challenge", "listFactors", "_listFactors", "challengeAndVerify", "_challengeAndVerify", "getAuthenticatorAssuranceLevel", "_getAuthenticatorAssuranceLevel", "storage", "localStorage", "BroadcastChannel", "e", "error", "_b", "addEventListener", "event", "_debug", "_notifyAllSubscribers", "data", "session", "initialize", "value", "cachedAt", "_len", "arguments", "length", "args", "Array", "_key", "Date", "toISOString", "_acquireLock", "_initialize", "params", "window", "location", "href", "callbackUrlType", "_isImplicitGrantCallback", "_isPKCECallback", "_getSessionFromURL", "errorCode", "details", "code", "_removeSession", "redirectType", "_saveSession", "setTimeout", "_recoverAndRefresh", "_handleVisibilityChange", "signInAnonymously", "credentials", "res", "body", "gotrue_meta_security", "captcha_token", "_c", "captchaToken", "xform", "user", "signUp", "email", "password", "codeChallenge", "codeChallengeMethod", "redirectTo", "emailRedirectTo", "code_challenge", "code_challenge_method", "phone", "channel", "signInWithPassword", "weak_password", "weakPassword", "signInWithOAuth", "_handleProviderSignIn", "provider", "scopes", "queryParams", "skipBrowserRedirect", "_d", "exchangeCodeForSession", "authCode", "_exchangeCodeForSession", "signInWithWeb3", "chain", "signInWithSolana", "Error", "message", "signature", "wallet", "statement", "resolvedWallet", "windowAny", "solana", "signIn", "signMessage", "URL", "output", "issuedAt", "domain", "host", "uri", "outputToProcess", "isArray", "signedMessage", "Uint8Array", "TextDecoder", "decode", "public<PERSON>ey", "toBase58", "notBefore", "_e", "expirationTime", "_f", "chainId", "_g", "nonce", "_h", "requestId", "_k", "_j", "resources", "map", "resource", "join", "maybeSignature", "TextEncoder", "encode", "_l", "_m", "storageItem", "codeVerifier", "split", "auth_code", "code_verifier", "signInWithIdToken", "token", "access_token", "id_token", "signInWithOtp", "create_user", "shouldCreateUser", "messageId", "message_id", "verifyOtp", "undefined", "type", "signInWithSSO", "provider_id", "providerId", "redirect_to", "skip_http_redirect", "reauthenticate", "_reauthenticate", "_useSession", "result", "sessionError", "jwt", "resend", "endpoint", "getSession", "last", "Promise", "resolve", "push", "waitOn", "all", "splice", "__loadSession", "stack", "currentSession", "maybeSession", "_isValidSession", "hasExpired", "expires_at", "now", "maybeUser", "isServer", "suppressWarning", "proxySession", "Proxy", "get", "target", "prop", "receiver", "Reflect", "_callRefreshToken", "refresh_token", "getUser", "_getUser", "updateUser", "attributes", "_updateUser", "sessionData", "userError", "setSession", "_setSession", "timeNow", "expiresAt", "payload", "exp", "refreshedSession", "token_type", "expires_in", "refreshSession", "_refreshSession", "error_description", "error_code", "searchParams", "delete", "history", "replaceState", "state", "toString", "provider_token", "provider_refresh_token", "Math", "round", "expiresIn", "parseInt", "actuallyExpiresIn", "hash", "Boolean", "currentStorageContent", "signOut", "scope", "_signOut", "accessToken", "status", "onAuthStateChange", "callback", "id", "subscription", "unsubscribe", "set", "_emitInitialSession", "err", "resetPasswordForEmail", "getUserIdentities", "identities", "linkIdentity", "_getUrl<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlinkIdentity", "identity", "identity_id", "_refreshAccessToken", "refreshToken", "debugName", "substring", "startedAt", "attempt", "pow", "nextBackOffInterval", "isValidSession", "is", "separateUser", "expiresWithMargin", "Infinity", "__isUserNotAvailableProxy", "getUserError", "promise", "reject", "broadcast", "postMessage", "errors", "promises", "from", "values", "x", "i", "sessionToProcess", "userIsProxy", "mainSessionData", "clonedMainSessionData", "structuredClone", "clonedSession", "_removeVisibilityChangedCallback", "removeEventListener", "_startAutoRefresh", "_stopAutoRefresh", "ticker", "setInterval", "_autoRefreshTokenTick", "unref", "<PERSON><PERSON>", "unrefTimer", "clearInterval", "startAutoRefresh", "stopAutoRefresh", "expiresInTicks", "floor", "isAcquireTimeout", "_onVisibilityChanged", "calledFromInitialize", "methodName", "document", "visibilityState", "urlParams", "encodeURIComponent", "flowParams", "URLSearchParams", "query", "factorId", "friendly_name", "friendlyName", "factor_type", "factorType", "issuer", "totp", "qr_code", "challenge_id", "challengeId", "challengeData", "challengeError", "factors", "filter", "factor", "currentLevel", "nextLevel", "currentAuthenticationMethods", "aal", "verifiedFactors", "amr", "fetchJwk", "kid", "jwk", "find", "key", "getClaims", "header", "raw", "<PERSON><PERSON><PERSON><PERSON>", "rawPayload", "allowExpired", "<PERSON><PERSON><PERSON>", "alg", "startsWith", "crypto", "claims", "algorithm", "subtle", "importKey", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts"], "sourcesContent": ["import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi'\nimport {\n  DEFAULT_HEADERS,\n  EXPIRY_MARGIN_MS,\n  AUTO_REFRESH_TICK_DURATION_MS,\n  AUTO_REFRESH_TICK_THRESHOLD,\n  GOTRUE_URL,\n  STORAGE_KEY,\n  J<PERSON><PERSON>_TTL,\n} from './lib/constants'\nimport {\n  AuthError,\n  AuthImplicitGrantRedirectError,\n  AuthPKCEGrantCodeExchangeError,\n  AuthInvalidCredentialsError,\n  AuthSessionMissingError,\n  AuthInvalidTokenResponseError,\n  AuthUnknownError,\n  isAuthApiError,\n  isAuthError,\n  isAuthRetryableFetchError,\n  isAuthSessionMissingError,\n  isAuthImplicitGrantRedirectError,\n  AuthInvalidJwtError,\n} from './lib/errors'\nimport {\n  Fetch,\n  _request,\n  _sessionResponse,\n  _sessionResponsePassword,\n  _userResponse,\n  _ssoResponse,\n} from './lib/fetch'\nimport {\n  Deferred,\n  getItemAsync,\n  isBrowser,\n  removeItemAsync,\n  resolveFetch,\n  setItemAsync,\n  uuid,\n  retryable,\n  sleep,\n  parseParametersFromURL,\n  getCodeChallengeAndMethod,\n  getAlgorithm,\n  validateExp,\n  decodeJWT,\n  userNotAvailableProxy,\n  supportsLocalStorage,\n} from './lib/helpers'\nimport { memoryLocalStorageAdapter } from './lib/local-storage'\nimport { polyfillGlobalThis } from './lib/polyfills'\nimport { version } from './lib/version'\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks'\n\nimport type {\n  AuthChangeEvent,\n  AuthResponse,\n  AuthResponsePassword,\n  AuthTokenResponse,\n  AuthTokenResponsePassword,\n  AuthOtpResponse,\n  CallRefreshTokenResult,\n  GoTrueClientOptions,\n  InitializeResult,\n  OAuthResponse,\n  SSOResponse,\n  Provider,\n  Session,\n  SignInWithIdTokenCredentials,\n  SignInWithOAuthCredentials,\n  SignInWithPasswordCredentials,\n  SignInWithPasswordlessCredentials,\n  SignUpWithPasswordCredentials,\n  SignInWithSSO,\n  SignOut,\n  Subscription,\n  SupportedStorage,\n  User,\n  UserAttributes,\n  UserResponse,\n  VerifyOtpParams,\n  GoTrueMFAApi,\n  MFAEnrollParams,\n  AuthMFAEnrollResponse,\n  MFAChallengeParams,\n  AuthMFAChallengeResponse,\n  MFAUnenrollParams,\n  AuthMFAUnenrollResponse,\n  MFAVerifyParams,\n  AuthMFAVerifyResponse,\n  AuthMFAListFactorsResponse,\n  AuthMFAGetAuthenticatorAssuranceLevelResponse,\n  AuthenticatorAssuranceLevels,\n  Factor,\n  MFAChallengeAndVerifyParams,\n  ResendParams,\n  AuthFlowType,\n  LockFunc,\n  UserIdentity,\n  SignInAnonymouslyCredentials,\n  MFAEnrollTOTPParams,\n  MFAEnrollPhoneParams,\n  AuthMFAEnrollTOTPResponse,\n  AuthMFAEnrollPhoneResponse,\n  JWK,\n  JwtPayload,\n  JwtHeader,\n  SolanaWeb3Credentials,\n  SolanaWallet,\n  Web3Credentials,\n} from './lib/types'\nimport { stringToUint8Array, bytesToBase64URL } from './lib/base64url'\n\npolyfillGlobalThis() // Make \"globalThis\" available\n\nconst DEFAULT_OPTIONS: Omit<\n  Required<GoTrueClientOptions>,\n  'fetch' | 'storage' | 'userStorage' | 'lock'\n> = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false,\n}\n\nasync function lockNoOp<R>(name: string, acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n  return await fn()\n}\n\n/**\n * Caches JWKS values for all clients created in the same environment. This is\n * especially useful for shared-memory execution environments such as Vercel's\n * Fluid Compute, AWS Lambda or Supabase's Edge Functions. Regardless of how\n * many clients are created, if they share the same storage key they will use\n * the same JWKS cache, significantly speeding up getClaims() with asymmetric\n * JWTs.\n */\nconst GLOBAL_JWKS: { [storageKey: string]: { cachedAt: number; jwks: { keys: JWK[] } } } = {}\n\nexport default class GoTrueClient {\n  private static nextInstanceID = 0\n\n  private instanceID: number\n\n  /**\n   * Namespace for the GoTrue admin methods.\n   * These methods should only be used in a trusted server-side environment.\n   */\n  admin: GoTrueAdminApi\n  /**\n   * Namespace for the MFA methods.\n   */\n  mfa: GoTrueMFAApi\n  /**\n   * The storage key used to identify the values saved in localStorage\n   */\n  protected storageKey: string\n\n  protected flowType: AuthFlowType\n\n  /**\n   * The JWKS used for verifying asymmetric JWTs\n   */\n  protected get jwks() {\n    return GLOBAL_JWKS[this.storageKey]?.jwks ?? { keys: [] }\n  }\n\n  protected set jwks(value: { keys: JWK[] }) {\n    GLOBAL_JWKS[this.storageKey] = { ...GLOBAL_JWKS[this.storageKey], jwks: value }\n  }\n\n  protected get jwks_cached_at() {\n    return GLOBAL_JWKS[this.storageKey]?.cachedAt ?? Number.MIN_SAFE_INTEGER\n  }\n\n  protected set jwks_cached_at(value: number) {\n    GLOBAL_JWKS[this.storageKey] = { ...GLOBAL_JWKS[this.storageKey], cachedAt: value }\n  }\n\n  protected autoRefreshToken: boolean\n  protected persistSession: boolean\n  protected storage: SupportedStorage\n  /**\n   * @experimental\n   */\n  protected userStorage: SupportedStorage | null = null\n  protected memoryStorage: { [key: string]: string } | null = null\n  protected stateChangeEmitters: Map<string, Subscription> = new Map()\n  protected autoRefreshTicker: ReturnType<typeof setInterval> | null = null\n  protected visibilityChangedCallback: (() => Promise<any>) | null = null\n  protected refreshingDeferred: Deferred<CallRefreshTokenResult> | null = null\n  /**\n   * Keeps track of the async client initialization.\n   * When null or not yet resolved the auth state is `unknown`\n   * Once resolved the the auth state is known and it's save to call any further client methods.\n   * Keep extra care to never reject or throw uncaught errors\n   */\n  protected initializePromise: Promise<InitializeResult> | null = null\n  protected detectSessionInUrl = true\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected hasCustomAuthorizationHeader = false\n  protected suppressGetSessionWarning = false\n  protected fetch: Fetch\n  protected lock: LockFunc\n  protected lockAcquired = false\n  protected pendingInLock: Promise<any>[] = []\n\n  /**\n   * Used to broadcast state change events to other tabs listening.\n   */\n  protected broadcastChannel: BroadcastChannel | null = null\n\n  protected logDebugMessages: boolean\n  protected logger: (message: string, ...args: any[]) => void = console.log\n\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options: GoTrueClientOptions) {\n    this.instanceID = GoTrueClient.nextInstanceID\n    GoTrueClient.nextInstanceID += 1\n\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn(\n        'Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.'\n      )\n    }\n\n    const settings = { ...DEFAULT_OPTIONS, ...options }\n\n    this.logDebugMessages = !!settings.debug\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug\n    }\n\n    this.persistSession = settings.persistSession\n    this.storageKey = settings.storageKey\n    this.autoRefreshToken = settings.autoRefreshToken\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch,\n    })\n\n    this.url = settings.url\n    this.headers = settings.headers\n    this.fetch = resolveFetch(settings.fetch)\n    this.lock = settings.lock || lockNoOp\n    this.detectSessionInUrl = settings.detectSessionInUrl\n    this.flowType = settings.flowType\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader\n\n    if (settings.lock) {\n      this.lock = settings.lock\n    } else if (isBrowser() && globalThis?.navigator?.locks) {\n      this.lock = navigatorLock\n    } else {\n      this.lock = lockNoOp\n    }\n\n    if (!this.jwks) {\n      this.jwks = { keys: [] }\n      this.jwks_cached_at = Number.MIN_SAFE_INTEGER\n    }\n\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n    }\n\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = globalThis.localStorage\n        } else {\n          this.memoryStorage = {}\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n        }\n      }\n\n      if (settings.userStorage) {\n        this.userStorage = settings.userStorage\n      }\n    } else {\n      this.memoryStorage = {}\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n    }\n\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey)\n      } catch (e: any) {\n        console.error(\n          'Failed to create a new BroadcastChannel, multi-tab state changes will not be available',\n          e\n        )\n      }\n\n      this.broadcastChannel?.addEventListener('message', async (event) => {\n        this._debug('received broadcast notification from other tab or client', event)\n\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false) // broadcast = false so we don't get an endless loop of messages\n      })\n    }\n\n    this.initialize()\n  }\n\n  private _debug(...args: any[]): GoTrueClient {\n    if (this.logDebugMessages) {\n      this.logger(\n        `GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`,\n        ...args\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize(): Promise<InitializeResult> {\n    if (this.initializePromise) {\n      return await this.initializePromise\n    }\n\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize()\n      })\n    })()\n\n    return await this.initializePromise\n  }\n\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  private async _initialize(): Promise<InitializeResult> {\n    try {\n      const params = parseParametersFromURL(window.location.href)\n      let callbackUrlType = 'none'\n      if (this._isImplicitGrantCallback(params)) {\n        callbackUrlType = 'implicit'\n      } else if (await this._isPKCECallback(params)) {\n        callbackUrlType = 'pkce'\n      }\n\n      /**\n       * Attempt to get the session from the URL only if these conditions are fulfilled\n       *\n       * Note: If the URL isn't one of the callback url types (implicit or pkce),\n       * then there could be an existing session so we don't want to prematurely remove it\n       */\n      if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n        const { data, error } = await this._getSessionFromURL(params, callbackUrlType)\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error)\n\n          if (isAuthImplicitGrantRedirectError(error)) {\n            const errorCode = error.details?.code\n            if (\n              errorCode === 'identity_already_exists' ||\n              errorCode === 'identity_not_found' ||\n              errorCode === 'single_identity_not_deletable'\n            ) {\n              return { error }\n            }\n          }\n\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession()\n\n          return { error }\n        }\n\n        const { session, redirectType } = data\n\n        this._debug(\n          '#_initialize()',\n          'detected session in URL',\n          session,\n          'redirect type',\n          redirectType\n        )\n\n        await this._saveSession(session)\n\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session)\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session)\n          }\n        }, 0)\n\n        return { error: null }\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh()\n      return { error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { error }\n      }\n\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error),\n      }\n    } finally {\n      await this._handleVisibilityChange()\n      this._debug('#_initialize()', 'end')\n    }\n  }\n\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials?: SignInAnonymouslyCredentials): Promise<AuthResponse> {\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: credentials?.options?.data ?? {},\n          gotrue_meta_security: { captcha_token: credentials?.options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials: SignUpWithPasswordCredentials): Promise<AuthResponse> {\n    try {\n      let res: AuthResponse\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: options?.data ?? {},\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          xform: _sessionResponse,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: options?.data ?? {},\n            channel: options?.channel ?? 'sms',\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponse,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(\n    credentials: SignInWithPasswordCredentials\n  ): Promise<AuthTokenResponsePassword> {\n    try {\n      let res: AuthResponsePassword\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n      const { data, error } = res\n\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return {\n        data: {\n          user: data.user,\n          session: data.session,\n          ...(data.weak_password ? { weakPassword: data.weak_password } : null),\n        },\n        error,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: credentials.options?.redirectTo,\n      scopes: credentials.options?.scopes,\n      queryParams: credentials.options?.queryParams,\n      skipBrowserRedirect: credentials.options?.skipBrowserRedirect,\n    })\n  }\n\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode: string): Promise<AuthTokenResponse> {\n    await this.initializePromise\n\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode)\n    })\n  }\n\n  /**\n   * Signs in a user by verifying a message signed by the user's private key.\n   * Only Solana supported at this time, using the Sign in with Solana standard.\n   */\n  async signInWithWeb3(credentials: Web3Credentials): Promise<\n    | {\n        data: { session: Session; user: User }\n        error: null\n      }\n    | { data: { session: null; user: null }; error: AuthError }\n  > {\n    const { chain } = credentials\n\n    if (chain === 'solana') {\n      return await this.signInWithSolana(credentials)\n    }\n\n    throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`)\n  }\n\n  private async signInWithSolana(credentials: SolanaWeb3Credentials) {\n    let message: string\n    let signature: Uint8Array\n\n    if ('message' in credentials) {\n      message = credentials.message\n      signature = credentials.signature\n    } else {\n      const { chain, wallet, statement, options } = credentials\n\n      let resolvedWallet: SolanaWallet\n\n      if (!isBrowser()) {\n        if (typeof wallet !== 'object' || !options?.url) {\n          throw new Error(\n            '@supabase/auth-js: Both wallet and url must be specified in non-browser environments.'\n          )\n        }\n\n        resolvedWallet = wallet\n      } else if (typeof wallet === 'object') {\n        resolvedWallet = wallet\n      } else {\n        const windowAny = window as any\n\n        if (\n          'solana' in windowAny &&\n          typeof windowAny.solana === 'object' &&\n          (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n            ('signMessage' in windowAny.solana &&\n              typeof windowAny.solana.signMessage === 'function'))\n        ) {\n          resolvedWallet = windowAny.solana\n        } else {\n          throw new Error(\n            `@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`\n          )\n        }\n      }\n\n      const url = new URL(options?.url ?? window.location.href)\n\n      if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n        const output = await resolvedWallet.signIn({\n          issuedAt: new Date().toISOString(),\n\n          ...options?.signInWithSolana,\n\n          // non-overridable properties\n          version: '1',\n          domain: url.host,\n          uri: url.href,\n\n          ...(statement ? { statement } : null),\n        })\n\n        let outputToProcess: any\n\n        if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n          outputToProcess = output[0]\n        } else if (\n          output &&\n          typeof output === 'object' &&\n          'signedMessage' in output &&\n          'signature' in output\n        ) {\n          outputToProcess = output\n        } else {\n          throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value')\n        }\n\n        if (\n          'signedMessage' in outputToProcess &&\n          'signature' in outputToProcess &&\n          (typeof outputToProcess.signedMessage === 'string' ||\n            outputToProcess.signedMessage instanceof Uint8Array) &&\n          outputToProcess.signature instanceof Uint8Array\n        ) {\n          message =\n            typeof outputToProcess.signedMessage === 'string'\n              ? outputToProcess.signedMessage\n              : new TextDecoder().decode(outputToProcess.signedMessage)\n          signature = outputToProcess.signature\n        } else {\n          throw new Error(\n            '@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields'\n          )\n        }\n      } else {\n        if (\n          !('signMessage' in resolvedWallet) ||\n          typeof resolvedWallet.signMessage !== 'function' ||\n          !('publicKey' in resolvedWallet) ||\n          typeof resolvedWallet !== 'object' ||\n          !resolvedWallet.publicKey ||\n          !('toBase58' in resolvedWallet.publicKey) ||\n          typeof resolvedWallet.publicKey.toBase58 !== 'function'\n        ) {\n          throw new Error(\n            '@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API'\n          )\n        }\n\n        message = [\n          `${url.host} wants you to sign in with your Solana account:`,\n          resolvedWallet.publicKey.toBase58(),\n          ...(statement ? ['', statement, ''] : ['']),\n          'Version: 1',\n          `URI: ${url.href}`,\n          `Issued At: ${options?.signInWithSolana?.issuedAt ?? new Date().toISOString()}`,\n          ...(options?.signInWithSolana?.notBefore\n            ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n            : []),\n          ...(options?.signInWithSolana?.expirationTime\n            ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n            : []),\n          ...(options?.signInWithSolana?.chainId\n            ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n            : []),\n          ...(options?.signInWithSolana?.nonce ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n          ...(options?.signInWithSolana?.requestId\n            ? [`Request ID: ${options.signInWithSolana.requestId}`]\n            : []),\n          ...(options?.signInWithSolana?.resources?.length\n            ? [\n                'Resources',\n                ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n              ]\n            : []),\n        ].join('\\n')\n\n        const maybeSignature = await resolvedWallet.signMessage(\n          new TextEncoder().encode(message),\n          'utf8'\n        )\n\n        if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n          throw new Error(\n            '@supabase/auth-js: Wallet signMessage() API returned an recognized value'\n          )\n        }\n\n        signature = maybeSignature\n      }\n    }\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=web3`,\n        {\n          headers: this.headers,\n          body: {\n            chain: 'solana',\n            message,\n            signature: bytesToBase64URL(signature),\n\n            ...(credentials.options?.captchaToken\n              ? { gotrue_meta_security: { captcha_token: credentials.options?.captchaToken } }\n              : null),\n          },\n          xform: _sessionResponse,\n        }\n      )\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _exchangeCodeForSession(authCode: string): Promise<\n    | {\n        data: { session: Session; user: User; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; user: null; redirectType: null }; error: AuthError }\n  > {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n    const [codeVerifier, redirectType] = ((storageItem ?? '') as string).split('/')\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=pkce`,\n        {\n          headers: this.headers,\n          body: {\n            auth_code: authCode,\n            code_verifier: codeVerifier,\n          },\n          xform: _sessionResponse,\n        }\n      )\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null, redirectType: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data, redirectType: redirectType ?? null }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials: SignInWithIdTokenCredentials): Promise<AuthTokenResponse> {\n    try {\n      const { options, provider, token, access_token, nonce } = credentials\n\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: { captcha_token: options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n\n      const { data, error } = res\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials: SignInWithPasswordlessCredentials): Promise<AuthOtpResponse> {\n    try {\n      if ('email' in credentials) {\n        const { email, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      }\n      if ('phone' in credentials) {\n        const { phone, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            channel: options?.channel ?? 'sms',\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.')\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params: VerifyOtpParams): Promise<AuthResponse> {\n    try {\n      let redirectTo: string | undefined = undefined\n      let captchaToken: string | undefined = undefined\n      if ('options' in params) {\n        redirectTo = params.options?.redirectTo\n        captchaToken = params.options?.captchaToken\n      }\n      const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: {\n          ...params,\n          gotrue_meta_security: { captcha_token: captchaToken },\n        },\n        redirectTo,\n        xform: _sessionResponse,\n      })\n\n      if (error) {\n        throw error\n      }\n\n      if (!data) {\n        throw new Error('An error occurred on token verification.')\n      }\n\n      const session: Session | null = data.session\n      const user: User = data.user\n\n      if (session?.access_token) {\n        await this._saveSession(session as Session)\n        await this._notifyAllSubscribers(\n          params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN',\n          session\n        )\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params: SignInWithSSO): Promise<SSOResponse> {\n    try {\n      let codeChallenge: string | null = null\n      let codeChallengeMethod: string | null = null\n      if (this.flowType === 'pkce') {\n        ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n          this.storage,\n          this.storageKey\n        )\n      }\n\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: {\n          ...('providerId' in params ? { provider_id: params.providerId } : null),\n          ...('domain' in params ? { domain: params.domain } : null),\n          redirect_to: params.options?.redirectTo ?? undefined,\n          ...(params?.options?.captchaToken\n            ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n            : null),\n          skip_http_redirect: true, // fetch does not handle redirects\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n        },\n        headers: this.headers,\n        xform: _ssoResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate(): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate()\n    })\n  }\n\n  private async _reauthenticate(): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) throw sessionError\n        if (!session) throw new AuthSessionMissingError()\n\n        const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token,\n        })\n        return { data: { user: null, session: null }, error }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials: ResendParams): Promise<AuthOtpResponse> {\n    try {\n      const endpoint = `${this.url}/resend`\n      if ('email' in credentials) {\n        const { email, type, options } = credentials\n        const { error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      } else if ('phone' in credentials) {\n        const { phone, type, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError(\n        'You must provide either an email or phone number and a type'\n      )\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async (result) => {\n        return result\n      })\n    })\n\n    return result\n  }\n\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  private async _acquireLock<R>(acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n    this._debug('#_acquireLock', 'begin', acquireTimeout)\n\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length\n          ? this.pendingInLock[this.pendingInLock.length - 1]\n          : Promise.resolve()\n\n        const result = (async () => {\n          await last\n          return await fn()\n        })()\n\n        this.pendingInLock.push(\n          (async () => {\n            try {\n              await result\n            } catch (e: any) {\n              // we just care if it finished\n            }\n          })()\n        )\n\n        return result\n      }\n\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey)\n\n        try {\n          this.lockAcquired = true\n\n          const result = fn()\n\n          this.pendingInLock.push(\n            (async () => {\n              try {\n                await result\n              } catch (e: any) {\n                // we just care if it finished\n              }\n            })()\n          )\n\n          await result\n\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock]\n\n            await Promise.all(waitOn)\n\n            this.pendingInLock.splice(0, waitOn.length)\n          }\n\n          return await result\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey)\n\n          this.lockAcquired = false\n        }\n      })\n    } finally {\n      this._debug('#_acquireLock', 'end')\n    }\n  }\n\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  private async _useSession<R>(\n    fn: (\n      result:\n        | {\n            data: {\n              session: Session\n            }\n            error: null\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: AuthError\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: null\n          }\n    ) => Promise<R>\n  ): Promise<R> {\n    this._debug('#_useSession', 'begin')\n\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession()\n\n      return await fn(result)\n    } finally {\n      this._debug('#_useSession', 'end')\n    }\n  }\n\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  private async __loadSession(): Promise<\n    | {\n        data: {\n          session: Session\n        }\n        error: null\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: AuthError\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: null\n      }\n  > {\n    this._debug('#__loadSession()', 'begin')\n\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack)\n    }\n\n    try {\n      let currentSession: Session | null = null\n\n      const maybeSession = await getItemAsync(this.storage, this.storageKey)\n\n      this._debug('#getSession()', 'session from storage', maybeSession)\n\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid')\n          await this._removeSession()\n        }\n      }\n\n      if (!currentSession) {\n        return { data: { session: null }, error: null }\n      }\n\n      // A session is considered expired before the access token _actually_\n      // expires. When the autoRefreshToken option is off (or when the tab is\n      // in the background), very eager users of getSession() -- like\n      // realtime-js -- might send a valid JWT which will expire by the time it\n      // reaches the server.\n      const hasExpired = currentSession.expires_at\n        ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS\n        : false\n\n      this._debug(\n        '#__loadSession()',\n        `session has${hasExpired ? '' : ' not'} expired`,\n        'expires_at',\n        currentSession.expires_at\n      )\n\n      if (!hasExpired) {\n        if (this.userStorage) {\n          const maybeUser: { user?: User | null } | null = (await getItemAsync(\n            this.userStorage,\n            this.storageKey + '-user'\n          )) as any\n\n          if (maybeUser?.user) {\n            currentSession.user = maybeUser.user\n          } else {\n            currentSession.user = userNotAvailableProxy()\n          }\n        }\n\n        if (this.storage.isServer && currentSession.user) {\n          let suppressWarning = this.suppressGetSessionWarning\n          const proxySession: Session = new Proxy(currentSession, {\n            get: (target: any, prop: string, receiver: any) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn(\n                  'Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.'\n                )\n                suppressWarning = true // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver)\n            },\n          })\n          currentSession = proxySession\n        }\n\n        return { data: { session: currentSession }, error: null }\n      }\n\n      const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n      if (error) {\n        return { data: { session: null }, error }\n      }\n\n      return { data: { session }, error: null }\n    } finally {\n      this._debug('#__loadSession()', 'end')\n    }\n  }\n\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt?: string): Promise<UserResponse> {\n    if (jwt) {\n      return await this._getUser(jwt)\n    }\n\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser()\n    })\n\n    return result\n  }\n\n  private async _getUser(jwt?: string): Promise<UserResponse> {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse,\n        })\n      }\n\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n\n        // returns an error if there is no access_token or custom authorization header\n        if (!data.session?.access_token && !this.hasCustomAuthorizationHeader) {\n          return { data: { user: null }, error: new AuthSessionMissingError() }\n        }\n\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n          xform: _userResponse,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n\n          await this._removeSession()\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n        }\n\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options)\n    })\n  }\n\n  protected async _updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          throw sessionError\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError()\n        }\n        const session: Session = sessionData.session\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n\n        const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            ...attributes,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          jwt: session.access_token,\n          xform: _userResponse,\n        })\n        if (userError) throw userError\n        session.user = data.user as User\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('USER_UPDATED', session)\n        return { data: { user: session.user }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession)\n    })\n  }\n\n  protected async _setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError()\n      }\n\n      const timeNow = Date.now() / 1000\n      let expiresAt = timeNow\n      let hasExpired = true\n      let session: Session | null = null\n      const { payload } = decodeJWT(currentSession.access_token)\n      if (payload.exp) {\n        expiresAt = payload.exp\n        hasExpired = expiresAt <= timeNow\n      }\n\n      if (hasExpired) {\n        const { session: refreshedSession, error } = await this._callRefreshToken(\n          currentSession.refresh_token\n        )\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!refreshedSession) {\n          return { data: { user: null, session: null }, error: null }\n        }\n        session = refreshedSession\n      } else {\n        const { data, error } = await this._getUser(currentSession.access_token)\n        if (error) {\n          throw error\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt,\n        }\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user: session.user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession?: { refresh_token: string }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession)\n    })\n  }\n\n  protected async _refreshSession(currentSession?: {\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        if (!currentSession) {\n          const { data, error } = result\n          if (error) {\n            throw error\n          }\n\n          currentSession = data.session ?? undefined\n        }\n\n        if (!currentSession?.refresh_token) {\n          throw new AuthSessionMissingError()\n        }\n\n        const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!session) {\n          return { data: { user: null, session: null }, error: null }\n        }\n\n        return { data: { user: session.user, session }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets the session data from a URL string\n   */\n  private async _getSessionFromURL(\n    params: { [parameter: string]: string },\n    callbackUrlType: string\n  ): Promise<\n    | {\n        data: { session: Session; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; redirectType: null }; error: AuthError }\n  > {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.')\n\n      // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n      if (params.error || params.error_description || params.error_code) {\n        // The error class returned implies that the redirect is from an implicit grant flow\n        // but it could also be from a redirect error from a PKCE flow.\n        throw new AuthImplicitGrantRedirectError(\n          params.error_description || 'Error in URL with unspecified error_description',\n          {\n            error: params.error || 'unspecified_error',\n            code: params.error_code || 'unspecified_code',\n          }\n        )\n      }\n\n      // Checks for mismatches between the flowType initialised in the client and the URL parameters\n      switch (callbackUrlType) {\n        case 'implicit':\n          if (this.flowType === 'pkce') {\n            throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.')\n          }\n          break\n        case 'pkce':\n          if (this.flowType === 'implicit') {\n            throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.')\n          }\n          break\n        default:\n        // there's no mismatch so we continue\n      }\n\n      // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n      if (callbackUrlType === 'pkce') {\n        this._debug('#_initialize()', 'begin', 'is PKCE flow', true)\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.')\n        const { data, error } = await this._exchangeCodeForSession(params.code)\n        if (error) throw error\n\n        const url = new URL(window.location.href)\n        url.searchParams.delete('code')\n\n        window.history.replaceState(window.history.state, '', url.toString())\n\n        return { data: { session: data.session, redirectType: null }, error: null }\n      }\n\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type,\n      } = params\n\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL')\n      }\n\n      const timeNow = Math.round(Date.now() / 1000)\n      const expiresIn = parseInt(expires_in)\n      let expiresAt = timeNow + expiresIn\n\n      if (expires_at) {\n        expiresAt = parseInt(expires_at)\n      }\n\n      const actuallyExpiresIn = expiresAt - timeNow\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n        console.warn(\n          `@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`\n        )\n      }\n\n      const issuedAt = expiresAt - expiresIn\n      if (timeNow - issuedAt >= 120) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      } else if (timeNow - issuedAt < 0) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      }\n\n      const { data, error } = await this._getUser(access_token)\n      if (error) throw error\n\n      const session: Session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user,\n      }\n\n      // Remove tokens from URL\n      window.location.hash = ''\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash')\n\n      return { data: { session, redirectType: params.type }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  private _isImplicitGrantCallback(params: { [parameter: string]: string }): boolean {\n    return Boolean(params.access_token || params.error_description)\n  }\n\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  private async _isPKCECallback(params: { [parameter: string]: string }): Promise<boolean> {\n    const currentStorageContent = await getItemAsync(\n      this.storage,\n      `${this.storageKey}-code-verifier`\n    )\n\n    return !!(params.code && currentStorageContent)\n  }\n\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options: SignOut = { scope: 'global' }): Promise<{ error: AuthError | null }> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options)\n    })\n  }\n\n  protected async _signOut(\n    { scope }: SignOut = { scope: 'global' }\n  ): Promise<{ error: AuthError | null }> {\n    return await this._useSession(async (result) => {\n      const { data, error: sessionError } = result\n      if (sessionError) {\n        return { error: sessionError }\n      }\n      const accessToken = data.session?.access_token\n      if (accessToken) {\n        const { error } = await this.admin.signOut(accessToken, scope)\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (\n            !(\n              isAuthApiError(error) &&\n              (error.status === 404 || error.status === 401 || error.status === 403)\n            )\n          ) {\n            return { error }\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession()\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      }\n      return { error: null }\n    })\n  }\n\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(\n    callback: (event: AuthChangeEvent, session: Session | null) => void | Promise<void>\n  ): {\n    data: { subscription: Subscription }\n  } {\n    const id: string = uuid()\n    const subscription: Subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id)\n\n        this.stateChangeEmitters.delete(id)\n      },\n    }\n\n    this._debug('#onAuthStateChange()', 'registered callback with id', id)\n\n    this.stateChangeEmitters.set(id, subscription)\n    ;(async () => {\n      await this.initializePromise\n\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id)\n      })\n    })()\n\n    return { data: { subscription } }\n  }\n\n  private async _emitInitialSession(id: string): Promise<void> {\n    return await this._useSession(async (result) => {\n      try {\n        const {\n          data: { session },\n          error,\n        } = result\n        if (error) throw error\n\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', session)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session)\n      } catch (err) {\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', null)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err)\n        console.error(err)\n      }\n    })\n  }\n\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(\n    email: string,\n    options: {\n      redirectTo?: string\n      captchaToken?: string\n    } = {}\n  ): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    let codeChallenge: string | null = null\n    let codeChallengeMethod: string | null = null\n\n    if (this.flowType === 'pkce') {\n      ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey,\n        true // isPasswordRecovery\n      )\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: { captcha_token: options.captchaToken },\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities(): Promise<\n    | {\n        data: {\n          identities: UserIdentity[]\n        }\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      const { data, error } = await this.getUser()\n      if (error) throw error\n      return { data: { identities: data.user.identities ?? [] }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    try {\n      const { data, error } = await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) throw error\n        const url: string = await this._getUrlForProvider(\n          `${this.url}/user/identities/authorize`,\n          credentials.provider,\n          {\n            redirectTo: credentials.options?.redirectTo,\n            scopes: credentials.options?.scopes,\n            queryParams: credentials.options?.queryParams,\n            skipBrowserRedirect: true,\n          }\n        )\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n        })\n      })\n      if (error) throw error\n      if (isBrowser() && !credentials.options?.skipBrowserRedirect) {\n        window.location.assign(data?.url)\n      }\n      return { data: { provider: credentials.provider, url: data?.url }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { provider: credentials.provider, url: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity: UserIdentity): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n        return await _request(\n          this.fetch,\n          'DELETE',\n          `${this.url}/user/identities/${identity.identity_id}`,\n          {\n            headers: this.headers,\n            jwt: data.session?.access_token ?? undefined,\n          }\n        )\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  private async _refreshAccessToken(refreshToken: string): Promise<AuthResponse> {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`\n    this._debug(debugName, 'begin')\n\n    try {\n      const startedAt = Date.now()\n\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(\n        async (attempt) => {\n          if (attempt > 0) {\n            await sleep(200 * Math.pow(2, attempt - 1)) // 200, 400, 800, ...\n          }\n\n          this._debug(debugName, 'refreshing attempt', attempt)\n\n          return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n            body: { refresh_token: refreshToken },\n            headers: this.headers,\n            xform: _sessionResponse,\n          })\n        },\n        (attempt, error) => {\n          const nextBackOffInterval = 200 * Math.pow(2, attempt)\n          return (\n            error &&\n            isAuthRetryableFetchError(error) &&\n            // retryable only if the request can be sent before the backoff overflows the tick duration\n            Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS\n          )\n        }\n      )\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n      throw error\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private _isValidSession(maybeSession: unknown): maybeSession is Session {\n    const isValidSession =\n      typeof maybeSession === 'object' &&\n      maybeSession !== null &&\n      'access_token' in maybeSession &&\n      'refresh_token' in maybeSession &&\n      'expires_at' in maybeSession\n\n    return isValidSession\n  }\n\n  private async _handleProviderSignIn(\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const url: string = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams,\n    })\n\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url)\n\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url)\n    }\n\n    return { data: { provider, url }, error: null }\n  }\n\n  /**\n   * Recovers the session from LocalStorage and refreshes the token\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  private async _recoverAndRefresh() {\n    const debugName = '#_recoverAndRefresh()'\n    this._debug(debugName, 'begin')\n\n    try {\n      const currentSession = (await getItemAsync(this.storage, this.storageKey)) as Session | null\n\n      if (currentSession && this.userStorage) {\n        let maybeUser: { user: User | null } | null = (await getItemAsync(\n          this.userStorage,\n          this.storageKey + '-user'\n        )) as any\n\n        if (!this.storage.isServer && Object.is(this.storage, this.userStorage) && !maybeUser) {\n          // storage and userStorage are the same storage medium, for example\n          // window.localStorage if userStorage does not have the user from\n          // storage stored, store it first thereby migrating the user object\n          // from storage -> userStorage\n\n          maybeUser = { user: currentSession.user }\n          await setItemAsync(this.userStorage, this.storageKey + '-user', maybeUser)\n        }\n\n        currentSession.user = maybeUser?.user ?? userNotAvailableProxy()\n      } else if (currentSession && !currentSession.user) {\n        // user storage is not set, let's check if it was previously enabled so\n        // we bring back the storage as it should be\n\n        if (!currentSession.user) {\n          // test if userStorage was previously enabled and the storage medium was the same, to move the user back under the same key\n          const separateUser: { user: User | null } | null = (await getItemAsync(\n            this.storage,\n            this.storageKey + '-user'\n          )) as any\n\n          if (separateUser && separateUser?.user) {\n            currentSession.user = separateUser.user\n\n            await removeItemAsync(this.storage, this.storageKey + '-user')\n            await setItemAsync(this.storage, this.storageKey, currentSession)\n          } else {\n            currentSession.user = userNotAvailableProxy()\n          }\n        }\n      }\n\n      this._debug(debugName, 'session from storage', currentSession)\n\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid')\n        if (currentSession !== null) {\n          await this._removeSession()\n        }\n\n        return\n      }\n\n      const expiresWithMargin =\n        (currentSession.expires_at ?? Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS\n\n      this._debug(\n        debugName,\n        `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`\n      )\n\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const { error } = await this._callRefreshToken(currentSession.refresh_token)\n\n          if (error) {\n            console.error(error)\n\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(\n                debugName,\n                'refresh failed with a non-retryable error, removing the session',\n                error\n              )\n              await this._removeSession()\n            }\n          }\n        }\n      } else if (\n        currentSession.user &&\n        (currentSession.user as any).__isUserNotAvailableProxy === true\n      ) {\n        // If we have a proxy user, try to get the real user data\n        try {\n          const { data, error: userError } = await this._getUser(currentSession.access_token)\n\n          if (!userError && data?.user) {\n            currentSession.user = data.user\n            await this._saveSession(currentSession)\n            await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n          } else {\n            this._debug(debugName, 'could not get user data, skipping SIGNED_IN notification')\n          }\n        } catch (getUserError) {\n          console.error('Error getting user data:', getUserError)\n          this._debug(\n            debugName,\n            'error getting user data, skipping SIGNED_IN notification',\n            getUserError\n          )\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err)\n\n      console.error(err)\n      return\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _callRefreshToken(refreshToken: string): Promise<CallRefreshTokenResult> {\n    if (!refreshToken) {\n      throw new AuthSessionMissingError()\n    }\n\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise\n    }\n\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`\n\n    this._debug(debugName, 'begin')\n\n    try {\n      this.refreshingDeferred = new Deferred<CallRefreshTokenResult>()\n\n      const { data, error } = await this._refreshAccessToken(refreshToken)\n      if (error) throw error\n      if (!data.session) throw new AuthSessionMissingError()\n\n      await this._saveSession(data.session)\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session)\n\n      const result = { session: data.session, error: null }\n\n      this.refreshingDeferred.resolve(result)\n\n      return result\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        const result = { session: null, error }\n\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession()\n        }\n\n        this.refreshingDeferred?.resolve(result)\n\n        return result\n      }\n\n      this.refreshingDeferred?.reject(error)\n      throw error\n    } finally {\n      this.refreshingDeferred = null\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _notifyAllSubscribers(\n    event: AuthChangeEvent,\n    session: Session | null,\n    broadcast = true\n  ) {\n    const debugName = `#_notifyAllSubscribers(${event})`\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`)\n\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({ event, session })\n      }\n\n      const errors: any[] = []\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n        try {\n          await x.callback(event, session)\n        } catch (e: any) {\n          errors.push(e)\n        }\n      })\n\n      await Promise.all(promises)\n\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i])\n        }\n\n        throw errors[0]\n      }\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  private async _saveSession(session: Session) {\n    this._debug('#_saveSession()', session)\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true\n\n    // Create a shallow copy to work with, to avoid mutating the original session object if it's used elsewhere\n    const sessionToProcess = { ...session }\n\n    const userIsProxy =\n      sessionToProcess.user && (sessionToProcess.user as any).__isUserNotAvailableProxy === true\n    if (this.userStorage) {\n      if (!userIsProxy && sessionToProcess.user) {\n        // If it's a real user object, save it to userStorage.\n        await setItemAsync(this.userStorage, this.storageKey + '-user', {\n          user: sessionToProcess.user,\n        })\n      } else if (userIsProxy) {\n        // If it's the proxy, it means user was not found in userStorage.\n        // We should ensure no stale user data for this key exists in userStorage if we were to save null,\n        // or simply not save the proxy. For now, we don't save the proxy here.\n        // If there's a need to clear userStorage if user becomes proxy, that logic would go here.\n      }\n\n      // Prepare the main session data for primary storage: remove the user property before cloning\n      // This is important because the original session.user might be the proxy\n      const mainSessionData: Omit<Session, 'user'> & { user?: User } = { ...sessionToProcess }\n      delete mainSessionData.user // Remove user (real or proxy) before cloning for main storage\n\n      const clonedMainSessionData = structuredClone(mainSessionData)\n      await setItemAsync(this.storage, this.storageKey, clonedMainSessionData)\n    } else {\n      // No userStorage is configured.\n      // In this case, session.user should ideally not be a proxy.\n      // If it were, structuredClone would fail. This implies an issue elsewhere if user is a proxy here\n      const clonedSession = structuredClone(sessionToProcess) // sessionToProcess still has its original user property\n      await setItemAsync(this.storage, this.storageKey, clonedSession)\n    }\n  }\n\n  private async _removeSession() {\n    this._debug('#_removeSession()')\n\n    await removeItemAsync(this.storage, this.storageKey)\n    await removeItemAsync(this.storage, this.storageKey + '-code-verifier')\n    await removeItemAsync(this.storage, this.storageKey + '-user')\n\n    if (this.userStorage) {\n      await removeItemAsync(this.userStorage, this.storageKey + '-user')\n    }\n\n    await this._notifyAllSubscribers('SIGNED_OUT', null)\n  }\n\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  private _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()')\n\n    const callback = this.visibilityChangedCallback\n    this.visibilityChangedCallback = null\n\n    try {\n      if (callback && isBrowser() && window?.removeEventListener) {\n        window.removeEventListener('visibilitychange', callback)\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e)\n    }\n  }\n\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _startAutoRefresh() {\n    await this._stopAutoRefresh()\n\n    this._debug('#_startAutoRefresh()')\n\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS)\n    this.autoRefreshTicker = ticker\n\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref()\n      // @ts-expect-error TS has no context of Deno\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-expect-error TS has no context of Deno\n      Deno.unrefTimer(ticker)\n    }\n\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise\n      await this._autoRefreshTokenTick()\n    }, 0)\n  }\n\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()')\n\n    const ticker = this.autoRefreshTicker\n    this.autoRefreshTicker = null\n\n    if (ticker) {\n      clearInterval(ticker)\n    }\n  }\n\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._startAutoRefresh()\n  }\n\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._stopAutoRefresh()\n  }\n\n  /**\n   * Runs the auto refresh token tick.\n   */\n  private async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin')\n\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now()\n\n          try {\n            return await this._useSession(async (result) => {\n              const {\n                data: { session },\n              } = result\n\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session')\n                return\n              }\n\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor(\n                (session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS\n              )\n\n              this._debug(\n                '#_autoRefreshTokenTick()',\n                `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`\n              )\n\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token)\n              }\n            })\n          } catch (e: any) {\n            console.error(\n              'Auto refresh tick failed with error. This is likely a transient error.',\n              e\n            )\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end')\n        }\n      })\n    } catch (e: any) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available')\n      } else {\n        throw e\n      }\n    }\n  }\n\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  private async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()')\n\n    if (!isBrowser() || !window?.addEventListener) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh()\n      }\n\n      return false\n    }\n\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false)\n\n      window?.addEventListener('visibilitychange', this.visibilityChangedCallback)\n\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true) // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error)\n    }\n  }\n\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  private async _onVisibilityChanged(calledFromInitialize: boolean) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`\n    this._debug(methodName, 'visibilityState', document.visibilityState)\n\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh()\n      }\n\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise\n\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(\n              methodName,\n              'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting'\n            )\n\n            // visibility has changed while waiting for the lock, abort\n            return\n          }\n\n          // recover the session\n          await this._recoverAndRefresh()\n        })\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh()\n      }\n    }\n  }\n\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  private async _getUrlForProvider(\n    url: string,\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const urlParams: string[] = [`provider=${encodeURIComponent(provider)}`]\n    if (options?.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`)\n    }\n    if (options?.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`)\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey\n      )\n\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n      })\n      urlParams.push(flowParams.toString())\n    }\n    if (options?.queryParams) {\n      const query = new URLSearchParams(options.queryParams)\n      urlParams.push(query.toString())\n    }\n    if (options?.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`)\n    }\n\n    return `${url}?${urlParams.join('&')}`\n  }\n\n  private async _unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  private async _enroll(params: MFAEnrollTOTPParams): Promise<AuthMFAEnrollTOTPResponse>\n  private async _enroll(params: MFAEnrollPhoneParams): Promise<AuthMFAEnrollPhoneResponse>\n  private async _enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        const body = {\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType,\n          ...(params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }),\n        }\n\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n\n        if (error) {\n          return { data: null, error }\n        }\n\n        if (params.factorType === 'totp' && data?.totp?.qr_code) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`\n        }\n\n        return { data, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  private async _verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          const { data, error } = await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/verify`,\n            {\n              body: { code: params.code, challenge_id: params.challengeId },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n          if (error) {\n            return { data: null, error }\n          }\n\n          await this._saveSession({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in,\n            ...data,\n          })\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data)\n\n          return { data, error }\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  private async _challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          return await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/challenge`,\n            {\n              body: { channel: params.channel },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  private async _challengeAndVerify(\n    params: MFAChallengeAndVerifyParams\n  ): Promise<AuthMFAVerifyResponse> {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n\n    const { data: challengeData, error: challengeError } = await this._challenge({\n      factorId: params.factorId,\n    })\n    if (challengeError) {\n      return { data: null, error: challengeError }\n    }\n\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code,\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  private async _listFactors(): Promise<AuthMFAListFactorsResponse> {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: { user },\n      error: userError,\n    } = await this.getUser()\n    if (userError) {\n      return { data: null, error: userError }\n    }\n\n    const factors = user?.factors || []\n    const totp = factors.filter(\n      (factor) => factor.factor_type === 'totp' && factor.status === 'verified'\n    )\n    const phone = factors.filter(\n      (factor) => factor.factor_type === 'phone' && factor.status === 'verified'\n    )\n\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone,\n      },\n      error: null,\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  private async _getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse> {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n        if (!session) {\n          return {\n            data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n            error: null,\n          }\n        }\n\n        const { payload } = decodeJWT(session.access_token)\n\n        let currentLevel: AuthenticatorAssuranceLevels | null = null\n\n        if (payload.aal) {\n          currentLevel = payload.aal\n        }\n\n        let nextLevel: AuthenticatorAssuranceLevels | null = currentLevel\n\n        const verifiedFactors =\n          session.user.factors?.filter((factor: Factor) => factor.status === 'verified') ?? []\n\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2'\n        }\n\n        const currentAuthenticationMethods = payload.amr || []\n\n        return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null }\n      })\n    })\n  }\n\n  private async fetchJwk(kid: string, jwks: { keys: JWK[] } = { keys: [] }): Promise<JWK | null> {\n    // try fetching from the supplied jwks\n    let jwk = jwks.keys.find((key) => key.kid === kid)\n    if (jwk) {\n      return jwk\n    }\n\n    const now = Date.now()\n\n    // try fetching from cache\n    jwk = this.jwks.keys.find((key) => key.kid === kid)\n\n    // jwk exists and jwks isn't stale\n    if (jwk && this.jwks_cached_at + JWKS_TTL > now) {\n      return jwk\n    }\n    // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n    const { data, error } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n      headers: this.headers,\n    })\n    if (error) {\n      throw error\n    }\n    if (!data.keys || data.keys.length === 0) {\n      return null\n    }\n\n    this.jwks = data\n    this.jwks_cached_at = now\n\n    // Find the signing key\n    jwk = data.keys.find((key: any) => key.kid === kid)\n    if (!jwk) {\n      return null\n    }\n    return jwk\n  }\n\n  /**\n   * Extracts the JWT claims present in the access token by first verifying the\n   * JWT against the server's JSON Web Key Set endpoint\n   * `/.well-known/jwks.json` which is often cached, resulting in significantly\n   * faster responses. Prefer this method over {@link #getUser} which always\n   * sends a request to the Auth server for each JWT.\n   *\n   * If the project is not using an asymmetric JWT signing key (like ECC or\n   * RSA) it always sends a request to the Auth server (similar to {@link\n   * #getUser}) to verify the JWT.\n   *\n   * @param jwt An optional specific JWT you wish to verify, not the one you\n   *            can obtain from {@link #getSession}.\n   * @param options Various additional options that allow you to customize the\n   *                behavior of this method.\n   */\n  async getClaims(\n    jwt?: string,\n    options: {\n      /**\n       * @deprecated Please use options.jwks instead.\n       */\n      keys?: JWK[]\n\n      /** If set to `true` the `exp` claim will not be validated against the current time. */\n      allowExpired?: boolean\n\n      /** If set, this JSON Web Key Set is going to have precedence over the cached value available on the server. */\n      jwks?: { keys: JWK[] }\n    } = {}\n  ): Promise<\n    | {\n        data: { claims: JwtPayload; header: JwtHeader; signature: Uint8Array }\n        error: null\n      }\n    | { data: null; error: AuthError }\n    | { data: null; error: null }\n  > {\n    try {\n      let token = jwt\n      if (!token) {\n        const { data, error } = await this.getSession()\n        if (error || !data.session) {\n          return { data: null, error }\n        }\n        token = data.session.access_token\n      }\n\n      const {\n        header,\n        payload,\n        signature,\n        raw: { header: rawHeader, payload: rawPayload },\n      } = decodeJWT(token)\n\n      if (!options?.allowExpired) {\n        // Reject expired JWTs should only happen if jwt argument was passed\n        validateExp(payload.exp)\n      }\n\n      const signingKey =\n        !header.alg ||\n        header.alg.startsWith('HS') ||\n        !header.kid ||\n        !('crypto' in globalThis && 'subtle' in globalThis.crypto)\n          ? null\n          : await this.fetchJwk(header.kid, options?.keys ? { keys: options.keys } : options?.jwks)\n\n      // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n      if (!signingKey) {\n        const { error } = await this.getUser(token)\n        if (error) {\n          throw error\n        }\n        // getUser succeeds so the claims in the JWT can be trusted\n        return {\n          data: {\n            claims: payload,\n            header,\n            signature,\n          },\n          error: null,\n        }\n      }\n\n      const algorithm = getAlgorithm(header.alg)\n\n      // Convert JWK to CryptoKey\n      const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n        'verify',\n      ])\n\n      // Verify the signature\n      const isValid = await crypto.subtle.verify(\n        algorithm,\n        publicKey,\n        signature,\n        stringToUint8Array(`${rawHeader}.${rawPayload}`)\n      )\n\n      if (!isValid) {\n        throw new AuthInvalidJwtError('Invalid JWT signature')\n      }\n\n      // If verification succeeds, decode and return claims\n      return {\n        data: {\n          claims: payload,\n          header,\n          signature,\n        },\n        error: null,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,SACEC,eAAe,EACfC,gBAAgB,EAChBC,6BAA6B,EAC7BC,2BAA2B,EAC3BC,UAAU,EACVC,WAAW,EACXC,QAAQ,QACH,iBAAiB;AACxB,SAEEC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,uBAAuB,EACvBC,6BAA6B,EAC7BC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,yBAAyB,EACzBC,yBAAyB,EACzBC,gCAAgC,EAChCC,mBAAmB,QACd,cAAc;AACrB,SAEEC,QAAQ,EACRC,gBAAgB,EAChBC,wBAAwB,EACxBC,aAAa,EACbC,YAAY,QACP,aAAa;AACpB,SACEC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,sBAAsB,EACtBC,yBAAyB,EACzBC,YAAY,EACZC,WAAW,EACXC,SAAS,EACTC,qBAAqB,EACrBC,oBAAoB,QACf,eAAe;AACtB,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,uBAAuB,EAAEC,aAAa,QAAQ,aAAa;AA2DpE,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,iBAAiB;AAEtEL,kBAAkB,EAAE,EAAC;AAErB,MAAMM,eAAe,GAGjB;EACFC,GAAG,EAAE5C,UAAU;EACf6C,UAAU,EAAE5C,WAAW;EACvB6C,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAErD,eAAe;EACxBsD,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,KAAK;EACZC,4BAA4B,EAAE;CAC/B;AAED,eAAeC,QAAQA,CAAIC,IAAY,EAAEC,cAAsB,EAAEC,EAAoB;EACnF,OAAO,MAAMA,EAAE,EAAE;AACnB;AAEA;;;;;;;;AAQA,MAAMC,WAAW,GAA0E,EAAE;AAE7F,eAAc,MAAOC,YAAY;EA+E/B;;;EAGAC,YAAYC,OAA4B;;IAvCxC;;;IAGU,KAAAC,WAAW,GAA4B,IAAI;IAC3C,KAAAC,aAAa,GAAqC,IAAI;IACtD,KAAAC,mBAAmB,GAA8B,IAAIC,GAAG,EAAE;IAC1D,KAAAC,iBAAiB,GAA0C,IAAI;IAC/D,KAAAC,yBAAyB,GAAgC,IAAI;IAC7D,KAAAC,kBAAkB,GAA4C,IAAI;IAC5E;;;;;;IAMU,KAAAC,iBAAiB,GAAqC,IAAI;IAC1D,KAAApB,kBAAkB,GAAG,IAAI;IAKzB,KAAAI,4BAA4B,GAAG,KAAK;IACpC,KAAAiB,yBAAyB,GAAG,KAAK;IAGjC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAmB,EAAE;IAE5C;;;IAGU,KAAAC,gBAAgB,GAA4B,IAAI;IAGhD,KAAAC,MAAM,GAA8CC,OAAO,CAACC,GAAG;IAMvE,IAAI,CAACC,UAAU,GAAGlB,YAAY,CAACmB,cAAc;IAC7CnB,YAAY,CAACmB,cAAc,IAAI,CAAC;IAEhC,IAAI,IAAI,CAACD,UAAU,GAAG,CAAC,IAAItD,SAAS,EAAE,EAAE;MACtCoD,OAAO,CAACI,IAAI,CACV,8MAA8M,CAC/M;;IAGH,MAAMC,QAAQ,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQtC,eAAe,GAAKiB,OAAO,CAAE;IAEnD,IAAI,CAACsB,gBAAgB,GAAG,CAAC,CAACH,QAAQ,CAAC5B,KAAK;IACxC,IAAI,OAAO4B,QAAQ,CAAC5B,KAAK,KAAK,UAAU,EAAE;MACxC,IAAI,CAACsB,MAAM,GAAGM,QAAQ,CAAC5B,KAAK;;IAG9B,IAAI,CAACJ,cAAc,GAAGgC,QAAQ,CAAChC,cAAc;IAC7C,IAAI,CAACF,UAAU,GAAGkC,QAAQ,CAAClC,UAAU;IACrC,IAAI,CAACC,gBAAgB,GAAGiC,QAAQ,CAACjC,gBAAgB;IACjD,IAAI,CAACqC,KAAK,GAAG,IAAIxF,cAAc,CAAC;MAC9BiD,GAAG,EAAEmC,QAAQ,CAACnC,GAAG;MACjBK,OAAO,EAAE8B,QAAQ,CAAC9B,OAAO;MACzBmC,KAAK,EAAEL,QAAQ,CAACK;KACjB,CAAC;IAEF,IAAI,CAACxC,GAAG,GAAGmC,QAAQ,CAACnC,GAAG;IACvB,IAAI,CAACK,OAAO,GAAG8B,QAAQ,CAAC9B,OAAO;IAC/B,IAAI,CAACmC,KAAK,GAAG5D,YAAY,CAACuD,QAAQ,CAACK,KAAK,CAAC;IACzC,IAAI,CAACC,IAAI,GAAGN,QAAQ,CAACM,IAAI,IAAIhC,QAAQ;IACrC,IAAI,CAACL,kBAAkB,GAAG+B,QAAQ,CAAC/B,kBAAkB;IACrD,IAAI,CAACE,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ;IACjC,IAAI,CAACE,4BAA4B,GAAG2B,QAAQ,CAAC3B,4BAA4B;IAEzE,IAAI2B,QAAQ,CAACM,IAAI,EAAE;MACjB,IAAI,CAACA,IAAI,GAAGN,QAAQ,CAACM,IAAI;KAC1B,MAAM,IAAI/D,SAAS,EAAE,KAAI,CAAAgE,EAAA,GAAAC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,SAAS,cAAAF,EAAA,uBAAAA,EAAA,CAAEG,KAAK,GAAE;MACtD,IAAI,CAACJ,IAAI,GAAG7C,aAAa;KAC1B,MAAM;MACL,IAAI,CAAC6C,IAAI,GAAGhC,QAAQ;;IAGtB,IAAI,CAAC,IAAI,CAACqC,IAAI,EAAE;MACd,IAAI,CAACA,IAAI,GAAG;QAAEC,IAAI,EAAE;MAAE,CAAE;MACxB,IAAI,CAACC,cAAc,GAAGC,MAAM,CAACC,gBAAgB;;IAG/C,IAAI,CAACC,GAAG,GAAG;MACTC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC;MAC/BG,QAAQ,EAAE,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,IAAI,CAAC;MACnCK,SAAS,EAAE,IAAI,CAACC,UAAU,CAACN,IAAI,CAAC,IAAI,CAAC;MACrCO,WAAW,EAAE,IAAI,CAACC,YAAY,CAACR,IAAI,CAAC,IAAI,CAAC;MACzCS,kBAAkB,EAAE,IAAI,CAACC,mBAAmB,CAACV,IAAI,CAAC,IAAI,CAAC;MACvDW,8BAA8B,EAAE,IAAI,CAACC,+BAA+B,CAACZ,IAAI,CAAC,IAAI;KAC/E;IAED,IAAI,IAAI,CAACnD,cAAc,EAAE;MACvB,IAAIgC,QAAQ,CAACgC,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGhC,QAAQ,CAACgC,OAAO;OAChC,MAAM;QACL,IAAI5E,oBAAoB,EAAE,EAAE;UAC1B,IAAI,CAAC4E,OAAO,GAAGxB,UAAU,CAACyB,YAAY;SACvC,MAAM;UACL,IAAI,CAAClD,aAAa,GAAG,EAAE;UACvB,IAAI,CAACiD,OAAO,GAAG3E,yBAAyB,CAAC,IAAI,CAAC0B,aAAa,CAAC;;;MAIhE,IAAIiB,QAAQ,CAAClB,WAAW,EAAE;QACxB,IAAI,CAACA,WAAW,GAAGkB,QAAQ,CAAClB,WAAW;;KAE1C,MAAM;MACL,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACiD,OAAO,GAAG3E,yBAAyB,CAAC,IAAI,CAAC0B,aAAa,CAAC;;IAG9D,IAAIxC,SAAS,EAAE,IAAIiE,UAAU,CAAC0B,gBAAgB,IAAI,IAAI,CAAClE,cAAc,IAAI,IAAI,CAACF,UAAU,EAAE;MACxF,IAAI;QACF,IAAI,CAAC2B,gBAAgB,GAAG,IAAIe,UAAU,CAAC0B,gBAAgB,CAAC,IAAI,CAACpE,UAAU,CAAC;OACzE,CAAC,OAAOqE,CAAM,EAAE;QACfxC,OAAO,CAACyC,KAAK,CACX,wFAAwF,EACxFD,CAAC,CACF;;MAGH,CAAAE,EAAA,OAAI,CAAC5C,gBAAgB,cAAA4C,EAAA,uBAAAA,EAAA,CAAEC,gBAAgB,CAAC,SAAS,EAAE,MAAOC,KAAK,IAAI;QACjE,IAAI,CAACC,MAAM,CAAC,0DAA0D,EAAED,KAAK,CAAC;QAE9E,MAAM,IAAI,CAACE,qBAAqB,CAACF,KAAK,CAACG,IAAI,CAACH,KAAK,EAAEA,KAAK,CAACG,IAAI,CAACC,OAAO,EAAE,KAAK,CAAC,EAAC;MAChF,CAAC,CAAC;;IAGJ,IAAI,CAACC,UAAU,EAAE;EACnB;EA5JA;;;EAGA,IAAcjC,IAAIA,CAAA;;IAChB,OAAO,CAAA0B,EAAA,IAAA9B,EAAA,GAAA7B,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC,cAAAyC,EAAA,uBAAAA,EAAA,CAAEI,IAAI,cAAA0B,EAAA,cAAAA,EAAA,GAAI;MAAEzB,IAAI,EAAE;IAAE,CAAE;EAC3D;EAEA,IAAcD,IAAIA,CAACkC,KAAsB;IACvCnE,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC,GAAAmC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQxB,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC;MAAE6C,IAAI,EAAEkC;IAAK,EAAE;EACjF;EAEA,IAAchC,cAAcA,CAAA;;IAC1B,OAAO,CAAAwB,EAAA,IAAA9B,EAAA,GAAA7B,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC,cAAAyC,EAAA,uBAAAA,EAAA,CAAEuC,QAAQ,cAAAT,EAAA,cAAAA,EAAA,GAAIvB,MAAM,CAACC,gBAAgB;EAC1E;EAEA,IAAcF,cAAcA,CAACgC,KAAa;IACxCnE,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC,GAAAmC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQxB,WAAW,CAAC,IAAI,CAACZ,UAAU,CAAC;MAAEgF,QAAQ,EAAED;IAAK,EAAE;EACrF;EA6IQL,MAAMA,CAAA,EAAe;IAC3B,IAAI,IAAI,CAACrC,gBAAgB,EAAE;MAAA,SAAA4C,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADXC,IAAW,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAXF,IAAW,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAEzB,IAAI,CAAC1D,MAAM,CACT,gBAAgB,IAAI,CAACG,UAAU,KAAKtC,OAAO,KAAK,IAAI8F,IAAI,EAAE,CAACC,WAAW,EAAE,EAAE,EAC1E,GAAGJ,IAAI,CACR;;IAGH,OAAO,IAAI;EACb;EAEA;;;;;EAKA,MAAMN,UAAUA,CAAA;IACd,IAAI,IAAI,CAACvD,iBAAiB,EAAE;MAC1B,OAAO,MAAM,IAAI,CAACA,iBAAiB;;IAGrC,IAAI,CAACA,iBAAiB,GAAG,CAAC,YAAW;MACnC,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;QAC5C,OAAO,MAAM,IAAI,CAACC,WAAW,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,EAAC,CAAE;IAEJ,OAAO,MAAM,IAAI,CAACnE,iBAAiB;EACrC;EAEA;;;;;;EAMQ,MAAMmE,WAAWA,CAAA;;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG3G,sBAAsB,CAAC4G,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3D,IAAIC,eAAe,GAAG,MAAM;MAC5B,IAAI,IAAI,CAACC,wBAAwB,CAACL,MAAM,CAAC,EAAE;QACzCI,eAAe,GAAG,UAAU;OAC7B,MAAM,IAAI,MAAM,IAAI,CAACE,eAAe,CAACN,MAAM,CAAC,EAAE;QAC7CI,eAAe,GAAG,MAAM;;MAG1B;;;;;;MAMA,IAAItH,SAAS,EAAE,IAAI,IAAI,CAAC0B,kBAAkB,IAAI4F,eAAe,KAAK,MAAM,EAAE;QACxE,MAAM;UAAEnB,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC4B,kBAAkB,CAACP,MAAM,EAAEI,eAAe,CAAC;QAC9E,IAAIzB,KAAK,EAAE;UACT,IAAI,CAACI,MAAM,CAAC,gBAAgB,EAAE,kCAAkC,EAAEJ,KAAK,CAAC;UAExE,IAAItG,gCAAgC,CAACsG,KAAK,CAAC,EAAE;YAC3C,MAAM6B,SAAS,GAAG,CAAA1D,EAAA,GAAA6B,KAAK,CAAC8B,OAAO,cAAA3D,EAAA,uBAAAA,EAAA,CAAE4D,IAAI;YACrC,IACEF,SAAS,KAAK,yBAAyB,IACvCA,SAAS,KAAK,oBAAoB,IAClCA,SAAS,KAAK,+BAA+B,EAC7C;cACA,OAAO;gBAAE7B;cAAK,CAAE;;;UAIpB;UACA;UACA,MAAM,IAAI,CAACgC,cAAc,EAAE;UAE3B,OAAO;YAAEhC;UAAK,CAAE;;QAGlB,MAAM;UAAEO,OAAO;UAAE0B;QAAY,CAAE,GAAG3B,IAAI;QAEtC,IAAI,CAACF,MAAM,CACT,gBAAgB,EAChB,yBAAyB,EACzBG,OAAO,EACP,eAAe,EACf0B,YAAY,CACb;QAED,MAAM,IAAI,CAACC,YAAY,CAAC3B,OAAO,CAAC;QAEhC4B,UAAU,CAAC,YAAW;UACpB,IAAIF,YAAY,KAAK,UAAU,EAAE;YAC/B,MAAM,IAAI,CAAC5B,qBAAqB,CAAC,mBAAmB,EAAEE,OAAO,CAAC;WAC/D,MAAM;YACL,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;QAE1D,CAAC,EAAE,CAAC,CAAC;QAEL,OAAO;UAAEP,KAAK,EAAE;QAAI,CAAE;;MAExB;MACA,MAAM,IAAI,CAACoC,kBAAkB,EAAE;MAC/B,OAAO;QAAEpC,KAAK,EAAE;MAAI,CAAE;KACvB,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEA;QAAK,CAAE;;MAGlB,OAAO;QACLA,KAAK,EAAE,IAAI3G,gBAAgB,CAAC,wCAAwC,EAAE2G,KAAK;OAC5E;KACF,SAAS;MACR,MAAM,IAAI,CAACqC,uBAAuB,EAAE;MACpC,IAAI,CAACjC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC;;EAExC;EAEA;;;;;EAKA,MAAMkC,iBAAiBA,CAACC,WAA0C;;IAChE,IAAI;MACF,MAAMC,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,SAAS,EAAE;QACnEK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB2G,IAAI,EAAE;UACJnC,IAAI,EAAE,CAAAL,EAAA,IAAA9B,EAAA,GAAAoE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9F,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEmC,IAAI,cAAAL,EAAA,cAAAA,EAAA,GAAI,EAAE;UACtCyC,oBAAoB,EAAE;YAAEC,aAAa,EAAE,CAAAC,EAAA,GAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE9F,OAAO,cAAAmG,EAAA,uBAAAA,EAAA,CAAEC;UAAY;SAC1E;QACDC,KAAK,EAAEjJ;OACR,CAAC;MACF,MAAM;QAAEyG,IAAI;QAAEN;MAAK,CAAE,GAAGwC,GAAG;MAE3B,IAAIxC,KAAK,IAAI,CAACM,IAAI,EAAE;QAClB,OAAO;UAAEA,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAEA;QAAK,CAAE;;MAE9D,MAAMO,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMwC,IAAI,GAAgBzC,IAAI,CAACyC,IAAI;MAEnC,IAAIzC,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEyC,IAAI;UAAExC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;EAUA,MAAMgD,MAAMA,CAACT,WAA0C;;IACrD,IAAI;MACF,IAAIC,GAAiB;MACrB,IAAI,OAAO,IAAID,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAEC,QAAQ;UAAEzG;QAAO,CAAE,GAAG8F,WAAW;QAChD,IAAIY,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAACrH,QAAQ,KAAK,MAAM,EAAE;UAC5B;UAAC,CAACoH,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CACrE,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,CAChB;;QAEH8G,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,SAAS,EAAE;UAC7DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBuH,UAAU,EAAE5G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6G,eAAe;UACpCb,IAAI,EAAE;YACJQ,KAAK;YACLC,QAAQ;YACR5C,IAAI,EAAE,CAAAnC,EAAA,GAAA1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,IAAI,cAAAnC,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzBuE,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY,CAAE;YAC9DU,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;WACxB;UACDN,KAAK,EAAEjJ;SACR,CAAC;OACH,MAAM,IAAI,OAAO,IAAI0I,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAEP,QAAQ;UAAEzG;QAAO,CAAE,GAAG8F,WAAW;QAChDC,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,SAAS,EAAE;UAC7DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJgB,KAAK;YACLP,QAAQ;YACR5C,IAAI,EAAE,CAAAL,EAAA,GAAAxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,IAAI,cAAAL,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzByD,OAAO,EAAE,CAAAd,EAAA,GAAAnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiH,OAAO,cAAAd,EAAA,cAAAA,EAAA,GAAI,KAAK;YAClCF,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY;WAC7D;UACDC,KAAK,EAAEjJ;SACR,CAAC;OACH,MAAM;QACL,MAAM,IAAIX,2BAA2B,CACnC,iEAAiE,CAClE;;MAGH,MAAM;QAAEoH,IAAI;QAAEN;MAAK,CAAE,GAAGwC,GAAG;MAE3B,IAAIxC,KAAK,IAAI,CAACM,IAAI,EAAE;QAClB,OAAO;UAAEA,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAEA;QAAK,CAAE;;MAG9D,MAAMO,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMwC,IAAI,GAAgBzC,IAAI,CAACyC,IAAI;MAEnC,IAAIzC,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEyC,IAAI;UAAExC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;EAQA,MAAM2D,kBAAkBA,CACtBpB,WAA0C;IAE1C,IAAI;MACF,IAAIC,GAAyB;MAC7B,IAAI,OAAO,IAAID,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAEC,QAAQ;UAAEzG;QAAO,CAAE,GAAG8F,WAAW;QAChDC,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,4BAA4B,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJQ,KAAK;YACLC,QAAQ;YACRR,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY;WAC7D;UACDC,KAAK,EAAEhJ;SACR,CAAC;OACH,MAAM,IAAI,OAAO,IAAIyI,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAEP,QAAQ;UAAEzG;QAAO,CAAE,GAAG8F,WAAW;QAChDC,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,4BAA4B,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJgB,KAAK;YACLP,QAAQ;YACRR,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY;WAC7D;UACDC,KAAK,EAAEhJ;SACR,CAAC;OACH,MAAM;QACL,MAAM,IAAIZ,2BAA2B,CACnC,iEAAiE,CAClE;;MAEH,MAAM;QAAEoH,IAAI;QAAEN;MAAK,CAAE,GAAGwC,GAAG;MAE3B,IAAIxC,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACyC,IAAI,EAAE;QAC/C,OAAO;UAAEzC,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAE,IAAI5G,6BAA6B;QAAE,CAAE;;MAE5F,IAAIkH,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QACLD,IAAI,EAAAzC,MAAA,CAAAC,MAAA;UACFiF,IAAI,EAAEzC,IAAI,CAACyC,IAAI;UACfxC,OAAO,EAAED,IAAI,CAACC;QAAO,GACjBD,IAAI,CAACsD,aAAa,GAAG;UAAEC,YAAY,EAAEvD,IAAI,CAACsD;QAAa,CAAE,GAAG,IAAK,CACtE;QACD5D;OACD;KACF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAM8D,eAAeA,CAACvB,WAAuC;;IAC3D,OAAO,MAAM,IAAI,CAACwB,qBAAqB,CAACxB,WAAW,CAACyB,QAAQ,EAAE;MAC5DX,UAAU,EAAE,CAAAlF,EAAA,GAAAoE,WAAW,CAAC9F,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEkF,UAAU;MAC3CY,MAAM,EAAE,CAAAhE,EAAA,GAAAsC,WAAW,CAAC9F,OAAO,cAAAwD,EAAA,uBAAAA,EAAA,CAAEgE,MAAM;MACnCC,WAAW,EAAE,CAAAtB,EAAA,GAAAL,WAAW,CAAC9F,OAAO,cAAAmG,EAAA,uBAAAA,EAAA,CAAEsB,WAAW;MAC7CC,mBAAmB,EAAE,CAAAC,EAAA,GAAA7B,WAAW,CAAC9F,OAAO,cAAA2H,EAAA,uBAAAA,EAAA,CAAED;KAC3C,CAAC;EACJ;EAEA;;;EAGA,MAAME,sBAAsBA,CAACC,QAAgB;IAC3C,MAAM,IAAI,CAACrH,iBAAiB;IAE5B,OAAO,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,OAAO,IAAI,CAACoD,uBAAuB,CAACD,QAAQ,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEA;;;;EAIA,MAAME,cAAcA,CAACjC,WAA4B;IAO/C,MAAM;MAAEkC;IAAK,CAAE,GAAGlC,WAAW;IAE7B,IAAIkC,KAAK,KAAK,QAAQ,EAAE;MACtB,OAAO,MAAM,IAAI,CAACC,gBAAgB,CAACnC,WAAW,CAAC;;IAGjD,MAAM,IAAIoC,KAAK,CAAC,yCAAyCF,KAAK,GAAG,CAAC;EACpE;EAEQ,MAAMC,gBAAgBA,CAACnC,WAAkC;;IAC/D,IAAIqC,OAAe;IACnB,IAAIC,SAAqB;IAEzB,IAAI,SAAS,IAAItC,WAAW,EAAE;MAC5BqC,OAAO,GAAGrC,WAAW,CAACqC,OAAO;MAC7BC,SAAS,GAAGtC,WAAW,CAACsC,SAAS;KAClC,MAAM;MACL,MAAM;QAAEJ,KAAK;QAAEK,MAAM;QAAEC,SAAS;QAAEtI;MAAO,CAAE,GAAG8F,WAAW;MAEzD,IAAIyC,cAA4B;MAEhC,IAAI,CAAC7K,SAAS,EAAE,EAAE;QAChB,IAAI,OAAO2K,MAAM,KAAK,QAAQ,IAAI,EAACrI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,GAAG,GAAE;UAC/C,MAAM,IAAIkJ,KAAK,CACb,uFAAuF,CACxF;;QAGHK,cAAc,GAAGF,MAAM;OACxB,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACrCE,cAAc,GAAGF,MAAM;OACxB,MAAM;QACL,MAAMG,SAAS,GAAG3D,MAAa;QAE/B,IACE,QAAQ,IAAI2D,SAAS,IACrB,OAAOA,SAAS,CAACC,MAAM,KAAK,QAAQ,KAClC,QAAQ,IAAID,SAAS,CAACC,MAAM,IAAI,OAAOD,SAAS,CAACC,MAAM,CAACC,MAAM,KAAK,UAAU,IAC5E,aAAa,IAAIF,SAAS,CAACC,MAAM,IAChC,OAAOD,SAAS,CAACC,MAAM,CAACE,WAAW,KAAK,UAAW,CAAC,EACxD;UACAJ,cAAc,GAAGC,SAAS,CAACC,MAAM;SAClC,MAAM;UACL,MAAM,IAAIP,KAAK,CACb,uTAAuT,CACxT;;;MAIL,MAAMlJ,GAAG,GAAG,IAAI4J,GAAG,CAAC,CAAAlH,EAAA,GAAA1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,GAAG,cAAA0C,EAAA,cAAAA,EAAA,GAAImD,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MAEzD,IAAI,QAAQ,IAAIwD,cAAc,IAAIA,cAAc,CAACG,MAAM,EAAE;QACvD,MAAMG,MAAM,GAAG,MAAMN,cAAc,CAACG,MAAM,CAAAtH,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;UACxCyH,QAAQ,EAAE,IAAItE,IAAI,EAAE,CAACC,WAAW;QAAE,GAE/BzE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB;UAE5B;UACAvJ,OAAO,EAAE,GAAG;UACZqK,MAAM,EAAE/J,GAAG,CAACgK,IAAI;UAChBC,GAAG,EAAEjK,GAAG,CAAC+F;QAAI,IAETuD,SAAS,GAAG;UAAEA;QAAS,CAAE,GAAG,IAAK,EACrC;QAEF,IAAIY,eAAoB;QAExB,IAAI5E,KAAK,CAAC6E,OAAO,CAACN,MAAM,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAI,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;UACvEK,eAAe,GAAGL,MAAM,CAAC,CAAC,CAAC;SAC5B,MAAM,IACLA,MAAM,IACN,OAAOA,MAAM,KAAK,QAAQ,IAC1B,eAAe,IAAIA,MAAM,IACzB,WAAW,IAAIA,MAAM,EACrB;UACAK,eAAe,GAAGL,MAAM;SACzB,MAAM;UACL,MAAM,IAAIX,KAAK,CAAC,uEAAuE,CAAC;;QAG1F,IACE,eAAe,IAAIgB,eAAe,IAClC,WAAW,IAAIA,eAAe,KAC7B,OAAOA,eAAe,CAACE,aAAa,KAAK,QAAQ,IAChDF,eAAe,CAACE,aAAa,YAAYC,UAAU,CAAC,IACtDH,eAAe,CAACd,SAAS,YAAYiB,UAAU,EAC/C;UACAlB,OAAO,GACL,OAAOe,eAAe,CAACE,aAAa,KAAK,QAAQ,GAC7CF,eAAe,CAACE,aAAa,GAC7B,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACL,eAAe,CAACE,aAAa,CAAC;UAC7DhB,SAAS,GAAGc,eAAe,CAACd,SAAS;SACtC,MAAM;UACL,MAAM,IAAIF,KAAK,CACb,0GAA0G,CAC3G;;OAEJ,MAAM;QACL,IACE,EAAE,aAAa,IAAIK,cAAc,CAAC,IAClC,OAAOA,cAAc,CAACI,WAAW,KAAK,UAAU,IAChD,EAAE,WAAW,IAAIJ,cAAc,CAAC,IAChC,OAAOA,cAAc,KAAK,QAAQ,IAClC,CAACA,cAAc,CAACiB,SAAS,IACzB,EAAE,UAAU,IAAIjB,cAAc,CAACiB,SAAS,CAAC,IACzC,OAAOjB,cAAc,CAACiB,SAAS,CAACC,QAAQ,KAAK,UAAU,EACvD;UACA,MAAM,IAAIvB,KAAK,CACb,iGAAiG,CAClG;;QAGHC,OAAO,GAAG,CACR,GAAGnJ,GAAG,CAACgK,IAAI,iDAAiD,EAC5DT,cAAc,CAACiB,SAAS,CAACC,QAAQ,EAAE,EACnC,IAAInB,SAAS,GAAG,CAAC,EAAE,EAAEA,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAC3C,YAAY,EACZ,QAAQtJ,GAAG,CAAC+F,IAAI,EAAE,EAClB,cAAc,CAAAoB,EAAA,IAAA3C,EAAA,GAAAxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAAzE,EAAA,uBAAAA,EAAA,CAAEsF,QAAQ,cAAA3C,EAAA,cAAAA,EAAA,GAAI,IAAI3B,IAAI,EAAE,CAACC,WAAW,EAAE,EAAE,EAC/E,IAAI,EAAAkD,EAAA,GAAA3H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAAN,EAAA,uBAAAA,EAAA,CAAE+B,SAAS,IACpC,CAAC,eAAe1J,OAAO,CAACiI,gBAAgB,CAACyB,SAAS,EAAE,CAAC,GACrD,EAAE,CAAC,EACP,IAAI,EAAAC,EAAA,GAAA3J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAA0B,EAAA,uBAAAA,EAAA,CAAEC,cAAc,IACzC,CAAC,oBAAoB5J,OAAO,CAACiI,gBAAgB,CAAC2B,cAAc,EAAE,CAAC,GAC/D,EAAE,CAAC,EACP,IAAI,EAAAC,EAAA,GAAA7J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAA4B,EAAA,uBAAAA,EAAA,CAAEC,OAAO,IAClC,CAAC,aAAa9J,OAAO,CAACiI,gBAAgB,CAAC6B,OAAO,EAAE,CAAC,GACjD,EAAE,CAAC,EACP,IAAI,EAAAC,EAAA,GAAA/J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAA8B,EAAA,uBAAAA,EAAA,CAAEC,KAAK,IAAG,CAAC,UAAUhK,OAAO,CAACiI,gBAAgB,CAAC+B,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EACzF,IAAI,EAAAC,EAAA,GAAAjK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAAgC,EAAA,uBAAAA,EAAA,CAAEC,SAAS,IACpC,CAAC,eAAelK,OAAO,CAACiI,gBAAgB,CAACiC,SAAS,EAAE,CAAC,GACrD,EAAE,CAAC,EACP,IAAI,EAAAC,EAAA,IAAAC,EAAA,GAAApK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiI,gBAAgB,cAAAmC,EAAA,uBAAAA,EAAA,CAAEC,SAAS,cAAAF,EAAA,uBAAAA,EAAA,CAAE/F,MAAM,IAC5C,CACE,WAAW,EACX,GAAGpE,OAAO,CAACiI,gBAAgB,CAACoC,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAK,KAAKA,QAAQ,EAAE,CAAC,CACzE,GACD,EAAE,CAAC,CACR,CAACC,IAAI,CAAC,IAAI,CAAC;QAEZ,MAAMC,cAAc,GAAG,MAAMlC,cAAc,CAACI,WAAW,CACrD,IAAI+B,WAAW,EAAE,CAACC,MAAM,CAACxC,OAAO,CAAC,EACjC,MAAM,CACP;QAED,IAAI,CAACsC,cAAc,IAAI,EAAEA,cAAc,YAAYpB,UAAU,CAAC,EAAE;UAC9D,MAAM,IAAInB,KAAK,CACb,0EAA0E,CAC3E;;QAGHE,SAAS,GAAGqC,cAAc;;;IAI9B,IAAI;MACF,MAAM;QAAE5G,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAMpG,QAAQ,CACpC,IAAI,CAACqE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACxC,GAAG,wBAAwB,EACnC;QACEK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB2G,IAAI,EAAA5E,MAAA,CAAAC,MAAA;UACF2G,KAAK,EAAE,QAAQ;UACfG,OAAO;UACPC,SAAS,EAAEtJ,gBAAgB,CAACsJ,SAAS;QAAC,GAElC,EAAAwC,EAAA,GAAA9E,WAAW,CAAC9F,OAAO,cAAA4K,EAAA,uBAAAA,EAAA,CAAExE,YAAY,IACjC;UAAEH,oBAAoB,EAAE;YAAEC,aAAa,EAAE,CAAA2E,EAAA,GAAA/E,WAAW,CAAC9F,OAAO,cAAA6K,EAAA,uBAAAA,EAAA,CAAEzE;UAAY;QAAE,CAAE,GAC9E,IAAK,CACV;QACDC,KAAK,EAAEjJ;OACR,CACF;MACD,IAAImG,KAAK,EAAE;QACT,MAAMA,KAAK;;MAEb,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACyC,IAAI,EAAE;QACxC,OAAO;UACLzC,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UACnCP,KAAK,EAAE,IAAI5G,6BAA6B;SACzC;;MAEH,IAAIkH,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QAAED,IAAI,EAAAzC,MAAA,CAAAC,MAAA,KAAOwC,IAAI,CAAE;QAAEN;MAAK,CAAE;KACpC,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEQ,MAAMuE,uBAAuBA,CAACD,QAAgB;IAOpD,MAAMiD,WAAW,GAAG,MAAMrN,YAAY,CAAC,IAAI,CAAC0F,OAAO,EAAE,GAAG,IAAI,CAAClE,UAAU,gBAAgB,CAAC;IACxF,MAAM,CAAC8L,YAAY,EAAEvF,YAAY,CAAC,GAAI,CAACsF,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE,EAAaE,KAAK,CAAC,GAAG,CAAC;IAE/E,IAAI;MACF,MAAM;QAAEnH,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAMpG,QAAQ,CACpC,IAAI,CAACqE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACxC,GAAG,wBAAwB,EACnC;QACEK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB2G,IAAI,EAAE;UACJiF,SAAS,EAAEpD,QAAQ;UACnBqD,aAAa,EAAEH;SAChB;QACD1E,KAAK,EAAEjJ;OACR,CACF;MACD,MAAMO,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,GAAG,IAAI,CAAClE,UAAU,gBAAgB,CAAC;MACvE,IAAIsE,KAAK,EAAE;QACT,MAAMA,KAAK;;MAEb,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACyC,IAAI,EAAE;QACxC,OAAO;UACLzC,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE,IAAI;YAAE0B,YAAY,EAAE;UAAI,CAAE;UACvDjC,KAAK,EAAE,IAAI5G,6BAA6B;SACzC;;MAEH,IAAIkH,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QAAED,IAAI,EAAAzC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOwC,IAAI;UAAE2B,YAAY,EAAEA,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI;QAAI,EAAE;QAAEjC;MAAK,CAAE;KACxE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE,IAAI;YAAE0B,YAAY,EAAE;UAAI,CAAE;UAAEjC;QAAK,CAAE;;MAG3E,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAM4H,iBAAiBA,CAACrF,WAAyC;IAC/D,IAAI;MACF,MAAM;QAAE9F,OAAO;QAAEuH,QAAQ;QAAE6D,KAAK;QAAEC,YAAY;QAAErB;MAAK,CAAE,GAAGlE,WAAW;MAErE,MAAMC,GAAG,GAAG,MAAM5I,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,4BAA4B,EAAE;QACtFK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB2G,IAAI,EAAE;UACJuB,QAAQ;UACR+D,QAAQ,EAAEF,KAAK;UACfC,YAAY;UACZrB,KAAK;UACL/D,oBAAoB,EAAE;YAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;UAAY;SAC7D;QACDC,KAAK,EAAEjJ;OACR,CAAC;MAEF,MAAM;QAAEyG,IAAI;QAAEN;MAAK,CAAE,GAAGwC,GAAG;MAC3B,IAAIxC,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACyC,IAAI,EAAE;QAC/C,OAAO;UACLzC,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UACnCP,KAAK,EAAE,IAAI5G,6BAA6B;SACzC;;MAEH,IAAIkH,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAAC2B,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QAAED,IAAI;QAAEN;MAAK,CAAE;KACvB,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;;;;;;;EAiBA,MAAMgI,aAAaA,CAACzF,WAA8C;;IAChE,IAAI;MACF,IAAI,OAAO,IAAIA,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAExG;QAAO,CAAE,GAAG8F,WAAW;QACtC,IAAIY,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAACrH,QAAQ,KAAK,MAAM,EAAE;UAC5B;UAAC,CAACoH,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CACrE,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,CAChB;;QAEH,MAAM;UAAEsE;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,MAAM,EAAE;UACtEK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJQ,KAAK;YACL3C,IAAI,EAAE,CAAAnC,EAAA,GAAA1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,IAAI,cAAAnC,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzB8J,WAAW,EAAE,CAAAhI,EAAA,GAAAxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyL,gBAAgB,cAAAjI,EAAA,cAAAA,EAAA,GAAI,IAAI;YAC9CyC,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY,CAAE;YAC9DU,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;WACxB;UACDC,UAAU,EAAE5G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6G;SACtB,CAAC;QACF,OAAO;UAAEhD,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,IAAI,OAAO,IAAIuC,WAAW,EAAE;QAC1B,MAAM;UAAEkB,KAAK;UAAEhH;QAAO,CAAE,GAAG8F,WAAW;QACtC,MAAM;UAAEjC,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,MAAM,EAAE;UAC5EK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJgB,KAAK;YACLnD,IAAI,EAAE,CAAAsC,EAAA,GAAAnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6D,IAAI,cAAAsC,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzBqF,WAAW,EAAE,CAAA7D,EAAA,GAAA3H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyL,gBAAgB,cAAA9D,EAAA,cAAAA,EAAA,GAAI,IAAI;YAC9C1B,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY,CAAE;YAC9Da,OAAO,EAAE,CAAA0C,EAAA,GAAA3J,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiH,OAAO,cAAA0C,EAAA,cAAAA,EAAA,GAAI;;SAEhC,CAAC;QACF,OAAO;UAAE9F,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE,IAAI;YAAE4H,SAAS,EAAE7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H;UAAU,CAAE;UAAEpI;QAAK,CAAE;;MAEpF,MAAM,IAAI9G,2BAA2B,CAAC,mDAAmD,CAAC;KAC3F,CAAC,OAAO8G,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMqI,SAASA,CAAChH,MAAuB;;IACrC,IAAI;MACF,IAAIgC,UAAU,GAAuBiF,SAAS;MAC9C,IAAIzF,YAAY,GAAuByF,SAAS;MAChD,IAAI,SAAS,IAAIjH,MAAM,EAAE;QACvBgC,UAAU,GAAG,CAAAlF,EAAA,GAAAkD,MAAM,CAAC5E,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEkF,UAAU;QACvCR,YAAY,GAAG,CAAA5C,EAAA,GAAAoB,MAAM,CAAC5E,OAAO,cAAAwD,EAAA,uBAAAA,EAAA,CAAE4C,YAAY;;MAE7C,MAAM;QAAEvC,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,SAAS,EAAE;QAC/EK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB2G,IAAI,EAAA5E,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACCuD,MAAM;UACTqB,oBAAoB,EAAE;YAAEC,aAAa,EAAEE;UAAY;QAAE,EACtD;QACDQ,UAAU;QACVP,KAAK,EAAEjJ;OACR,CAAC;MAEF,IAAImG,KAAK,EAAE;QACT,MAAMA,KAAK;;MAGb,IAAI,CAACM,IAAI,EAAE;QACT,MAAM,IAAIqE,KAAK,CAAC,0CAA0C,CAAC;;MAG7D,MAAMpE,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMwC,IAAI,GAASzC,IAAI,CAACyC,IAAI;MAE5B,IAAIxC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuH,YAAY,EAAE;QACzB,MAAM,IAAI,CAAC5F,YAAY,CAAC3B,OAAkB,CAAC;QAC3C,MAAM,IAAI,CAACF,qBAAqB,CAC9BgB,MAAM,CAACkH,IAAI,IAAI,UAAU,GAAG,mBAAmB,GAAG,WAAW,EAC7DhI,OAAO,CACR;;MAGH,OAAO;QAAED,IAAI,EAAE;UAAEyC,IAAI;UAAExC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;;;;EAcA,MAAMwI,aAAaA,CAACnH,MAAqB;;IACvC,IAAI;MACF,IAAI8B,aAAa,GAAkB,IAAI;MACvC,IAAIC,mBAAmB,GAAkB,IAAI;MAC7C,IAAI,IAAI,CAACrH,QAAQ,KAAK,MAAM,EAAE;QAC5B;QAAC,CAACoH,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CACrE,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,CAChB;;MAGH,OAAO,MAAM9B,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,MAAM,EAAE;QAC3DgH,IAAI,EAAA5E,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACE,YAAY,IAAIuD,MAAM,GAAG;UAAEoH,WAAW,EAAEpH,MAAM,CAACqH;QAAU,CAAE,GAAG,IAAK,GACnE,QAAQ,IAAIrH,MAAM,GAAG;UAAEmE,MAAM,EAAEnE,MAAM,CAACmE;QAAM,CAAE,GAAG,IAAK;UAC1DmD,WAAW,EAAE,CAAA1I,EAAA,IAAA9B,EAAA,GAAAkD,MAAM,CAAC5E,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEkF,UAAU,cAAApD,EAAA,cAAAA,EAAA,GAAIqI;QAAS,IAChD,EAAA1F,EAAA,GAAAvB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE5E,OAAO,cAAAmG,EAAA,uBAAAA,EAAA,CAAEC,YAAY,IAC7B;UAAEH,oBAAoB,EAAE;YAAEC,aAAa,EAAEtB,MAAM,CAAC5E,OAAO,CAACoG;UAAY;QAAE,CAAE,GACxE,IAAK;UACT+F,kBAAkB,EAAE,IAAI;UACxBrF,cAAc,EAAEJ,aAAa;UAC7BK,qBAAqB,EAAEJ;QAAmB,EAC3C;QACDtH,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBgH,KAAK,EAAE9I;OACR,CAAC;KACH,CAAC,OAAOgG,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAM6I,cAAcA,CAAA;IAClB,MAAM,IAAI,CAAC5L,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAAC2H,eAAe,EAAE;IACrC,CAAC,CAAC;EACJ;EAEQ,MAAMA,eAAeA,CAAA;IAC3B,IAAI;MACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAAC,MAAOC,MAAM,IAAI;QAC7C,MAAM;UACJ1I,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP,KAAK,EAAEiJ;QAAY,CACpB,GAAGD,MAAM;QACV,IAAIC,YAAY,EAAE,MAAMA,YAAY;QACpC,IAAI,CAAC1I,OAAO,EAAE,MAAM,IAAIpH,uBAAuB,EAAE;QAEjD,MAAM;UAAE6G;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACxC,GAAG,iBAAiB,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE3I,OAAO,CAACuH;SACd,CAAC;QACF,OAAO;UAAExH,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;MACvD,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMmJ,MAAMA,CAAC5G,WAAyB;IACpC,IAAI;MACF,MAAM6G,QAAQ,GAAG,GAAG,IAAI,CAAC3N,GAAG,SAAS;MACrC,IAAI,OAAO,IAAI8G,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAEsF,IAAI;UAAE9L;QAAO,CAAE,GAAG8F,WAAW;QAC5C,MAAM;UAAEvC;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAEmL,QAAQ,EAAE;UAC7DtN,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJQ,KAAK;YACLsF,IAAI;YACJ7F,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY;WAC7D;UACDQ,UAAU,EAAE5G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6G;SACtB,CAAC;QACF,OAAO;UAAEhD,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,OAAO,IAAIuC,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAE8E,IAAI;UAAE9L;QAAO,CAAE,GAAG8F,WAAW;QAC5C,MAAM;UAAEjC,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAEmL,QAAQ,EAAE;UACnEtN,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB2G,IAAI,EAAE;YACJgB,KAAK;YACL8E,IAAI;YACJ7F,oBAAoB,EAAE;cAAEC,aAAa,EAAElG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;YAAY;;SAE/D,CAAC;QACF,OAAO;UAAEvC,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE,IAAI;YAAE4H,SAAS,EAAE7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H;UAAU,CAAE;UAAEpI;QAAK,CAAE;;MAEpF,MAAM,IAAI9G,2BAA2B,CACnC,6DAA6D,CAC9D;KACF,CAAC,OAAO8G,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;EAWA,MAAMqJ,UAAUA,CAAA;IACd,MAAM,IAAI,CAACpM,iBAAiB;IAE5B,MAAM+L,MAAM,GAAG,MAAM,IAAI,CAAC7H,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACpD,OAAO,IAAI,CAAC4H,WAAW,CAAC,MAAOC,MAAM,IAAI;QACvC,OAAOA,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOA,MAAM;EACf;EAEA;;;EAGQ,MAAM7H,YAAYA,CAAI/E,cAAsB,EAAEC,EAAoB;IACxE,IAAI,CAAC+D,MAAM,CAAC,eAAe,EAAE,OAAO,EAAEhE,cAAc,CAAC;IAErD,IAAI;MACF,IAAI,IAAI,CAACe,YAAY,EAAE;QACrB,MAAMmM,IAAI,GAAG,IAAI,CAAClM,aAAa,CAACyD,MAAM,GAClC,IAAI,CAACzD,aAAa,CAAC,IAAI,CAACA,aAAa,CAACyD,MAAM,GAAG,CAAC,CAAC,GACjD0I,OAAO,CAACC,OAAO,EAAE;QAErB,MAAMR,MAAM,GAAG,CAAC,YAAW;UACzB,MAAMM,IAAI;UACV,OAAO,MAAMjN,EAAE,EAAE;QACnB,CAAC,EAAC,CAAE;QAEJ,IAAI,CAACe,aAAa,CAACqM,IAAI,CACrB,CAAC,YAAW;UACV,IAAI;YACF,MAAMT,MAAM;WACb,CAAC,OAAOjJ,CAAM,EAAE;YACf;UAAA;QAEJ,CAAC,EAAC,CAAE,CACL;QAED,OAAOiJ,MAAM;;MAGf,OAAO,MAAM,IAAI,CAAC9K,IAAI,CAAC,QAAQ,IAAI,CAACxC,UAAU,EAAE,EAAEU,cAAc,EAAE,YAAW;QAC3E,IAAI,CAACgE,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE,IAAI,CAAC1E,UAAU,CAAC;QAE9E,IAAI;UACF,IAAI,CAACyB,YAAY,GAAG,IAAI;UAExB,MAAM6L,MAAM,GAAG3M,EAAE,EAAE;UAEnB,IAAI,CAACe,aAAa,CAACqM,IAAI,CACrB,CAAC,YAAW;YACV,IAAI;cACF,MAAMT,MAAM;aACb,CAAC,OAAOjJ,CAAM,EAAE;cACf;YAAA;UAEJ,CAAC,EAAC,CAAE,CACL;UAED,MAAMiJ,MAAM;UAEZ;UACA,OAAO,IAAI,CAAC5L,aAAa,CAACyD,MAAM,EAAE;YAChC,MAAM6I,MAAM,GAAG,CAAC,GAAG,IAAI,CAACtM,aAAa,CAAC;YAEtC,MAAMmM,OAAO,CAACI,GAAG,CAACD,MAAM,CAAC;YAEzB,IAAI,CAACtM,aAAa,CAACwM,MAAM,CAAC,CAAC,EAAEF,MAAM,CAAC7I,MAAM,CAAC;;UAG7C,OAAO,MAAMmI,MAAM;SACpB,SAAS;UACR,IAAI,CAAC5I,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE,IAAI,CAAC1E,UAAU,CAAC;UAE9E,IAAI,CAACyB,YAAY,GAAG,KAAK;;MAE7B,CAAC,CAAC;KACH,SAAS;MACR,IAAI,CAACiD,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;;EAEvC;EAEA;;;;;;EAMQ,MAAM2I,WAAWA,CACvB1M,EAoBe;IAEf,IAAI,CAAC+D,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC;IAEpC,IAAI;MACF;MACA,MAAM4I,MAAM,GAAG,MAAM,IAAI,CAACa,aAAa,EAAE;MAEzC,OAAO,MAAMxN,EAAE,CAAC2M,MAAM,CAAC;KACxB,SAAS;MACR,IAAI,CAAC5I,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC;;EAEtC;EAEA;;;;;EAKQ,MAAMyJ,aAAaA,CAAA;IAoBzB,IAAI,CAACzJ,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAExC,IAAI,CAAC,IAAI,CAACjD,YAAY,EAAE;MACtB,IAAI,CAACiD,MAAM,CAAC,kBAAkB,EAAE,mCAAmC,EAAE,IAAIuE,KAAK,EAAE,CAACmF,KAAK,CAAC;;IAGzF,IAAI;MACF,IAAIC,cAAc,GAAmB,IAAI;MAEzC,MAAMC,YAAY,GAAG,MAAM9P,YAAY,CAAC,IAAI,CAAC0F,OAAO,EAAE,IAAI,CAAClE,UAAU,CAAC;MAEtE,IAAI,CAAC0E,MAAM,CAAC,eAAe,EAAE,sBAAsB,EAAE4J,YAAY,CAAC;MAElE,IAAIA,YAAY,KAAK,IAAI,EAAE;QACzB,IAAI,IAAI,CAACC,eAAe,CAACD,YAAY,CAAC,EAAE;UACtCD,cAAc,GAAGC,YAAY;SAC9B,MAAM;UACL,IAAI,CAAC5J,MAAM,CAAC,eAAe,EAAE,mCAAmC,CAAC;UACjE,MAAM,IAAI,CAAC4B,cAAc,EAAE;;;MAI/B,IAAI,CAAC+H,cAAc,EAAE;QACnB,OAAO;UAAEzJ,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAE;QAAI,CAAE;;MAGjD;MACA;MACA;MACA;MACA;MACA,MAAMkK,UAAU,GAAGH,cAAc,CAACI,UAAU,GACxCJ,cAAc,CAACI,UAAU,GAAG,IAAI,GAAGlJ,IAAI,CAACmJ,GAAG,EAAE,GAAG1R,gBAAgB,GAChE,KAAK;MAET,IAAI,CAAC0H,MAAM,CACT,kBAAkB,EAClB,cAAc8J,UAAU,GAAG,EAAE,GAAG,MAAM,UAAU,EAChD,YAAY,EACZH,cAAc,CAACI,UAAU,CAC1B;MAED,IAAI,CAACD,UAAU,EAAE;QACf,IAAI,IAAI,CAACxN,WAAW,EAAE;UACpB,MAAM2N,SAAS,GAAmC,MAAMnQ,YAAY,CAClE,IAAI,CAACwC,WAAW,EAChB,IAAI,CAAChB,UAAU,GAAG,OAAO,CAClB;UAET,IAAI2O,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEtH,IAAI,EAAE;YACnBgH,cAAc,CAAChH,IAAI,GAAGsH,SAAS,CAACtH,IAAI;WACrC,MAAM;YACLgH,cAAc,CAAChH,IAAI,GAAGhI,qBAAqB,EAAE;;;QAIjD,IAAI,IAAI,CAAC6E,OAAO,CAAC0K,QAAQ,IAAIP,cAAc,CAAChH,IAAI,EAAE;UAChD,IAAIwH,eAAe,GAAG,IAAI,CAACrN,yBAAyB;UACpD,MAAMsN,YAAY,GAAY,IAAIC,KAAK,CAACV,cAAc,EAAE;YACtDW,GAAG,EAAEA,CAACC,MAAW,EAAEC,IAAY,EAAEC,QAAa,KAAI;cAChD,IAAI,CAACN,eAAe,IAAIK,IAAI,KAAK,MAAM,EAAE;gBACvC;gBACArN,OAAO,CAACI,IAAI,CACV,iWAAiW,CAClW;gBACD4M,eAAe,GAAG,IAAI,EAAC;gBACvB,IAAI,CAACrN,yBAAyB,GAAG,IAAI,EAAC;;cAExC,OAAO4N,OAAO,CAACJ,GAAG,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;YAC5C;WACD,CAAC;UACFd,cAAc,GAAGS,YAAY;;QAG/B,OAAO;UAAElK,IAAI,EAAE;YAAEC,OAAO,EAAEwJ;UAAc,CAAE;UAAE/J,KAAK,EAAE;QAAI,CAAE;;MAG3D,MAAM;QAAEO,OAAO;QAAEP;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC+K,iBAAiB,CAAChB,cAAc,CAACiB,aAAa,CAAC;MACrF,IAAIhL,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAG3C,OAAO;QAAEM,IAAI,EAAE;UAAEC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAC1C,SAAS;MACR,IAAI,CAACI,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;;EAE1C;EAEA;;;;;;;EAOA,MAAM6K,OAAOA,CAAC/B,GAAY;IACxB,IAAIA,GAAG,EAAE;MACP,OAAO,MAAM,IAAI,CAACgC,QAAQ,CAAChC,GAAG,CAAC;;IAGjC,MAAM,IAAI,CAACjM,iBAAiB;IAE5B,MAAM+L,MAAM,GAAG,MAAM,IAAI,CAAC7H,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACpD,OAAO,MAAM,IAAI,CAAC+J,QAAQ,EAAE;IAC9B,CAAC,CAAC;IAEF,OAAOlC,MAAM;EACf;EAEQ,MAAMkC,QAAQA,CAAChC,GAAY;IACjC,IAAI;MACF,IAAIA,GAAG,EAAE;QACP,OAAO,MAAMtP,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACxC,GAAG,OAAO,EAAE;UAC3DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAEA,GAAG;UACRpG,KAAK,EAAE/I;SACR,CAAC;;MAGJ,OAAO,MAAM,IAAI,CAACgP,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAE1I,IAAI;UAAEN;QAAK,CAAE,GAAGgJ,MAAM;QAC9B,IAAIhJ,KAAK,EAAE;UACT,MAAMA,KAAK;;QAGb;QACA,IAAI,EAAC,CAAA7B,EAAA,GAAAmC,IAAI,CAACC,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J,YAAY,KAAI,CAAC,IAAI,CAAC7L,4BAA4B,EAAE;UACrE,OAAO;YAAEqE,IAAI,EAAE;cAAEyC,IAAI,EAAE;YAAI,CAAE;YAAE/C,KAAK,EAAE,IAAI7G,uBAAuB;UAAE,CAAE;;QAGvE,OAAO,MAAMS,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACxC,GAAG,OAAO,EAAE;UAC3DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE,CAAAtG,EAAA,IAAA3C,EAAA,GAAAK,IAAI,CAACC,OAAO,cAAAN,EAAA,uBAAAA,EAAA,CAAE6H,YAAY,cAAAlF,EAAA,cAAAA,EAAA,GAAI0F,SAAS;UAC5CxF,KAAK,EAAE/I;SACR,CAAC;MACJ,CAAC,CAAC;KACH,CAAC,OAAOiG,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,IAAIvG,yBAAyB,CAACuG,KAAK,CAAC,EAAE;UACpC;UACA;UAEA,MAAM,IAAI,CAACgC,cAAc,EAAE;UAC3B,MAAM5H,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,GAAG,IAAI,CAAClE,UAAU,gBAAgB,CAAC;;QAGzE,OAAO;UAAE4E,IAAI,EAAE;YAAEyC,IAAI,EAAE;UAAI,CAAE;UAAE/C;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMmL,UAAUA,CACdC,UAA0B,EAGpB;IAAA,IAFN3O,OAAA,GAAAmE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAEI,EAAE;IAEN,MAAM,IAAI,CAAC3D,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACkK,WAAW,CAACD,UAAU,EAAE3O,OAAO,CAAC;IACpD,CAAC,CAAC;EACJ;EAEU,MAAM4O,WAAWA,CACzBD,UAA0B,EAGpB;IAAA,IAFN3O,OAAA,GAAAmE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAEI,EAAE;IAEN,IAAI;MACF,OAAO,MAAM,IAAI,CAACmI,WAAW,CAAC,MAAOC,MAAM,IAAI;QAC7C,MAAM;UAAE1I,IAAI,EAAEgL,WAAW;UAAEtL,KAAK,EAAEiJ;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,MAAMA,YAAY;;QAEpB,IAAI,CAACqC,WAAW,CAAC/K,OAAO,EAAE;UACxB,MAAM,IAAIpH,uBAAuB,EAAE;;QAErC,MAAMoH,OAAO,GAAY+K,WAAW,CAAC/K,OAAO;QAC5C,IAAI4C,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAACrH,QAAQ,KAAK,MAAM,IAAIqP,UAAU,CAACnI,KAAK,IAAI,IAAI,EAAE;UACxD;UAAC,CAACE,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CACrE,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,CAChB;;QAGH,MAAM;UAAE4E,IAAI;UAAEN,KAAK,EAAEuL;QAAS,CAAE,GAAG,MAAM3R,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACxC,GAAG,OAAO,EAAE;UACvFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBuH,UAAU,EAAE5G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6G,eAAe;UACpCb,IAAI,EAAA5E,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACCsN,UAAU;YACb7H,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;UAAmB,EAC3C;UACD8F,GAAG,EAAE3I,OAAO,CAACuH,YAAY;UACzBhF,KAAK,EAAE/I;SACR,CAAC;QACF,IAAIwR,SAAS,EAAE,MAAMA,SAAS;QAC9BhL,OAAO,CAACwC,IAAI,GAAGzC,IAAI,CAACyC,IAAY;QAChC,MAAM,IAAI,CAACb,YAAY,CAAC3B,OAAO,CAAC;QAChC,MAAM,IAAI,CAACF,qBAAqB,CAAC,cAAc,EAAEE,OAAO,CAAC;QACzD,OAAO;UAAED,IAAI,EAAE;YAAEyC,IAAI,EAAExC,OAAO,CAACwC;UAAI,CAAE;UAAE/C,KAAK,EAAE;QAAI,CAAE;MACtD,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE;UAAI,CAAE;UAAE/C;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;EAKA,MAAMwL,UAAUA,CAACzB,cAGhB;IACC,MAAM,IAAI,CAAC9M,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACsK,WAAW,CAAC1B,cAAc,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEU,MAAM0B,WAAWA,CAAC1B,cAG3B;IACC,IAAI;MACF,IAAI,CAACA,cAAc,CAACjC,YAAY,IAAI,CAACiC,cAAc,CAACiB,aAAa,EAAE;QACjE,MAAM,IAAI7R,uBAAuB,EAAE;;MAGrC,MAAMuS,OAAO,GAAGzK,IAAI,CAACmJ,GAAG,EAAE,GAAG,IAAI;MACjC,IAAIuB,SAAS,GAAGD,OAAO;MACvB,IAAIxB,UAAU,GAAG,IAAI;MACrB,IAAI3J,OAAO,GAAmB,IAAI;MAClC,MAAM;QAAEqL;MAAO,CAAE,GAAG9Q,SAAS,CAACiP,cAAc,CAACjC,YAAY,CAAC;MAC1D,IAAI8D,OAAO,CAACC,GAAG,EAAE;QACfF,SAAS,GAAGC,OAAO,CAACC,GAAG;QACvB3B,UAAU,GAAGyB,SAAS,IAAID,OAAO;;MAGnC,IAAIxB,UAAU,EAAE;QACd,MAAM;UAAE3J,OAAO,EAAEuL,gBAAgB;UAAE9L;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC+K,iBAAiB,CACvEhB,cAAc,CAACiB,aAAa,CAC7B;QACD,IAAIhL,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE;cAAEyC,IAAI,EAAE,IAAI;cAAExC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAEA;UAAK,CAAE;;QAG9D,IAAI,CAAC8L,gBAAgB,EAAE;UACrB,OAAO;YAAExL,IAAI,EAAE;cAAEyC,IAAI,EAAE,IAAI;cAAExC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAE;UAAI,CAAE;;QAE7DO,OAAO,GAAGuL,gBAAgB;OAC3B,MAAM;QACL,MAAM;UAAExL,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACkL,QAAQ,CAACnB,cAAc,CAACjC,YAAY,CAAC;QACxE,IAAI9H,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEbO,OAAO,GAAG;UACRuH,YAAY,EAAEiC,cAAc,CAACjC,YAAY;UACzCkD,aAAa,EAAEjB,cAAc,CAACiB,aAAa;UAC3CjI,IAAI,EAAEzC,IAAI,CAACyC,IAAI;UACfgJ,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAEL,SAAS,GAAGD,OAAO;UAC/BvB,UAAU,EAAEwB;SACb;QACD,MAAM,IAAI,CAACzJ,YAAY,CAAC3B,OAAO,CAAC;QAChC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEyC,IAAI,EAAExC,OAAO,CAACwC,IAAI;UAAExC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAC9D,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAEwC,IAAI,EAAE;UAAI,CAAE;UAAE/C;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;EAMA,MAAMiM,cAAcA,CAAClC,cAA0C;IAC7D,MAAM,IAAI,CAAC9M,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAAC+K,eAAe,CAACnC,cAAc,CAAC;IACnD,CAAC,CAAC;EACJ;EAEU,MAAMmC,eAAeA,CAACnC,cAE/B;IACC,IAAI;MACF,OAAO,MAAM,IAAI,CAAChB,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,IAAI,CAACe,cAAc,EAAE;UACnB,MAAM;YAAEzJ,IAAI;YAAEN;UAAK,CAAE,GAAGgJ,MAAM;UAC9B,IAAIhJ,KAAK,EAAE;YACT,MAAMA,KAAK;;UAGb+J,cAAc,GAAG,CAAA5L,EAAA,GAAAmC,IAAI,CAACC,OAAO,cAAApC,EAAA,cAAAA,EAAA,GAAImK,SAAS;;QAG5C,IAAI,EAACyB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiB,aAAa,GAAE;UAClC,MAAM,IAAI7R,uBAAuB,EAAE;;QAGrC,MAAM;UAAEoH,OAAO;UAAEP;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC+K,iBAAiB,CAAChB,cAAc,CAACiB,aAAa,CAAC;QACrF,IAAIhL,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE;cAAEyC,IAAI,EAAE,IAAI;cAAExC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAEA;UAAK,CAAE;;QAG9D,IAAI,CAACO,OAAO,EAAE;UACZ,OAAO;YAAED,IAAI,EAAE;cAAEyC,IAAI,EAAE,IAAI;cAAExC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAE;UAAI,CAAE;;QAG7D,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAExC,OAAO,CAACwC,IAAI;YAAExC;UAAO,CAAE;UAAEP,KAAK,EAAE;QAAI,CAAE;MAC/D,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEyC,IAAI,EAAE,IAAI;YAAExC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGQ,MAAM4B,kBAAkBA,CAC9BP,MAAuC,EACvCI,eAAuB;IAQvB,IAAI;MACF,IAAI,CAACtH,SAAS,EAAE,EAAE,MAAM,IAAInB,8BAA8B,CAAC,sBAAsB,CAAC;MAElF;MACA,IAAIqI,MAAM,CAACrB,KAAK,IAAIqB,MAAM,CAAC8K,iBAAiB,IAAI9K,MAAM,CAAC+K,UAAU,EAAE;QACjE;QACA;QACA,MAAM,IAAIpT,8BAA8B,CACtCqI,MAAM,CAAC8K,iBAAiB,IAAI,iDAAiD,EAC7E;UACEnM,KAAK,EAAEqB,MAAM,CAACrB,KAAK,IAAI,mBAAmB;UAC1C+B,IAAI,EAAEV,MAAM,CAAC+K,UAAU,IAAI;SAC5B,CACF;;MAGH;MACA,QAAQ3K,eAAe;QACrB,KAAK,UAAU;UACb,IAAI,IAAI,CAAC1F,QAAQ,KAAK,MAAM,EAAE;YAC5B,MAAM,IAAI9C,8BAA8B,CAAC,4BAA4B,CAAC;;UAExE;QACF,KAAK,MAAM;UACT,IAAI,IAAI,CAAC8C,QAAQ,KAAK,UAAU,EAAE;YAChC,MAAM,IAAI/C,8BAA8B,CAAC,sCAAsC,CAAC;;UAElF;QACF;QACA;;MAGF;MACA,IAAIyI,eAAe,KAAK,MAAM,EAAE;QAC9B,IAAI,CAACrB,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC;QAC5D,IAAI,CAACiB,MAAM,CAACU,IAAI,EAAE,MAAM,IAAI9I,8BAA8B,CAAC,mBAAmB,CAAC;QAC/E,MAAM;UAAEqH,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACuE,uBAAuB,CAAClD,MAAM,CAACU,IAAI,CAAC;QACvE,IAAI/B,KAAK,EAAE,MAAMA,KAAK;QAEtB,MAAMvE,GAAG,GAAG,IAAI4J,GAAG,CAAC/D,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;QACzC/F,GAAG,CAAC4Q,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;QAE/BhL,MAAM,CAACiL,OAAO,CAACC,YAAY,CAAClL,MAAM,CAACiL,OAAO,CAACE,KAAK,EAAE,EAAE,EAAEhR,GAAG,CAACiR,QAAQ,EAAE,CAAC;QAErE,OAAO;UAAEpM,IAAI,EAAE;YAAEC,OAAO,EAAED,IAAI,CAACC,OAAO;YAAE0B,YAAY,EAAE;UAAI,CAAE;UAAEjC,KAAK,EAAE;QAAI,CAAE;;MAG7E,MAAM;QACJ2M,cAAc;QACdC,sBAAsB;QACtB9E,YAAY;QACZkD,aAAa;QACbgB,UAAU;QACV7B,UAAU;QACV4B;MAAU,CACX,GAAG1K,MAAM;MAEV,IAAI,CAACyG,YAAY,IAAI,CAACkE,UAAU,IAAI,CAAChB,aAAa,IAAI,CAACe,UAAU,EAAE;QACjE,MAAM,IAAI/S,8BAA8B,CAAC,2BAA2B,CAAC;;MAGvE,MAAM0S,OAAO,GAAGmB,IAAI,CAACC,KAAK,CAAC7L,IAAI,CAACmJ,GAAG,EAAE,GAAG,IAAI,CAAC;MAC7C,MAAM2C,SAAS,GAAGC,QAAQ,CAAChB,UAAU,CAAC;MACtC,IAAIL,SAAS,GAAGD,OAAO,GAAGqB,SAAS;MAEnC,IAAI5C,UAAU,EAAE;QACdwB,SAAS,GAAGqB,QAAQ,CAAC7C,UAAU,CAAC;;MAGlC,MAAM8C,iBAAiB,GAAGtB,SAAS,GAAGD,OAAO;MAC7C,IAAIuB,iBAAiB,GAAG,IAAI,IAAItU,6BAA6B,EAAE;QAC7D4E,OAAO,CAACI,IAAI,CACV,iEAAiEsP,iBAAiB,iCAAiCF,SAAS,GAAG,CAChI;;MAGH,MAAMxH,QAAQ,GAAGoG,SAAS,GAAGoB,SAAS;MACtC,IAAIrB,OAAO,GAAGnG,QAAQ,IAAI,GAAG,EAAE;QAC7BhI,OAAO,CAACI,IAAI,CACV,iGAAiG,EACjG4H,QAAQ,EACRoG,SAAS,EACTD,OAAO,CACR;OACF,MAAM,IAAIA,OAAO,GAAGnG,QAAQ,GAAG,CAAC,EAAE;QACjChI,OAAO,CAACI,IAAI,CACV,8GAA8G,EAC9G4H,QAAQ,EACRoG,SAAS,EACTD,OAAO,CACR;;MAGH,MAAM;QAAEpL,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAACkL,QAAQ,CAACpD,YAAY,CAAC;MACzD,IAAI9H,KAAK,EAAE,MAAMA,KAAK;MAEtB,MAAMO,OAAO,GAAY;QACvBoM,cAAc;QACdC,sBAAsB;QACtB9E,YAAY;QACZkE,UAAU,EAAEe,SAAS;QACrB5C,UAAU,EAAEwB,SAAS;QACrBX,aAAa;QACbe,UAAU;QACVhJ,IAAI,EAAEzC,IAAI,CAACyC;OACZ;MAED;MACAzB,MAAM,CAACC,QAAQ,CAAC2L,IAAI,GAAG,EAAE;MACzB,IAAI,CAAC9M,MAAM,CAAC,uBAAuB,EAAE,+BAA+B,CAAC;MAErE,OAAO;QAAEE,IAAI,EAAE;UAAEC,OAAO;UAAE0B,YAAY,EAAEZ,MAAM,CAACkH;QAAI,CAAE;QAAEvI,KAAK,EAAE;MAAI,CAAE;KACrE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAE0B,YAAY,EAAE;UAAI,CAAE;UAAEjC;QAAK,CAAE;;MAG/D,MAAMA,KAAK;;EAEf;EAEA;;;EAGQ0B,wBAAwBA,CAACL,MAAuC;IACtE,OAAO8L,OAAO,CAAC9L,MAAM,CAACyG,YAAY,IAAIzG,MAAM,CAAC8K,iBAAiB,CAAC;EACjE;EAEA;;;EAGQ,MAAMxK,eAAeA,CAACN,MAAuC;IACnE,MAAM+L,qBAAqB,GAAG,MAAMlT,YAAY,CAC9C,IAAI,CAAC0F,OAAO,EACZ,GAAG,IAAI,CAAClE,UAAU,gBAAgB,CACnC;IAED,OAAO,CAAC,EAAE2F,MAAM,CAACU,IAAI,IAAIqL,qBAAqB,CAAC;EACjD;EAEA;;;;;;;;EAQA,MAAMC,OAAOA,CAAA,EAAuC;IAAA,IAAtC5Q,OAAA,GAAAmE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAAmB;MAAE0M,KAAK,EAAE;IAAQ,CAAE;IAClD,MAAM,IAAI,CAACrQ,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACoM,QAAQ,CAAC9Q,OAAO,CAAC;IACrC,CAAC,CAAC;EACJ;EAEU,MAAM8Q,QAAQA,CAAA,EACkB;IAAA,IAAxC;MAAED;IAAK,IAAA1M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAAc;MAAE0M,KAAK,EAAE;IAAQ,CAAE;IAExC,OAAO,MAAM,IAAI,CAACvE,WAAW,CAAC,MAAOC,MAAM,IAAI;;MAC7C,MAAM;QAAE1I,IAAI;QAAEN,KAAK,EAAEiJ;MAAY,CAAE,GAAGD,MAAM;MAC5C,IAAIC,YAAY,EAAE;QAChB,OAAO;UAAEjJ,KAAK,EAAEiJ;QAAY,CAAE;;MAEhC,MAAMuE,WAAW,GAAG,CAAArP,EAAA,GAAAmC,IAAI,CAACC,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J,YAAY;MAC9C,IAAI0F,WAAW,EAAE;QACf,MAAM;UAAExN;QAAK,CAAE,GAAG,MAAM,IAAI,CAAChC,KAAK,CAACqP,OAAO,CAACG,WAAW,EAAEF,KAAK,CAAC;QAC9D,IAAItN,KAAK,EAAE;UACT;UACA;UACA,IACE,EACE1G,cAAc,CAAC0G,KAAK,CAAC,KACpBA,KAAK,CAACyN,MAAM,KAAK,GAAG,IAAIzN,KAAK,CAACyN,MAAM,KAAK,GAAG,IAAIzN,KAAK,CAACyN,MAAM,KAAK,GAAG,CAAC,CACvE,EACD;YACA,OAAO;cAAEzN;YAAK,CAAE;;;;MAItB,IAAIsN,KAAK,KAAK,QAAQ,EAAE;QACtB,MAAM,IAAI,CAACtL,cAAc,EAAE;QAC3B,MAAM5H,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,GAAG,IAAI,CAAClE,UAAU,gBAAgB,CAAC;;MAEzE,OAAO;QAAEsE,KAAK,EAAE;MAAI,CAAE;IACxB,CAAC,CAAC;EACJ;EAEA;;;;EAIA0N,iBAAiBA,CACfC,QAAmF;IAInF,MAAMC,EAAE,GAAWrT,IAAI,EAAE;IACzB,MAAMsT,YAAY,GAAiB;MACjCD,EAAE;MACFD,QAAQ;MACRG,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAC1N,MAAM,CAAC,gBAAgB,EAAE,uCAAuC,EAAEwN,EAAE,CAAC;QAE1E,IAAI,CAAChR,mBAAmB,CAAC0P,MAAM,CAACsB,EAAE,CAAC;MACrC;KACD;IAED,IAAI,CAACxN,MAAM,CAAC,sBAAsB,EAAE,6BAA6B,EAAEwN,EAAE,CAAC;IAEtE,IAAI,CAAChR,mBAAmB,CAACmR,GAAG,CAACH,EAAE,EAAEC,YAAY,CAAC;IAC7C,CAAC,YAAW;MACX,MAAM,IAAI,CAAC5Q,iBAAiB;MAE5B,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;QACrC,IAAI,CAAC6M,mBAAmB,CAACJ,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EAAC,CAAE;IAEJ,OAAO;MAAEtN,IAAI,EAAE;QAAEuN;MAAY;IAAE,CAAE;EACnC;EAEQ,MAAMG,mBAAmBA,CAACJ,EAAU;IAC1C,OAAO,MAAM,IAAI,CAAC7E,WAAW,CAAC,MAAOC,MAAM,IAAI;;MAC7C,IAAI;QACF,MAAM;UACJ1I,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP;QAAK,CACN,GAAGgJ,MAAM;QACV,IAAIhJ,KAAK,EAAE,MAAMA,KAAK;QAEtB,OAAM,CAAA7B,EAAA,OAAI,CAACvB,mBAAmB,CAAC8N,GAAG,CAACkD,EAAE,CAAC,cAAAzP,EAAA,uBAAAA,EAAA,CAAEwP,QAAQ,CAAC,iBAAiB,EAAEpN,OAAO,CAAC;QAC5E,IAAI,CAACH,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAEwN,EAAE,EAAE,SAAS,EAAErN,OAAO,CAAC;OACtE,CAAC,OAAO0N,GAAG,EAAE;QACZ,OAAM,CAAAhO,EAAA,OAAI,CAACrD,mBAAmB,CAAC8N,GAAG,CAACkD,EAAE,CAAC,cAAA3N,EAAA,uBAAAA,EAAA,CAAE0N,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;QACzE,IAAI,CAACvN,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAEwN,EAAE,EAAE,OAAO,EAAEK,GAAG,CAAC;QAC/D1Q,OAAO,CAACyC,KAAK,CAACiO,GAAG,CAAC;;IAEtB,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOA,MAAMC,qBAAqBA,CACzBjL,KAAa,EAIP;IAAA,IAHNxG,OAAA,GAAAmE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAGI,EAAE;IAQN,IAAIuC,aAAa,GAAkB,IAAI;IACvC,IAAIC,mBAAmB,GAAkB,IAAI;IAE7C,IAAI,IAAI,CAACrH,QAAQ,KAAK,MAAM,EAAE;MAC5B;MAAC,CAACoH,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CACrE,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,EACf,IAAI,CAAC;OACN;;IAEH,IAAI;MACF,OAAO,MAAM9B,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,UAAU,EAAE;QAC/DgH,IAAI,EAAE;UACJQ,KAAK;UACLM,cAAc,EAAEJ,aAAa;UAC7BK,qBAAqB,EAAEJ,mBAAmB;UAC1CV,oBAAoB,EAAE;YAAEC,aAAa,EAAElG,OAAO,CAACoG;UAAY;SAC5D;QACD/G,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBuH,UAAU,EAAE5G,OAAO,CAAC4G;OACrB,CAAC;KACH,CAAC,OAAOrD,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAG9B,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMmO,iBAAiBA,CAAA;;IASrB,IAAI;MACF,MAAM;QAAE7N,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAACiL,OAAO,EAAE;MAC5C,IAAIjL,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEM,IAAI,EAAE;UAAE8N,UAAU,EAAE,CAAAjQ,EAAA,GAAAmC,IAAI,CAACyC,IAAI,CAACqL,UAAU,cAAAjQ,EAAA,cAAAA,EAAA,GAAI;QAAE,CAAE;QAAE6B,KAAK,EAAE;MAAI,CAAE;KACzE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EACA;;;;EAIA,MAAMqO,YAAYA,CAAC9L,WAAuC;;IACxD,IAAI;MACF,MAAM;QAAEjC,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC+I,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC9D,MAAM;UAAE1I,IAAI;UAAEN;QAAK,CAAE,GAAGgJ,MAAM;QAC9B,IAAIhJ,KAAK,EAAE,MAAMA,KAAK;QACtB,MAAMvE,GAAG,GAAW,MAAM,IAAI,CAAC6S,kBAAkB,CAC/C,GAAG,IAAI,CAAC7S,GAAG,4BAA4B,EACvC8G,WAAW,CAACyB,QAAQ,EACpB;UACEX,UAAU,EAAE,CAAAlF,EAAA,GAAAoE,WAAW,CAAC9F,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEkF,UAAU;UAC3CY,MAAM,EAAE,CAAAhE,EAAA,GAAAsC,WAAW,CAAC9F,OAAO,cAAAwD,EAAA,uBAAAA,EAAA,CAAEgE,MAAM;UACnCC,WAAW,EAAE,CAAAtB,EAAA,GAAAL,WAAW,CAAC9F,OAAO,cAAAmG,EAAA,uBAAAA,EAAA,CAAEsB,WAAW;UAC7CC,mBAAmB,EAAE;SACtB,CACF;QACD,OAAO,MAAMvK,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAExC,GAAG,EAAE;UAC5CK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE,CAAA9C,EAAA,IAAAhC,EAAA,GAAA9D,IAAI,CAACC,OAAO,cAAA6D,EAAA,uBAAAA,EAAA,CAAE0D,YAAY,cAAA1B,EAAA,cAAAA,EAAA,GAAIkC;SACpC,CAAC;MACJ,CAAC,CAAC;MACF,IAAItI,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAI7F,SAAS,EAAE,IAAI,EAAC,CAAAgE,EAAA,GAAAoE,WAAW,CAAC9F,OAAO,cAAA0B,EAAA,uBAAAA,EAAA,CAAEgG,mBAAmB,GAAE;QAC5D7C,MAAM,CAACC,QAAQ,CAACzD,MAAM,CAACwC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE7E,GAAG,CAAC;;MAEnC,OAAO;QAAE6E,IAAI,EAAE;UAAE0D,QAAQ,EAAEzB,WAAW,CAACyB,QAAQ;UAAEvI,GAAG,EAAE6E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE7E;QAAG,CAAE;QAAEuE,KAAK,EAAE;MAAI,CAAE;KACjF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAE0D,QAAQ,EAAEzB,WAAW,CAACyB,QAAQ;YAAEvI,GAAG,EAAE;UAAI,CAAE;UAAEuE;QAAK,CAAE;;MAEvE,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMuO,cAAcA,CAACC,QAAsB;IAOzC,IAAI;MACF,OAAO,MAAM,IAAI,CAACzF,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAE1I,IAAI;UAAEN;QAAK,CAAE,GAAGgJ,MAAM;QAC9B,IAAIhJ,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEb,OAAO,MAAMpG,QAAQ,CACnB,IAAI,CAACqE,KAAK,EACV,QAAQ,EACR,GAAG,IAAI,CAACxC,GAAG,oBAAoB+S,QAAQ,CAACC,WAAW,EAAE,EACrD;UACE3S,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE,CAAAjJ,EAAA,IAAA9B,EAAA,GAAAmC,IAAI,CAACC,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J,YAAY,cAAA7H,EAAA,cAAAA,EAAA,GAAIqI;SACpC,CACF;MACH,CAAC,CAAC;KACH,CAAC,OAAOtI,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;;EAIQ,MAAM0O,mBAAmBA,CAACC,YAAoB;IACpD,MAAMC,SAAS,GAAG,wBAAwBD,YAAY,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;IAC5E,IAAI,CAACzO,MAAM,CAACwO,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,MAAME,SAAS,GAAG7N,IAAI,CAACmJ,GAAG,EAAE;MAE5B;MACA,OAAO,MAAM5P,SAAS,CACpB,MAAOuU,OAAO,IAAI;QAChB,IAAIA,OAAO,GAAG,CAAC,EAAE;UACf,MAAMtU,KAAK,CAAC,GAAG,GAAGoS,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAED,OAAO,GAAG,CAAC,CAAC,CAAC,EAAC;;QAG9C,IAAI,CAAC3O,MAAM,CAACwO,SAAS,EAAE,oBAAoB,EAAEG,OAAO,CAAC;QAErD,OAAO,MAAMnV,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,iCAAiC,EAAE;UACtFgH,IAAI,EAAE;YAAEuI,aAAa,EAAE2D;UAAY,CAAE;UACrC7S,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBgH,KAAK,EAAEjJ;SACR,CAAC;MACJ,CAAC,EACD,CAACkV,OAAO,EAAE/O,KAAK,KAAI;QACjB,MAAMiP,mBAAmB,GAAG,GAAG,GAAGpC,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAED,OAAO,CAAC;QACtD,OACE/O,KAAK,IACLxG,yBAAyB,CAACwG,KAAK,CAAC;QAChC;QACAiB,IAAI,CAACmJ,GAAG,EAAE,GAAG6E,mBAAmB,GAAGH,SAAS,GAAGnW,6BAA6B;MAEhF,CAAC,CACF;KACF,CAAC,OAAOqH,KAAK,EAAE;MACd,IAAI,CAACI,MAAM,CAACwO,SAAS,EAAE,OAAO,EAAE5O,KAAK,CAAC;MAEtC,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAEwC,IAAI,EAAE;UAAI,CAAE;UAAE/C;QAAK,CAAE;;MAEvD,MAAMA,KAAK;KACZ,SAAS;MACR,IAAI,CAACI,MAAM,CAACwO,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ3E,eAAeA,CAACD,YAAqB;IAC3C,MAAMkF,cAAc,GAClB,OAAOlF,YAAY,KAAK,QAAQ,IAChCA,YAAY,KAAK,IAAI,IACrB,cAAc,IAAIA,YAAY,IAC9B,eAAe,IAAIA,YAAY,IAC/B,YAAY,IAAIA,YAAY;IAE9B,OAAOkF,cAAc;EACvB;EAEQ,MAAMnL,qBAAqBA,CACjCC,QAAkB,EAClBvH,OAKC;IAED,MAAMhB,GAAG,GAAW,MAAM,IAAI,CAAC6S,kBAAkB,CAAC,GAAG,IAAI,CAAC7S,GAAG,YAAY,EAAEuI,QAAQ,EAAE;MACnFX,UAAU,EAAE5G,OAAO,CAAC4G,UAAU;MAC9BY,MAAM,EAAExH,OAAO,CAACwH,MAAM;MACtBC,WAAW,EAAEzH,OAAO,CAACyH;KACtB,CAAC;IAEF,IAAI,CAAC9D,MAAM,CAAC,0BAA0B,EAAE,UAAU,EAAE4D,QAAQ,EAAE,SAAS,EAAEvH,OAAO,EAAE,KAAK,EAAEhB,GAAG,CAAC;IAE7F;IACA,IAAItB,SAAS,EAAE,IAAI,CAACsC,OAAO,CAAC0H,mBAAmB,EAAE;MAC/C7C,MAAM,CAACC,QAAQ,CAACzD,MAAM,CAACrC,GAAG,CAAC;;IAG7B,OAAO;MAAE6E,IAAI,EAAE;QAAE0D,QAAQ;QAAEvI;MAAG,CAAE;MAAEuE,KAAK,EAAE;IAAI,CAAE;EACjD;EAEA;;;;EAIQ,MAAMoC,kBAAkBA,CAAA;;IAC9B,MAAMwM,SAAS,GAAG,uBAAuB;IACzC,IAAI,CAACxO,MAAM,CAACwO,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,MAAM7E,cAAc,GAAI,MAAM7P,YAAY,CAAC,IAAI,CAAC0F,OAAO,EAAE,IAAI,CAAClE,UAAU,CAAoB;MAE5F,IAAIqO,cAAc,IAAI,IAAI,CAACrN,WAAW,EAAE;QACtC,IAAI2N,SAAS,GAAkC,MAAMnQ,YAAY,CAC/D,IAAI,CAACwC,WAAW,EAChB,IAAI,CAAChB,UAAU,GAAG,OAAO,CAClB;QAET,IAAI,CAAC,IAAI,CAACkE,OAAO,CAAC0K,QAAQ,IAAIzM,MAAM,CAACsR,EAAE,CAAC,IAAI,CAACvP,OAAO,EAAE,IAAI,CAAClD,WAAW,CAAC,IAAI,CAAC2N,SAAS,EAAE;UACrF;UACA;UACA;UACA;UAEAA,SAAS,GAAG;YAAEtH,IAAI,EAAEgH,cAAc,CAAChH;UAAI,CAAE;UACzC,MAAMzI,YAAY,CAAC,IAAI,CAACoC,WAAW,EAAE,IAAI,CAAChB,UAAU,GAAG,OAAO,EAAE2O,SAAS,CAAC;;QAG5EN,cAAc,CAAChH,IAAI,GAAG,CAAA5E,EAAA,GAAAkM,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEtH,IAAI,cAAA5E,EAAA,cAAAA,EAAA,GAAIpD,qBAAqB,EAAE;OACjE,MAAM,IAAIgP,cAAc,IAAI,CAACA,cAAc,CAAChH,IAAI,EAAE;QACjD;QACA;QAEA,IAAI,CAACgH,cAAc,CAAChH,IAAI,EAAE;UACxB;UACA,MAAMqM,YAAY,GAAkC,MAAMlV,YAAY,CACpE,IAAI,CAAC0F,OAAO,EACZ,IAAI,CAAClE,UAAU,GAAG,OAAO,CAClB;UAET,IAAI0T,YAAY,KAAIA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAErM,IAAI,GAAE;YACtCgH,cAAc,CAAChH,IAAI,GAAGqM,YAAY,CAACrM,IAAI;YAEvC,MAAM3I,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,IAAI,CAAClE,UAAU,GAAG,OAAO,CAAC;YAC9D,MAAMpB,YAAY,CAAC,IAAI,CAACsF,OAAO,EAAE,IAAI,CAAClE,UAAU,EAAEqO,cAAc,CAAC;WAClE,MAAM;YACLA,cAAc,CAAChH,IAAI,GAAGhI,qBAAqB,EAAE;;;;MAKnD,IAAI,CAACqF,MAAM,CAACwO,SAAS,EAAE,sBAAsB,EAAE7E,cAAc,CAAC;MAE9D,IAAI,CAAC,IAAI,CAACE,eAAe,CAACF,cAAc,CAAC,EAAE;QACzC,IAAI,CAAC3J,MAAM,CAACwO,SAAS,EAAE,sBAAsB,CAAC;QAC9C,IAAI7E,cAAc,KAAK,IAAI,EAAE;UAC3B,MAAM,IAAI,CAAC/H,cAAc,EAAE;;QAG7B;;MAGF,MAAMqN,iBAAiB,GACrB,CAAC,CAAApP,EAAA,GAAA8J,cAAc,CAACI,UAAU,cAAAlK,EAAA,cAAAA,EAAA,GAAIqP,QAAQ,IAAI,IAAI,GAAGrO,IAAI,CAACmJ,GAAG,EAAE,GAAG1R,gBAAgB;MAEhF,IAAI,CAAC0H,MAAM,CACTwO,SAAS,EACT,cAAcS,iBAAiB,GAAG,EAAE,GAAG,MAAM,2BAA2B3W,gBAAgB,GAAG,CAC5F;MAED,IAAI2W,iBAAiB,EAAE;QACrB,IAAI,IAAI,CAAC1T,gBAAgB,IAAIoO,cAAc,CAACiB,aAAa,EAAE;UACzD,MAAM;YAAEhL;UAAK,CAAE,GAAG,MAAM,IAAI,CAAC+K,iBAAiB,CAAChB,cAAc,CAACiB,aAAa,CAAC;UAE5E,IAAIhL,KAAK,EAAE;YACTzC,OAAO,CAACyC,KAAK,CAACA,KAAK,CAAC;YAEpB,IAAI,CAACxG,yBAAyB,CAACwG,KAAK,CAAC,EAAE;cACrC,IAAI,CAACI,MAAM,CACTwO,SAAS,EACT,iEAAiE,EACjE5O,KAAK,CACN;cACD,MAAM,IAAI,CAACgC,cAAc,EAAE;;;;OAIlC,MAAM,IACL+H,cAAc,CAAChH,IAAI,IAClBgH,cAAc,CAAChH,IAAY,CAACwM,yBAAyB,KAAK,IAAI,EAC/D;QACA;QACA,IAAI;UACF,MAAM;YAAEjP,IAAI;YAAEN,KAAK,EAAEuL;UAAS,CAAE,GAAG,MAAM,IAAI,CAACL,QAAQ,CAACnB,cAAc,CAACjC,YAAY,CAAC;UAEnF,IAAI,CAACyD,SAAS,KAAIjL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,IAAI,GAAE;YAC5BgH,cAAc,CAAChH,IAAI,GAAGzC,IAAI,CAACyC,IAAI;YAC/B,MAAM,IAAI,CAACb,YAAY,CAAC6H,cAAc,CAAC;YACvC,MAAM,IAAI,CAAC1J,qBAAqB,CAAC,WAAW,EAAE0J,cAAc,CAAC;WAC9D,MAAM;YACL,IAAI,CAAC3J,MAAM,CAACwO,SAAS,EAAE,0DAA0D,CAAC;;SAErF,CAAC,OAAOY,YAAY,EAAE;UACrBjS,OAAO,CAACyC,KAAK,CAAC,0BAA0B,EAAEwP,YAAY,CAAC;UACvD,IAAI,CAACpP,MAAM,CACTwO,SAAS,EACT,0DAA0D,EAC1DY,YAAY,CACb;;OAEJ,MAAM;QACL;QACA;QACA;QACA,MAAM,IAAI,CAACnP,qBAAqB,CAAC,WAAW,EAAE0J,cAAc,CAAC;;KAEhE,CAAC,OAAOkE,GAAG,EAAE;MACZ,IAAI,CAAC7N,MAAM,CAACwO,SAAS,EAAE,OAAO,EAAEX,GAAG,CAAC;MAEpC1Q,OAAO,CAACyC,KAAK,CAACiO,GAAG,CAAC;MAClB;KACD,SAAS;MACR,IAAI,CAAC7N,MAAM,CAACwO,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ,MAAM7D,iBAAiBA,CAAC4D,YAAoB;;IAClD,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIxV,uBAAuB,EAAE;;IAGrC;IACA,IAAI,IAAI,CAAC6D,kBAAkB,EAAE;MAC3B,OAAO,IAAI,CAACA,kBAAkB,CAACyS,OAAO;;IAGxC,MAAMb,SAAS,GAAG,sBAAsBD,YAAY,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;IAE1E,IAAI,CAACzO,MAAM,CAACwO,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,IAAI,CAAC5R,kBAAkB,GAAG,IAAI/C,QAAQ,EAA0B;MAEhE,MAAM;QAAEqG,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC0O,mBAAmB,CAACC,YAAY,CAAC;MACpE,IAAI3O,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIpH,uBAAuB,EAAE;MAEtD,MAAM,IAAI,CAAC+I,YAAY,CAAC5B,IAAI,CAACC,OAAO,CAAC;MACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,iBAAiB,EAAEC,IAAI,CAACC,OAAO,CAAC;MAEjE,MAAMyI,MAAM,GAAG;QAAEzI,OAAO,EAAED,IAAI,CAACC,OAAO;QAAEP,KAAK,EAAE;MAAI,CAAE;MAErD,IAAI,CAAChD,kBAAkB,CAACwM,OAAO,CAACR,MAAM,CAAC;MAEvC,OAAOA,MAAM;KACd,CAAC,OAAOhJ,KAAK,EAAE;MACd,IAAI,CAACI,MAAM,CAACwO,SAAS,EAAE,OAAO,EAAE5O,KAAK,CAAC;MAEtC,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,MAAMgJ,MAAM,GAAG;UAAEzI,OAAO,EAAE,IAAI;UAAEP;QAAK,CAAE;QAEvC,IAAI,CAACxG,yBAAyB,CAACwG,KAAK,CAAC,EAAE;UACrC,MAAM,IAAI,CAACgC,cAAc,EAAE;;QAG7B,CAAA7D,EAAA,OAAI,CAACnB,kBAAkB,cAAAmB,EAAA,uBAAAA,EAAA,CAAEqL,OAAO,CAACR,MAAM,CAAC;QAExC,OAAOA,MAAM;;MAGf,CAAA/I,EAAA,OAAI,CAACjD,kBAAkB,cAAAiD,EAAA,uBAAAA,EAAA,CAAEyP,MAAM,CAAC1P,KAAK,CAAC;MACtC,MAAMA,KAAK;KACZ,SAAS;MACR,IAAI,CAAChD,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACoD,MAAM,CAACwO,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ,MAAMvO,qBAAqBA,CACjCF,KAAsB,EACtBI,OAAuB,EACP;IAAA,IAAhBoP,SAAS,GAAA/O,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAAG,IAAI;IAEhB,MAAMgO,SAAS,GAAG,0BAA0BzO,KAAK,GAAG;IACpD,IAAI,CAACC,MAAM,CAACwO,SAAS,EAAE,OAAO,EAAErO,OAAO,EAAE,eAAeoP,SAAS,EAAE,CAAC;IAEpE,IAAI;MACF,IAAI,IAAI,CAACtS,gBAAgB,IAAIsS,SAAS,EAAE;QACtC,IAAI,CAACtS,gBAAgB,CAACuS,WAAW,CAAC;UAAEzP,KAAK;UAAEI;QAAO,CAAE,CAAC;;MAGvD,MAAMsP,MAAM,GAAU,EAAE;MACxB,MAAMC,QAAQ,GAAG/O,KAAK,CAACgP,IAAI,CAAC,IAAI,CAACnT,mBAAmB,CAACoT,MAAM,EAAE,CAAC,CAACjJ,GAAG,CAAC,MAAOkJ,CAAC,IAAI;QAC7E,IAAI;UACF,MAAMA,CAAC,CAACtC,QAAQ,CAACxN,KAAK,EAAEI,OAAO,CAAC;SACjC,CAAC,OAAOR,CAAM,EAAE;UACf8P,MAAM,CAACpG,IAAI,CAAC1J,CAAC,CAAC;;MAElB,CAAC,CAAC;MAEF,MAAMwJ,OAAO,CAACI,GAAG,CAACmG,QAAQ,CAAC;MAE3B,IAAID,MAAM,CAAChP,MAAM,GAAG,CAAC,EAAE;QACrB,KAAK,IAAIqP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAAChP,MAAM,EAAEqP,CAAC,IAAI,CAAC,EAAE;UACzC3S,OAAO,CAACyC,KAAK,CAAC6P,MAAM,CAACK,CAAC,CAAC,CAAC;;QAG1B,MAAML,MAAM,CAAC,CAAC,CAAC;;KAElB,SAAS;MACR,IAAI,CAACzP,MAAM,CAACwO,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEA;;;;EAIQ,MAAM1M,YAAYA,CAAC3B,OAAgB;IACzC,IAAI,CAACH,MAAM,CAAC,iBAAiB,EAAEG,OAAO,CAAC;IACvC;IACA;IACA,IAAI,CAACrD,yBAAyB,GAAG,IAAI;IAErC;IACA,MAAMiT,gBAAgB,GAAAtS,MAAA,CAAAC,MAAA,KAAQyC,OAAO,CAAE;IAEvC,MAAM6P,WAAW,GACfD,gBAAgB,CAACpN,IAAI,IAAKoN,gBAAgB,CAACpN,IAAY,CAACwM,yBAAyB,KAAK,IAAI;IAC5F,IAAI,IAAI,CAAC7S,WAAW,EAAE;MACpB,IAAI,CAAC0T,WAAW,IAAID,gBAAgB,CAACpN,IAAI,EAAE;QACzC;QACA,MAAMzI,YAAY,CAAC,IAAI,CAACoC,WAAW,EAAE,IAAI,CAAChB,UAAU,GAAG,OAAO,EAAE;UAC9DqH,IAAI,EAAEoN,gBAAgB,CAACpN;SACxB,CAAC;OACH,MAAM,IAAIqN,WAAW,EAAE;QACtB;QACA;QACA;QACA;MAAA;MAGF;MACA;MACA,MAAMC,eAAe,GAAAxS,MAAA,CAAAC,MAAA,KAAiDqS,gBAAgB,CAAE;MACxF,OAAOE,eAAe,CAACtN,IAAI,EAAC;MAE5B,MAAMuN,qBAAqB,GAAGC,eAAe,CAACF,eAAe,CAAC;MAC9D,MAAM/V,YAAY,CAAC,IAAI,CAACsF,OAAO,EAAE,IAAI,CAAClE,UAAU,EAAE4U,qBAAqB,CAAC;KACzE,MAAM;MACL;MACA;MACA;MACA,MAAME,aAAa,GAAGD,eAAe,CAACJ,gBAAgB,CAAC,EAAC;MACxD,MAAM7V,YAAY,CAAC,IAAI,CAACsF,OAAO,EAAE,IAAI,CAAClE,UAAU,EAAE8U,aAAa,CAAC;;EAEpE;EAEQ,MAAMxO,cAAcA,CAAA;IAC1B,IAAI,CAAC5B,MAAM,CAAC,mBAAmB,CAAC;IAEhC,MAAMhG,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,IAAI,CAAClE,UAAU,CAAC;IACpD,MAAMtB,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,IAAI,CAAClE,UAAU,GAAG,gBAAgB,CAAC;IACvE,MAAMtB,eAAe,CAAC,IAAI,CAACwF,OAAO,EAAE,IAAI,CAAClE,UAAU,GAAG,OAAO,CAAC;IAE9D,IAAI,IAAI,CAACgB,WAAW,EAAE;MACpB,MAAMtC,eAAe,CAAC,IAAI,CAACsC,WAAW,EAAE,IAAI,CAAChB,UAAU,GAAG,OAAO,CAAC;;IAGpE,MAAM,IAAI,CAAC2E,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;EACtD;EAEA;;;;;;EAMQoQ,gCAAgCA,CAAA;IACtC,IAAI,CAACrQ,MAAM,CAAC,qCAAqC,CAAC;IAElD,MAAMuN,QAAQ,GAAG,IAAI,CAAC5Q,yBAAyB;IAC/C,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC,IAAI;MACF,IAAI4Q,QAAQ,IAAIxT,SAAS,EAAE,KAAImH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoP,mBAAmB,GAAE;QAC1DpP,MAAM,CAACoP,mBAAmB,CAAC,kBAAkB,EAAE/C,QAAQ,CAAC;;KAE3D,CAAC,OAAO5N,CAAC,EAAE;MACVxC,OAAO,CAACyC,KAAK,CAAC,2CAA2C,EAAED,CAAC,CAAC;;EAEjE;EAEA;;;;EAIQ,MAAM4Q,iBAAiBA,CAAA;IAC7B,MAAM,IAAI,CAACC,gBAAgB,EAAE;IAE7B,IAAI,CAACxQ,MAAM,CAAC,sBAAsB,CAAC;IAEnC,MAAMyQ,MAAM,GAAGC,WAAW,CAAC,MAAM,IAAI,CAACC,qBAAqB,EAAE,EAAEpY,6BAA6B,CAAC;IAC7F,IAAI,CAACmE,iBAAiB,GAAG+T,MAAM;IAE/B,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACG,KAAK,KAAK,UAAU,EAAE;MAC9E;MACA;MACA;MACA;MACA;MACA;MACAH,MAAM,CAACG,KAAK,EAAE;MACd;KACD,MAAM,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,CAACC,UAAU,KAAK,UAAU,EAAE;MAC/E;MACA;MACA;MACAD,IAAI,CAACC,UAAU,CAACL,MAAM,CAAC;;IAGzB;IACA;IACA;IACA1O,UAAU,CAAC,YAAW;MACpB,MAAM,IAAI,CAAClF,iBAAiB;MAC5B,MAAM,IAAI,CAAC8T,qBAAqB,EAAE;IACpC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;;EAIQ,MAAMH,gBAAgBA,CAAA;IAC5B,IAAI,CAACxQ,MAAM,CAAC,qBAAqB,CAAC;IAElC,MAAMyQ,MAAM,GAAG,IAAI,CAAC/T,iBAAiB;IACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAE7B,IAAI+T,MAAM,EAAE;MACVM,aAAa,CAACN,MAAM,CAAC;;EAEzB;EAEA;;;;;;;;;;;;;;;;;;;;;;EAsBA,MAAMO,gBAAgBA,CAAA;IACpB,IAAI,CAACX,gCAAgC,EAAE;IACvC,MAAM,IAAI,CAACE,iBAAiB,EAAE;EAChC;EAEA;;;;;;;;EAQA,MAAMU,eAAeA,CAAA;IACnB,IAAI,CAACZ,gCAAgC,EAAE;IACvC,MAAM,IAAI,CAACG,gBAAgB,EAAE;EAC/B;EAEA;;;EAGQ,MAAMG,qBAAqBA,CAAA;IACjC,IAAI,CAAC3Q,MAAM,CAAC,0BAA0B,EAAE,OAAO,CAAC;IAEhD,IAAI;MACF,MAAM,IAAI,CAACe,YAAY,CAAC,CAAC,EAAE,YAAW;QACpC,IAAI;UACF,MAAMiJ,GAAG,GAAGnJ,IAAI,CAACmJ,GAAG,EAAE;UAEtB,IAAI;YACF,OAAO,MAAM,IAAI,CAACrB,WAAW,CAAC,MAAOC,MAAM,IAAI;cAC7C,MAAM;gBACJ1I,IAAI,EAAE;kBAAEC;gBAAO;cAAE,CAClB,GAAGyI,MAAM;cAEV,IAAI,CAACzI,OAAO,IAAI,CAACA,OAAO,CAACyK,aAAa,IAAI,CAACzK,OAAO,CAAC4J,UAAU,EAAE;gBAC7D,IAAI,CAAC/J,MAAM,CAAC,0BAA0B,EAAE,YAAY,CAAC;gBACrD;;cAGF;cACA,MAAMkR,cAAc,GAAGzE,IAAI,CAAC0E,KAAK,CAC/B,CAAChR,OAAO,CAAC4J,UAAU,GAAG,IAAI,GAAGC,GAAG,IAAIzR,6BAA6B,CAClE;cAED,IAAI,CAACyH,MAAM,CACT,0BAA0B,EAC1B,2BAA2BkR,cAAc,wBAAwB3Y,6BAA6B,4BAA4BC,2BAA2B,QAAQ,CAC9J;cAED,IAAI0Y,cAAc,IAAI1Y,2BAA2B,EAAE;gBACjD,MAAM,IAAI,CAACmS,iBAAiB,CAACxK,OAAO,CAACyK,aAAa,CAAC;;YAEvD,CAAC,CAAC;WACH,CAAC,OAAOjL,CAAM,EAAE;YACfxC,OAAO,CAACyC,KAAK,CACX,wEAAwE,EACxED,CAAC,CACF;;SAEJ,SAAS;UACR,IAAI,CAACK,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC;;MAElD,CAAC,CAAC;KACH,CAAC,OAAOL,CAAM,EAAE;MACf,IAAIA,CAAC,CAACyR,gBAAgB,IAAIzR,CAAC,YAAY3E,uBAAuB,EAAE;QAC9D,IAAI,CAACgF,MAAM,CAAC,4CAA4C,CAAC;OAC1D,MAAM;QACL,MAAML,CAAC;;;EAGb;EAEA;;;;;EAKQ,MAAMsC,uBAAuBA,CAAA;IACnC,IAAI,CAACjC,MAAM,CAAC,4BAA4B,CAAC;IAEzC,IAAI,CAACjG,SAAS,EAAE,IAAI,EAACmH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEpB,gBAAgB,GAAE;MAC7C,IAAI,IAAI,CAACvE,gBAAgB,EAAE;QACzB;QACA,IAAI,CAACyV,gBAAgB,EAAE;;MAGzB,OAAO,KAAK;;IAGd,IAAI;MACF,IAAI,CAACrU,yBAAyB,GAAG,YAAY,MAAM,IAAI,CAAC0U,oBAAoB,CAAC,KAAK,CAAC;MAEnFnQ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEpB,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAACnD,yBAAyB,CAAC;MAE5E;MACA;MACA,MAAM,IAAI,CAAC0U,oBAAoB,CAAC,IAAI,CAAC,EAAC;KACvC,CAAC,OAAOzR,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;EAEnD;EAEA;;;EAGQ,MAAMyR,oBAAoBA,CAACC,oBAA6B;IAC9D,MAAMC,UAAU,GAAG,yBAAyBD,oBAAoB,GAAG;IACnE,IAAI,CAACtR,MAAM,CAACuR,UAAU,EAAE,iBAAiB,EAAEC,QAAQ,CAACC,eAAe,CAAC;IAEpE,IAAID,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;MAC1C,IAAI,IAAI,CAAClW,gBAAgB,EAAE;QACzB;QACA;QACA,IAAI,CAACgV,iBAAiB,EAAE;;MAG1B,IAAI,CAACe,oBAAoB,EAAE;QACzB;QACA;QACA;QACA;QACA,MAAM,IAAI,CAACzU,iBAAiB;QAE5B,MAAM,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;UACrC,IAAIyQ,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;YAC1C,IAAI,CAACzR,MAAM,CACTuR,UAAU,EACV,0GAA0G,CAC3G;YAED;YACA;;UAGF;UACA,MAAM,IAAI,CAACvP,kBAAkB,EAAE;QACjC,CAAC,CAAC;;KAEL,MAAM,IAAIwP,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;MAChD,IAAI,IAAI,CAAClW,gBAAgB,EAAE;QACzB,IAAI,CAACiV,gBAAgB,EAAE;;;EAG7B;EAEA;;;;;;EAMQ,MAAMtC,kBAAkBA,CAC9B7S,GAAW,EACXuI,QAAkB,EAClBvH,OAKC;IAED,MAAMqV,SAAS,GAAa,CAAC,YAAYC,kBAAkB,CAAC/N,QAAQ,CAAC,EAAE,CAAC;IACxE,IAAIvH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4G,UAAU,EAAE;MACvByO,SAAS,CAACrI,IAAI,CAAC,eAAesI,kBAAkB,CAACtV,OAAO,CAAC4G,UAAU,CAAC,EAAE,CAAC;;IAEzE,IAAI5G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwH,MAAM,EAAE;MACnB6N,SAAS,CAACrI,IAAI,CAAC,UAAUsI,kBAAkB,CAACtV,OAAO,CAACwH,MAAM,CAAC,EAAE,CAAC;;IAEhE,IAAI,IAAI,CAAClI,QAAQ,KAAK,MAAM,EAAE;MAC5B,MAAM,CAACoH,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMzI,yBAAyB,CAC1E,IAAI,CAACiF,OAAO,EACZ,IAAI,CAAClE,UAAU,CAChB;MAED,MAAMsW,UAAU,GAAG,IAAIC,eAAe,CAAC;QACrC1O,cAAc,EAAE,GAAGwO,kBAAkB,CAAC5O,aAAa,CAAC,EAAE;QACtDK,qBAAqB,EAAE,GAAGuO,kBAAkB,CAAC3O,mBAAmB,CAAC;OAClE,CAAC;MACF0O,SAAS,CAACrI,IAAI,CAACuI,UAAU,CAACtF,QAAQ,EAAE,CAAC;;IAEvC,IAAIjQ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyH,WAAW,EAAE;MACxB,MAAMgO,KAAK,GAAG,IAAID,eAAe,CAACxV,OAAO,CAACyH,WAAW,CAAC;MACtD4N,SAAS,CAACrI,IAAI,CAACyI,KAAK,CAACxF,QAAQ,EAAE,CAAC;;IAElC,IAAIjQ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0H,mBAAmB,EAAE;MAChC2N,SAAS,CAACrI,IAAI,CAAC,sBAAsBhN,OAAO,CAAC0H,mBAAmB,EAAE,CAAC;;IAGrE,OAAO,GAAG1I,GAAG,IAAIqW,SAAS,CAAC7K,IAAI,CAAC,GAAG,CAAC,EAAE;EACxC;EAEQ,MAAM9H,SAASA,CAACkC,MAAyB;IAC/C,IAAI;MACF,OAAO,MAAM,IAAI,CAAC0H,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAE1I,IAAI,EAAEgL,WAAW;UAAEtL,KAAK,EAAEiJ;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAE3I,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAEiJ;UAAY,CAAE;;QAG5C,OAAO,MAAMrP,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAACxC,GAAG,YAAY4F,MAAM,CAAC8Q,QAAQ,EAAE,EAAE;UACpFrW,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE,CAAA/K,EAAA,GAAAmN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE/K,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J;SAC5B,CAAC;MACJ,CAAC,CAAC;KACH,CAAC,OAAO9H,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAOQ,MAAMf,OAAOA,CAACoC,MAAuB;IAC3C,IAAI;MACF,OAAO,MAAM,IAAI,CAAC0H,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAE1I,IAAI,EAAEgL,WAAW;UAAEtL,KAAK,EAAEiJ;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAE3I,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAEiJ;UAAY,CAAE;;QAG5C,MAAMxG,IAAI,GAAA5E,MAAA,CAAAC,MAAA;UACRsU,aAAa,EAAE/Q,MAAM,CAACgR,YAAY;UAClCC,WAAW,EAAEjR,MAAM,CAACkR;QAAU,GAC1BlR,MAAM,CAACkR,UAAU,KAAK,OAAO,GAAG;UAAE9O,KAAK,EAAEpC,MAAM,CAACoC;QAAK,CAAE,GAAG;UAAE+O,MAAM,EAAEnR,MAAM,CAACmR;QAAM,CAAG,CACzF;QAED,MAAM;UAAElS,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACxC,GAAG,UAAU,EAAE;UAChFgH,IAAI;UACJ3G,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBoN,GAAG,EAAE,CAAA/K,EAAA,GAAAmN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE/K,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J;SAC5B,CAAC;QAEF,IAAI9H,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAG9B,IAAIqB,MAAM,CAACkR,UAAU,KAAK,MAAM,KAAI,CAAAtS,EAAA,GAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmS,IAAI,cAAAxS,EAAA,uBAAAA,EAAA,CAAEyS,OAAO,GAAE;UACvDpS,IAAI,CAACmS,IAAI,CAACC,OAAO,GAAG,4BAA4BpS,IAAI,CAACmS,IAAI,CAACC,OAAO,EAAE;;QAGrE,OAAO;UAAEpS,IAAI;UAAEN,KAAK,EAAE;QAAI,CAAE;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;EAGQ,MAAMlB,OAAOA,CAACuC,MAAuB;IAC3C,OAAO,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,IAAI;QACF,OAAO,MAAM,IAAI,CAAC4H,WAAW,CAAC,MAAOC,MAAM,IAAI;;UAC7C,MAAM;YAAE1I,IAAI,EAAEgL,WAAW;YAAEtL,KAAK,EAAEiJ;UAAY,CAAE,GAAGD,MAAM;UACzD,IAAIC,YAAY,EAAE;YAChB,OAAO;cAAE3I,IAAI,EAAE,IAAI;cAAEN,KAAK,EAAEiJ;YAAY,CAAE;;UAG5C,MAAM;YAAE3I,IAAI;YAAEN;UAAK,CAAE,GAAG,MAAMpG,QAAQ,CACpC,IAAI,CAACqE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACxC,GAAG,YAAY4F,MAAM,CAAC8Q,QAAQ,SAAS,EAC/C;YACE1P,IAAI,EAAE;cAAEV,IAAI,EAAEV,MAAM,CAACU,IAAI;cAAE4Q,YAAY,EAAEtR,MAAM,CAACuR;YAAW,CAAE;YAC7D9W,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBoN,GAAG,EAAE,CAAA/K,EAAA,GAAAmN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE/K,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J;WAC5B,CACF;UACD,IAAI9H,KAAK,EAAE;YACT,OAAO;cAAEM,IAAI,EAAE,IAAI;cAAEN;YAAK,CAAE;;UAG9B,MAAM,IAAI,CAACkC,YAAY,CAAArE,MAAA,CAAAC,MAAA;YACrBqM,UAAU,EAAE0C,IAAI,CAACC,KAAK,CAAC7L,IAAI,CAACmJ,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG9J,IAAI,CAAC0L;UAAU,GACxD1L,IAAI,EACP;UACF,MAAM,IAAI,CAACD,qBAAqB,CAAC,wBAAwB,EAAEC,IAAI,CAAC;UAEhE,OAAO;YAAEA,IAAI;YAAEN;UAAK,CAAE;QACxB,CAAC,CAAC;OACH,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;UACtB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9B,MAAMA,KAAK;;IAEf,CAAC,CAAC;EACJ;EAEA;;;EAGQ,MAAMX,UAAUA,CAACgC,MAA0B;IACjD,OAAO,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,IAAI;QACF,OAAO,MAAM,IAAI,CAAC4H,WAAW,CAAC,MAAOC,MAAM,IAAI;;UAC7C,MAAM;YAAE1I,IAAI,EAAEgL,WAAW;YAAEtL,KAAK,EAAEiJ;UAAY,CAAE,GAAGD,MAAM;UACzD,IAAIC,YAAY,EAAE;YAChB,OAAO;cAAE3I,IAAI,EAAE,IAAI;cAAEN,KAAK,EAAEiJ;YAAY,CAAE;;UAG5C,OAAO,MAAMrP,QAAQ,CACnB,IAAI,CAACqE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACxC,GAAG,YAAY4F,MAAM,CAAC8Q,QAAQ,YAAY,EAClD;YACE1P,IAAI,EAAE;cAAEiB,OAAO,EAAErC,MAAM,CAACqC;YAAO,CAAE;YACjC5H,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBoN,GAAG,EAAE,CAAA/K,EAAA,GAAAmN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE/K,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAE2J;WAC5B,CACF;QACH,CAAC,CAAC;OACH,CAAC,OAAO9H,KAAK,EAAE;QACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;UACtB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9B,MAAMA,KAAK;;IAEf,CAAC,CAAC;EACJ;EAEA;;;EAGQ,MAAMP,mBAAmBA,CAC/B4B,MAAmC;IAEnC;IACA;IAEA,MAAM;MAAEf,IAAI,EAAEuS,aAAa;MAAE7S,KAAK,EAAE8S;IAAc,CAAE,GAAG,MAAM,IAAI,CAACzT,UAAU,CAAC;MAC3E8S,QAAQ,EAAE9Q,MAAM,CAAC8Q;KAClB,CAAC;IACF,IAAIW,cAAc,EAAE;MAClB,OAAO;QAAExS,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAE8S;MAAc,CAAE;;IAG9C,OAAO,MAAM,IAAI,CAAChU,OAAO,CAAC;MACxBqT,QAAQ,EAAE9Q,MAAM,CAAC8Q,QAAQ;MACzBS,WAAW,EAAEC,aAAa,CAACjF,EAAE;MAC7B7L,IAAI,EAAEV,MAAM,CAACU;KACd,CAAC;EACJ;EAEA;;;EAGQ,MAAMxC,YAAYA,CAAA;IACxB;IACA,MAAM;MACJe,IAAI,EAAE;QAAEyC;MAAI,CAAE;MACd/C,KAAK,EAAEuL;IAAS,CACjB,GAAG,MAAM,IAAI,CAACN,OAAO,EAAE;IACxB,IAAIM,SAAS,EAAE;MACb,OAAO;QAAEjL,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAEuL;MAAS,CAAE;;IAGzC,MAAMwH,OAAO,GAAG,CAAAhQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgQ,OAAO,KAAI,EAAE;IACnC,MAAMN,IAAI,GAAGM,OAAO,CAACC,MAAM,CACxBC,MAAM,IAAKA,MAAM,CAACX,WAAW,KAAK,MAAM,IAAIW,MAAM,CAACxF,MAAM,KAAK,UAAU,CAC1E;IACD,MAAMhK,KAAK,GAAGsP,OAAO,CAACC,MAAM,CACzBC,MAAM,IAAKA,MAAM,CAACX,WAAW,KAAK,OAAO,IAAIW,MAAM,CAACxF,MAAM,KAAK,UAAU,CAC3E;IAED,OAAO;MACLnN,IAAI,EAAE;QACJqJ,GAAG,EAAEoJ,OAAO;QACZN,IAAI;QACJhP;OACD;MACDzD,KAAK,EAAE;KACR;EACH;EAEA;;;EAGQ,MAAML,+BAA+BA,CAAA;IAC3C,OAAO,IAAI,CAACwB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,OAAO,MAAM,IAAI,CAAC4H,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UACJ1I,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP,KAAK,EAAEiJ;QAAY,CACpB,GAAGD,MAAM;QACV,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAE3I,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAEiJ;UAAY,CAAE;;QAE5C,IAAI,CAAC1I,OAAO,EAAE;UACZ,OAAO;YACLD,IAAI,EAAE;cAAE4S,YAAY,EAAE,IAAI;cAAEC,SAAS,EAAE,IAAI;cAAEC,4BAA4B,EAAE;YAAE,CAAE;YAC/EpT,KAAK,EAAE;WACR;;QAGH,MAAM;UAAE4L;QAAO,CAAE,GAAG9Q,SAAS,CAACyF,OAAO,CAACuH,YAAY,CAAC;QAEnD,IAAIoL,YAAY,GAAwC,IAAI;QAE5D,IAAItH,OAAO,CAACyH,GAAG,EAAE;UACfH,YAAY,GAAGtH,OAAO,CAACyH,GAAG;;QAG5B,IAAIF,SAAS,GAAwCD,YAAY;QAEjE,MAAMI,eAAe,GACnB,CAAArT,EAAA,IAAA9B,EAAA,GAAAoC,OAAO,CAACwC,IAAI,CAACgQ,OAAO,cAAA5U,EAAA,uBAAAA,EAAA,CAAE6U,MAAM,CAAEC,MAAc,IAAKA,MAAM,CAACxF,MAAM,KAAK,UAAU,CAAC,cAAAxN,EAAA,cAAAA,EAAA,GAAI,EAAE;QAEtF,IAAIqT,eAAe,CAACzS,MAAM,GAAG,CAAC,EAAE;UAC9BsS,SAAS,GAAG,MAAM;;QAGpB,MAAMC,4BAA4B,GAAGxH,OAAO,CAAC2H,GAAG,IAAI,EAAE;QAEtD,OAAO;UAAEjT,IAAI,EAAE;YAAE4S,YAAY;YAAEC,SAAS;YAAEC;UAA4B,CAAE;UAAEpT,KAAK,EAAE;QAAI,CAAE;MACzF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQ,MAAMwT,QAAQA,CAACC,GAAW,EAAsC;IAAA,IAApClV,IAAA,GAAAqC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAAwB;MAAEpC,IAAI,EAAE;IAAE,CAAE;IACtE;IACA,IAAIkV,GAAG,GAAGnV,IAAI,CAACC,IAAI,CAACmV,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IAClD,IAAIC,GAAG,EAAE;MACP,OAAOA,GAAG;;IAGZ,MAAMtJ,GAAG,GAAGnJ,IAAI,CAACmJ,GAAG,EAAE;IAEtB;IACAsJ,GAAG,GAAG,IAAI,CAACnV,IAAI,CAACC,IAAI,CAACmV,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IAEnD;IACA,IAAIC,GAAG,IAAI,IAAI,CAACjV,cAAc,GAAG1F,QAAQ,GAAGqR,GAAG,EAAE;MAC/C,OAAOsJ,GAAG;;IAEZ;IACA,MAAM;MAAEpT,IAAI;MAAEN;IAAK,CAAE,GAAG,MAAMpG,QAAQ,CAAC,IAAI,CAACqE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACxC,GAAG,wBAAwB,EAAE;MAC7FK,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;IACF,IAAIkE,KAAK,EAAE;MACT,MAAMA,KAAK;;IAEb,IAAI,CAACM,IAAI,CAAC9B,IAAI,IAAI8B,IAAI,CAAC9B,IAAI,CAACqC,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,IAAI;;IAGb,IAAI,CAACtC,IAAI,GAAG+B,IAAI;IAChB,IAAI,CAAC7B,cAAc,GAAG2L,GAAG;IAEzB;IACAsJ,GAAG,GAAGpT,IAAI,CAAC9B,IAAI,CAACmV,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IACnD,IAAI,CAACC,GAAG,EAAE;MACR,OAAO,IAAI;;IAEb,OAAOA,GAAG;EACZ;EAEA;;;;;;;;;;;;;;;;EAgBA,MAAMG,SAASA,CACb3K,GAAY,EAYN;IAAA,IAXNzM,OAAA,GAAAmE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA0H,SAAA,GAAA1H,SAAA,MAWI,EAAE;IASN,IAAI;MACF,IAAIiH,KAAK,GAAGqB,GAAG;MACf,IAAI,CAACrB,KAAK,EAAE;QACV,MAAM;UAAEvH,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACqJ,UAAU,EAAE;QAC/C,IAAIrJ,KAAK,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE;UAC1B,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9B6H,KAAK,GAAGvH,IAAI,CAACC,OAAO,CAACuH,YAAY;;MAGnC,MAAM;QACJgM,MAAM;QACNlI,OAAO;QACP/G,SAAS;QACTkP,GAAG,EAAE;UAAED,MAAM,EAAEE,SAAS;UAAEpI,OAAO,EAAEqI;QAAU;MAAE,CAChD,GAAGnZ,SAAS,CAAC+M,KAAK,CAAC;MAEpB,IAAI,EAACpL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyX,YAAY,GAAE;QAC1B;QACArZ,WAAW,CAAC+Q,OAAO,CAACC,GAAG,CAAC;;MAG1B,MAAMsI,UAAU,GACd,CAACL,MAAM,CAACM,GAAG,IACXN,MAAM,CAACM,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,IAC3B,CAACP,MAAM,CAACL,GAAG,IACX,EAAE,QAAQ,IAAIrV,UAAU,IAAI,QAAQ,IAAIA,UAAU,CAACkW,MAAM,CAAC,GACtD,IAAI,GACJ,MAAM,IAAI,CAACd,QAAQ,CAACM,MAAM,CAACL,GAAG,EAAE,CAAAhX,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+B,IAAI,IAAG;QAAEA,IAAI,EAAE/B,OAAO,CAAC+B;MAAI,CAAE,GAAG/B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,IAAI,CAAC;MAE7F;MACA,IAAI,CAAC4V,UAAU,EAAE;QACf,MAAM;UAAEnU;QAAK,CAAE,GAAG,MAAM,IAAI,CAACiL,OAAO,CAACpD,KAAK,CAAC;QAC3C,IAAI7H,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEb;QACA,OAAO;UACLM,IAAI,EAAE;YACJiU,MAAM,EAAE3I,OAAO;YACfkI,MAAM;YACNjP;WACD;UACD7E,KAAK,EAAE;SACR;;MAGH,MAAMwU,SAAS,GAAG5Z,YAAY,CAACkZ,MAAM,CAACM,GAAG,CAAC;MAE1C;MACA,MAAMnO,SAAS,GAAG,MAAMqO,MAAM,CAACG,MAAM,CAACC,SAAS,CAAC,KAAK,EAAEP,UAAU,EAAEK,SAAS,EAAE,IAAI,EAAE,CAClF,QAAQ,CACT,CAAC;MAEF;MACA,MAAMG,OAAO,GAAG,MAAML,MAAM,CAACG,MAAM,CAAC5V,MAAM,CACxC2V,SAAS,EACTvO,SAAS,EACTpB,SAAS,EACTvJ,kBAAkB,CAAC,GAAG0Y,SAAS,IAAIC,UAAU,EAAE,CAAC,CACjD;MAED,IAAI,CAACU,OAAO,EAAE;QACZ,MAAM,IAAIhb,mBAAmB,CAAC,uBAAuB,CAAC;;MAGxD;MACA,OAAO;QACL2G,IAAI,EAAE;UACJiU,MAAM,EAAE3I,OAAO;UACfkI,MAAM;UACNjP;SACD;QACD7E,KAAK,EAAE;OACR;KACF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIzG,WAAW,CAACyG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;;AAp6FezD,YAAA,CAAAmB,cAAc,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}