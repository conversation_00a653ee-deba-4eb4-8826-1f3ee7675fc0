import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  AcademicCapIcon,
  PlayIcon,
  DocumentIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowRightIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

// Components
import ResponsiveGrid from '../common/ResponsiveGrid';
import ResponsiveCard from '../common/ResponsiveCard';
import ResponsiveButton from '../common/ResponsiveButton';
import ResponsiveText from '../common/ResponsiveText';
import LoadingSpinner from '../common/LoadingSpinner';

// Services
import { supabaseService } from '../../services/supabaseService';

// Types
import { Course, Student } from '../../types';

interface MyCoursesProps {
  user: Student;
}

const MyCourses: React.FC<MyCoursesProps> = ({ user }) => {
  const navigate = useNavigate();
  const [filter, setFilter] = useState('all');
  const [courses, setCourses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load user's enrolled courses
  useEffect(() => {
    loadEnrolledCourses();
  }, [user.id]);

  const loadEnrolledCourses = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get student enrollments with course details
      const enrollments = await supabaseService.getStudentEnrollments(user.id);
      setCourses(enrollments || []);
    } catch (error: any) {
      console.error('Error loading courses:', error);
      setError('حدث خطأ في تحميل الكورسات');
      toast.error('فشل في تحميل الكورسات');
    } finally {
      setLoading(false);
    }
  };

  const filteredCourses = courses.filter(enrollment => {
    if (filter === 'completed') {
      return enrollment.progress === 100;
    } else if (filter === 'in-progress') {
      return enrollment.progress > 0 && enrollment.progress < 100;
    }
    return true;
  });

  const handleCourseClick = (courseId: string) => {
    navigate(`/student/course/${courseId}`);
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    return 'bg-yellow-500';
  };

  const getStatusIcon = (progress: number) => {
    if (progress === 100) {
      return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
    }
    return <ClockIcon className="w-5 h-5 text-blue-600" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <AcademicCapIcon className="w-12 h-12 mx-auto mb-2" />
          <p>{error}</p>
        </div>
        <ResponsiveButton onClick={loadEnrolledCourses} variant="primary">
          إعادة المحاولة
        </ResponsiveButton>
      </div>
    );
  }

  return (
    <div className="container-responsive space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <ResponsiveText as="h1" size="2xl" weight="bold" color="gray">
            كورساتي
          </ResponsiveText>
          <ResponsiveText size="sm" color="gray" className="mt-1">
            تابع تقدمك في الكورسات المسجل بها
          </ResponsiveText>
        </div>

        {/* Stats Summary */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="text-center">
            <ResponsiveText size="lg" weight="bold" color="primary">
              {courses.length}
            </ResponsiveText>
            <ResponsiveText size="xs" color="gray">
              إجمالي الكورسات
            </ResponsiveText>
          </div>
          <div className="text-center">
            <ResponsiveText size="lg" weight="bold" color="accent">
              {courses.filter(c => c.progress === 100).length}
            </ResponsiveText>
            <ResponsiveText size="xs" color="gray">
              مكتملة
            </ResponsiveText>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <ResponsiveCard padding="md" className="bg-white">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2 space-x-reverse">
            <FunnelIcon className="w-5 h-5 text-gray-500" />
            <ResponsiveText size="sm" weight="medium" color="gray">
              تصفية الكورسات
            </ResponsiveText>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 sm:gap-4">
          <ResponsiveButton
            onClick={() => setFilter('all')}
            variant={filter === 'all' ? 'primary' : 'ghost'}
            size="sm"
            className="flex-1 sm:flex-none"
          >
            جميع الكورسات ({courses.length})
          </ResponsiveButton>
          <ResponsiveButton
            onClick={() => setFilter('in-progress')}
            variant={filter === 'in-progress' ? 'primary' : 'ghost'}
            size="sm"
            className="flex-1 sm:flex-none"
          >
            قيد التقدم ({courses.filter(c => c.progress > 0 && c.progress < 100).length})
          </ResponsiveButton>
          <ResponsiveButton
            onClick={() => setFilter('completed')}
            variant={filter === 'completed' ? 'primary' : 'ghost'}
            size="sm"
            className="flex-1 sm:flex-none"
          >
            مكتملة ({courses.filter(c => c.progress === 100).length})
          </ResponsiveButton>
        </div>
      </ResponsiveCard>

      {/* Courses Grid */}
      <ResponsiveGrid
        columns={{ mobile: 1, tablet: 2, desktop: 3 }}
        gap="md"
        animated={true}
        staggerChildren={true}
      >
        {filteredCourses.map((enrollment, index) => {
          const course = enrollment.courses;
          const progress = enrollment.progress || 0;
          const totalVideos = course?.videos?.length || 0;
          const completedVideos = Math.floor((progress / 100) * totalVideos);

          return (
            <ResponsiveCard
              key={enrollment.id}
              hover={true}
              animated={true}
              animationDelay={index * 0.1}
              onClick={() => handleCourseClick(course.id)}
              className="cursor-pointer"
            >
              <div className="space-y-4">
                {/* Course Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse flex-1">
                    <div className="p-2 bg-primary-100 rounded-lg flex-shrink-0">
                      <AcademicCapIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <ResponsiveText
                        as="h3"
                        size="base"
                        weight="semibold"
                        color="gray"
                        className="truncate"
                      >
                        {course.title}
                      </ResponsiveText>
                      <ResponsiveText
                        size="xs"
                        color="gray"
                        className="line-clamp-2 mt-1"
                      >
                        {course.description}
                      </ResponsiveText>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusIcon(progress)}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <ResponsiveText size="xs" color="gray">
                      التقدم
                    </ResponsiveText>
                    <ResponsiveText size="xs" weight="medium" color="gray">
                      {progress}%
                    </ResponsiveText>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress)}`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Course Stats */}
                <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600">
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <PlayIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>{completedVideos}/{totalVideos} فيديو</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <ClipboardDocumentListIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>{course?.quizzes?.length || 0} اختبار</span>
                  </div>
                </div>

                {/* Continue Button */}
                <ResponsiveButton
                  onClick={(e) => {
                    e?.stopPropagation();
                    handleCourseClick(course.id);
                  }}
                  variant="primary"
                  size="sm"
                  fullWidth={true}
                  icon={<ArrowRightIcon className="w-4 h-4" />}
                  iconPosition="left"
                >
                  {progress === 100 ? 'مراجعة الكورس' : 'متابعة التعلم'}
                </ResponsiveButton>
              </div>
            </ResponsiveCard>
          );
        })}
      </ResponsiveGrid>

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <ResponsiveText as="h3" size="lg" weight="medium" color="gray" className="mb-2">
            لا توجد كورسات
          </ResponsiveText>
          <ResponsiveText size="sm" color="gray">
            {filter === 'completed'
              ? 'لم تكمل أي كورسات بعد'
              : filter === 'in-progress'
              ? 'لا توجد كورسات قيد التقدم'
              : 'لم تسجل في أي كورسات بعد'
            }
          </ResponsiveText>
        </div>
      )}
    </div>
  );
};

export default MyCourses;
