{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(url) {\n    let {\n      headers = {},\n      schema,\n      fetch\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n    this.schemaName = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    const url = new URL(`${this.url}/${relation}`);\n    return new PostgrestQueryBuilder_1.default(url, {\n      headers: Object.assign({}, this.headers),\n      schema: this.schemaName,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn) {\n    let args = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let {\n      head = false,\n      get = false,\n      count\n    } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let method;\n    const url = new URL(`${this.url}/rpc/${fn}`);\n    let body;\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET';\n      Object.entries(args)\n      // params with undefined value needs to be filtered out, otherwise it'll\n      // show up as `?param=undefined`\n      .filter(_ref => {\n        let [_, value] = _ref;\n        return value !== undefined;\n      })\n      // array values need special syntax\n      .map(_ref2 => {\n        let [name, value] = _ref2;\n        return [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`];\n      }).forEach(_ref3 => {\n        let [name, value] = _ref3;\n        url.searchParams.append(name, value);\n      });\n    } else {\n      method = 'POST';\n      body = args;\n    }\n    const headers = Object.assign({}, this.headers);\n    if (count) {\n      headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestClient;", "map": {"version": 3, "names": ["PostgrestQueryBuilder_1", "__importDefault", "require", "PostgrestFilterBuilder_1", "constants_1", "PostgrestClient", "constructor", "url", "headers", "schema", "fetch", "arguments", "length", "undefined", "Object", "assign", "DEFAULT_HEADERS", "schemaName", "from", "relation", "URL", "default", "rpc", "fn", "args", "head", "get", "count", "method", "body", "entries", "filter", "_ref", "_", "value", "map", "_ref2", "name", "Array", "isArray", "join", "for<PERSON>ach", "_ref3", "searchParams", "append", "allowEmpty", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestClient.ts"], "sourcesContent": ["import PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\nimport { DEFAULT_HEADERS } from './constants'\nimport { Fetch, GenericSchema } from './types'\n\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nexport default class PostgrestClient<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n> {\n  url: string\n  headers: Record<string, string>\n  schemaName?: SchemaName\n  fetch?: Fetch\n\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(\n    url: string,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: SchemaName\n      fetch?: Fetch\n    } = {}\n  ) {\n    this.url = url\n    this.headers = { ...DEFAULT_HEADERS, ...headers }\n    this.schemaName = schema\n    this.fetch = fetch\n  }\n\n  from<\n    TableName extends string & keyof Schema['Tables'],\n    Table extends Schema['Tables'][TableName]\n  >(relation: TableName): PostgrestQueryBuilder<Schema, Table, TableName>\n  from<ViewName extends string & keyof Schema['Views'], View extends Schema['Views'][ViewName]>(\n    relation: ViewName\n  ): PostgrestQueryBuilder<Schema, View, ViewName>\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation: string): PostgrestQueryBuilder<Schema, any, any> {\n    const url = new URL(`${this.url}/${relation}`)\n    return new PostgrestQueryBuilder(url, {\n      headers: { ...this.headers },\n      schema: this.schemaName,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema<DynamicSchema extends string & keyof Database>(\n    schema: DynamicSchema\n  ): PostgrestClient<\n    Database,\n    DynamicSchema,\n    Database[DynamicSchema] extends GenericSchema ? Database[DynamicSchema] : any\n  > {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc<FnName extends string & keyof Schema['Functions'], Fn extends Schema['Functions'][FnName]>(\n    fn: FnName,\n    args: Fn['Args'] = {},\n    {\n      head = false,\n      get = false,\n      count,\n    }: {\n      head?: boolean\n      get?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<\n    Schema,\n    Fn['Returns'] extends any[]\n      ? Fn['Returns'][number] extends Record<string, unknown>\n        ? Fn['Returns'][number]\n        : never\n      : never,\n    Fn['Returns'],\n    FnName,\n    null\n  > {\n    let method: 'HEAD' | 'GET' | 'POST'\n    const url = new URL(`${this.url}/rpc/${fn}`)\n    let body: unknown | undefined\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET'\n      Object.entries(args)\n        // params with undefined value needs to be filtered out, otherwise it'll\n        // show up as `?param=undefined`\n        .filter(([_, value]) => value !== undefined)\n        // array values need special syntax\n        .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n        .forEach(([name, value]) => {\n          url.searchParams.append(name, value)\n        })\n    } else {\n      method = 'POST'\n      body = args\n    }\n\n    const headers = { ...this.headers }\n    if (count) {\n      headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<Fn['Returns']>)\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,MAAAA,uBAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,wBAAA,GAAAF,eAAA,CAAAC,OAAA;AAEA,MAAAE,WAAA,GAAAF,OAAA;AAGA;;;;;;;;;;AAUA,MAAqBG,eAAe;EAclC;EACA;;;;;;;;;EASAC,YACEC,GAAW,EASL;IAAA,IARN;MACEC,OAAO,GAAG,EAAE;MACZC,MAAM;MACNC;IAAK,IAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAKH,EAAE;IAEN,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAAM,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQX,WAAA,CAAAY,eAAe,GAAKR,OAAO,CAAE;IACjD,IAAI,CAACS,UAAU,GAAGR,MAAM;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EASA;;;;;EAKAQ,IAAIA,CAACC,QAAgB;IACnB,MAAMZ,GAAG,GAAG,IAAIa,GAAG,CAAC,GAAG,IAAI,CAACb,GAAG,IAAIY,QAAQ,EAAE,CAAC;IAC9C,OAAO,IAAInB,uBAAA,CAAAqB,OAAqB,CAACd,GAAG,EAAE;MACpCC,OAAO,EAAAM,MAAA,CAAAC,MAAA,KAAO,IAAI,CAACP,OAAO,CAAE;MAC5BC,MAAM,EAAE,IAAI,CAACQ,UAAU;MACvBP,KAAK,EAAE,IAAI,CAACA;KACb,CAAC;EACJ;EAEA;;;;;;;EAOAD,MAAMA,CACJA,MAAqB;IAMrB,OAAO,IAAIJ,eAAe,CAAC,IAAI,CAACE,GAAG,EAAE;MACnCC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM;MACNC,KAAK,EAAE,IAAI,CAACA;KACb,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBAY,GAAGA,CACDC,EAAU,EAUJ;IAAA,IATNC,IAAA,GAAAb,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAmB,EAAE;IAAA,IACrB;MACEc,IAAI,GAAG,KAAK;MACZC,GAAG,GAAG,KAAK;MACXC;IAAK,IAAAhB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAKH,EAAE;IAYN,IAAIiB,MAA+B;IACnC,MAAMrB,GAAG,GAAG,IAAIa,GAAG,CAAC,GAAG,IAAI,CAACb,GAAG,QAAQgB,EAAE,EAAE,CAAC;IAC5C,IAAIM,IAAyB;IAC7B,IAAIJ,IAAI,IAAIC,GAAG,EAAE;MACfE,MAAM,GAAGH,IAAI,GAAG,MAAM,GAAG,KAAK;MAC9BX,MAAM,CAACgB,OAAO,CAACN,IAAI;MACjB;MACA;MAAA,CACCO,MAAM,CAACC,IAAA;QAAA,IAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,GAAAF,IAAA;QAAA,OAAKE,KAAK,KAAKrB,SAAS;MAAA;MAC3C;MAAA,CACCsB,GAAG,CAACC,KAAA;QAAA,IAAC,CAACC,IAAI,EAAEH,KAAK,CAAC,GAAAE,KAAA;QAAA,OAAK,CAACC,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAG,IAAIA,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGN,KAAK,EAAE,CAAC;MAAA,EAAC,CAC1FO,OAAO,CAACC,KAAA,IAAkB;QAAA,IAAjB,CAACL,IAAI,EAAEH,KAAK,CAAC,GAAAQ,KAAA;QACrBnC,GAAG,CAACoC,YAAY,CAACC,MAAM,CAACP,IAAI,EAAEH,KAAK,CAAC;MACtC,CAAC,CAAC;KACL,MAAM;MACLN,MAAM,GAAG,MAAM;MACfC,IAAI,GAAGL,IAAI;;IAGb,MAAMhB,OAAO,GAAAM,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACP,OAAO,CAAE;IACnC,IAAImB,KAAK,EAAE;MACTnB,OAAO,CAAC,QAAQ,CAAC,GAAG,SAASmB,KAAK,EAAE;;IAGtC,OAAO,IAAIxB,wBAAA,CAAAkB,OAAsB,CAAC;MAChCO,MAAM;MACNrB,GAAG;MACHC,OAAO;MACPC,MAAM,EAAE,IAAI,CAACQ,UAAU;MACvBY,IAAI;MACJnB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmC,UAAU,EAAE;KACiC,CAAC;EAClD;;AAnKFC,OAAA,CAAAzB,OAAA,GAAAhB,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}