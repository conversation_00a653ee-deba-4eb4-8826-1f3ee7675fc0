{"ast": null, "code": "export const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return import('@supabase/node-fetch').then(_ref => {\n        let {\n          default: fetch\n        } = _ref;\n        return fetch(...args);\n      });\n    };\n  } else {\n    _fetch = fetch;\n  }\n  return function () {\n    return _fetch(...arguments);\n  };\n};", "map": {"version": 3, "names": ["resolveFetch", "customFetch", "_fetch", "fetch", "_len", "arguments", "length", "args", "Array", "_key", "then", "_ref", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\functions-js\\src\\helper.ts"], "sourcesContent": ["import { Fetch } from './types'\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n"], "mappings": "AAEA,OAAO,MAAMA,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAG,SAAAA,CAAA;MAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAA,OACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAACC,IAAA;QAAA,IAAC;UAAEC,OAAO,EAAET;QAAK,CAAE,GAAAQ,IAAA;QAAA,OAAKR,KAAK,CAAC,GAAGI,IAAI,CAAC;MAAA,EAAC;IAAA;GACrF,MAAM;IACLL,MAAM,GAAGC,KAAK;;EAEhB,OAAO;IAAA,OAAaD,MAAM,CAAC,GAAAG,SAAO,CAAC;EAAA;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}