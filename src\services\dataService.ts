import { db } from '../config/firebase';
import { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { Course, Student, Quiz, Certificate, Category } from '../types';
import { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';
import { mockStudents } from '../data/mockStudents';
import { supabaseService } from './supabaseService';

// Mock categories data
const mockCategories: Category[] = [
  {
    id: 'programming',
    name: 'البرمجة',
    description: 'كورسات البرمجة وتطوير البرمجيات',
    color: 'blue',
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'web',
    name: 'تطوير الويب',
    description: 'كورسات تطوير المواقع والتطبيقات',
    color: 'green',
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'ai',
    name: 'الذكاء الاصطناعي',
    description: 'كورسات الذكاء الاصطناعي وتعلم الآلة',
    color: 'purple',
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

class DataService {
  // Courses
  async getCourses(): Promise<Course[]> {
    try {
      // Try to get from Supabase first
      const supabaseCourses = await supabaseService.getAllCourses();
      if (supabaseCourses && supabaseCourses.length > 0) {
        // Transform Supabase data to match our Course type
        return supabaseCourses.map(course => ({
          id: course.id,
          title: course.title,
          description: course.description,
          categoryId: course.category_id || '',
          instructorId: course.instructor_id || '',
          thumbnail: course.thumbnail_url || '',
          price: course.price || 0,
          duration: course.duration_hours || 0,
          level: course.level || 'beginner',
          isActive: course.is_active,
          videos: course.videos?.map(video => ({
            id: video.id,
            title: video.title,
            url: video.video_url,
            duration: video.duration || 0,
            order: video.order_index || 0
          })) || [],
          pdfs: [], // Will be implemented later
          quizzes: course.quizzes?.map(quiz => quiz.id) || [],
          enrolledStudents: 0, // Will be calculated separately
          createdAt: new Date(course.created_at),
          updatedAt: new Date(course.updated_at || course.created_at)
        }));
      }

      // Fallback to mock data
      return mockCourses;
    } catch (error) {
      console.error('Error fetching courses:', error);
      // Fallback to mock data
      return mockCourses;
    }
  }

  async getCourse(id: string): Promise<Course | null> {
    try {
      // Try to get from Supabase first
      const supabaseCourse = await supabaseService.getCourseById(id);
      if (supabaseCourse) {
        // Transform Supabase data to match our Course type
        return {
          id: supabaseCourse.id,
          title: supabaseCourse.title,
          description: supabaseCourse.description,
          categoryId: supabaseCourse.category_id || '',
          instructorId: supabaseCourse.instructor_id || '',
          thumbnail: supabaseCourse.thumbnail_url || '',
          price: supabaseCourse.price || 0,
          duration: supabaseCourse.duration_hours || 0,
          level: supabaseCourse.level || 'beginner',
          isActive: supabaseCourse.is_active,
          videos: supabaseCourse.videos?.map(video => ({
            id: video.id,
            title: video.title,
            url: video.video_url,
            duration: video.duration || 0,
            order: video.order_index || 0
          })) || [],
          pdfs: [], // Will be implemented later
          quizzes: supabaseCourse.quizzes?.map(quiz => quiz.id) || [],
          enrolledStudents: 0, // Will be calculated separately
          createdAt: new Date(supabaseCourse.created_at),
          updatedAt: new Date(supabaseCourse.updated_at || supabaseCourse.created_at)
        };
      }

      // Fallback to mock data
      const course = mockCourses.find(c => c.id === id);
      return course || null;
    } catch (error) {
      console.error('Error fetching course:', error);
      // Fallback to mock data
      const course = mockCourses.find(c => c.id === id);
      return course || null;
    }
  }

  async addCourse(course: Omit<Course, 'id'>): Promise<string> {
    try {
      // Create course in Supabase
      const newCourse = await supabaseService.createCourse({
        title: course.title,
        description: course.description,
        categoryId: course.categoryId,
        instructorId: course.instructorId,
        thumbnailUrl: course.thumbnail,
        price: course.price,
        durationHours: course.duration,
        level: course.level
      });

      return newCourse.id;
    } catch (error) {
      console.error('Error adding course:', error);
      throw error;
    }
  }

  async updateCourse(id: string, course: Partial<Course>): Promise<void> {
    try {
      // Update course in Supabase
      await supabaseService.updateCourse(id, {
        title: course.title,
        description: course.description,
        categoryId: course.categoryId,
        thumbnailUrl: course.thumbnail,
        price: course.price,
        durationHours: course.duration,
        level: course.level,
        isActive: course.isActive
      });
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      // Delete course from Supabase
      await supabaseService.deleteCourse(id);
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  }

  // Students
  async getStudents(): Promise<Student[]> {
    try {
      return mockStudents;
    } catch (error) {
      console.error('Error fetching students:', error);
      return mockStudents;
    }
  }

  async getStudent(id: string): Promise<Student | null> {
    try {
      const student = mockStudents.find(s => s.id === id);
      return student || null;
    } catch (error) {
      console.error('Error fetching student:', error);
      return null;
    }
  }

  // Quizzes
  async getQuizzes(): Promise<Quiz[]> {
    try {
      return mockQuizzes;
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      return mockQuizzes;
    }
  }

  async getQuiz(id: string): Promise<Quiz | null> {
    try {
      const quiz = mockQuizzes.find(q => q.id === id);
      return quiz || null;
    } catch (error) {
      console.error('Error fetching quiz:', error);
      return null;
    }
  }

  // Certificates
  async getCertificates(): Promise<Certificate[]> {
    try {
      return mockCertificates;
    } catch (error) {
      console.error('Error fetching certificates:', error);
      return mockCertificates;
    }
  }

  async getStudentCertificates(studentId: string): Promise<Certificate[]> {
    try {
      return mockCertificates.filter(cert => cert.studentId === studentId);
    } catch (error) {
      console.error('Error fetching student certificates:', error);
      return [];
    }
  }

  // Categories
  async getCategories(): Promise<Category[]> {
    try {
      return mockCategories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      return mockCategories;
    }
  }

  async getCategory(id: string): Promise<Category | null> {
    try {
      const category = mockCategories.find(c => c.id === id);
      return category || null;
    } catch (error) {
      console.error('Error fetching category:', error);
      return null;
    }
  }

  async createCategory(category: Omit<Category, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'categories'), category);
      return docRef.id;
    } catch (error) {
      console.error('Error adding category:', error);
      throw error;
    }
  }

  async updateCategory(id: string, category: Partial<Category>): Promise<void> {
    try {
      await updateDoc(doc(db, 'categories', id), category);
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'categories', id));
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }

  // Analytics
  async getAnalytics() {
    return {
      totalStudents: mockStudents.length,
      totalCourses: mockCourses.length,
      totalQuizzes: mockQuizzes.length,
      totalCertificates: mockCertificates.length,
      revenue: mockCourses.length * 299, // Mock revenue calculation
      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)
    };
  }
}

export const dataService = new DataService();
