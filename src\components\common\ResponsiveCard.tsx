import React from 'react';
import { motion } from 'framer-motion';

interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  border?: boolean;
  hover?: boolean;
  animated?: boolean;
  animationDelay?: number;
  onClick?: () => void;
  gradient?: boolean;
}

const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className = '',
  padding = 'md',
  shadow = 'md',
  rounded = 'lg',
  border = true,
  hover = false,
  animated = false,
  animationDelay = 0,
  onClick,
  gradient = false
}) => {
  const baseClasses = 'bg-white transition-all duration-200';

  const paddingClasses = {
    none: '',
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8',
    xl: 'p-8 sm:p-10'
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  };

  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full'
  };

  const borderClass = border ? 'border border-gray-200' : '';
  const hoverClass = hover ? 'hover:shadow-lg hover:scale-105 cursor-pointer touch-manipulation' : '';
  const gradientClass = gradient ? 'bg-gradient-to-br from-white to-gray-50' : '';
  const clickableClass = onClick ? 'cursor-pointer touch-manipulation' : '';

  const cardClasses = `
    ${baseClasses}
    ${paddingClasses[padding]}
    ${shadowClasses[shadow]}
    ${roundedClasses[rounded]}
    ${borderClass}
    ${hoverClass}
    ${gradientClass}
    ${clickableClass}
    ${className}
  `.trim();

  const cardContent = (
    <div className={cardClasses} onClick={onClick}>
      {children}
    </div>
  );

  if (animated) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: animationDelay, duration: 0.6 }}
        whileHover={hover ? { y: -5 } : {}}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
};

export default ResponsiveCard;
