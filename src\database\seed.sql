-- منصة ALaa <PERSON> - البيانات الأولية
-- تم التطوير بواسطة فريق ALaa Abd <PERSON>hamied 2025

-- إدراج مدير افتراضي
INSERT INTO admins (email, name, password_hash, permissions) VALUES 
('<EMAIL>', 'ALaa Abd <PERSON>', '$2b$10$example_hash_here', ARRAY['all'])
ON CONFLICT (email) DO NOTHING;

-- إدراج الفئات الأساسية
INSERT INTO categories (name, description, icon, color) VALUES 
('البرمجة', 'كورسات البرمجة وتطوير البرمجيات', 'code', '#3B82F6'),
('التصميم', 'كورسات التصميم الجرافيكي وتصميم المواقع', 'design', '#10B981'),
('التسويق', 'كورسات التسويق الرقمي والإلكتروني', 'marketing', '#F59E0B'),
('الأعمال', 'كورسات إدارة الأعمال وريادة الأعمال', 'business', '#EF4444'),
('اللغات', 'كورسات تعلم اللغات المختلفة', 'language', '#8B5CF6')
ON CONFLICT DO NOTHING;

-- إدراج كورسات تجريبية
INSERT INTO courses (title, description, category_id, instructor_id, level, requirements, learning_outcomes) 
SELECT 
    'مقدمة في البرمجة',
    'تعلم أساسيات البرمجة من الصفر',
    c.id,
    a.id,
    'beginner',
    ARRAY['لا توجد متطلبات مسبقة', 'جهاز كمبيوتر', 'اتصال بالإنترنت'],
    ARRAY['فهم أساسيات البرمجة', 'كتابة برامج بسيطة', 'حل المشاكل البرمجية']
FROM categories c, admins a 
WHERE c.name = 'البرمجة' AND a.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

INSERT INTO courses (title, description, category_id, instructor_id, level, requirements, learning_outcomes) 
SELECT 
    'تصميم المواقع الحديثة',
    'تعلم تصميم مواقع ويب احترافية ومتجاوبة',
    c.id,
    a.id,
    'intermediate',
    ARRAY['معرفة أساسية بـ HTML و CSS', 'جهاز كمبيوتر', 'برنامج تصميم'],
    ARRAY['تصميم مواقع متجاوبة', 'استخدام أدوات التصميم الحديثة', 'تطبيق مبادئ UX/UI']
FROM categories c, admins a 
WHERE c.name = 'التصميم' AND a.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- إدراج طلاب تجريبيين
INSERT INTO students (access_code, name, email) VALUES 
('1234567', 'أحمد محمد', '<EMAIL>'),
('2345678', 'فاطمة علي', '<EMAIL>'),
('3456789', 'محمد حسن', '<EMAIL>'),
('4567890', 'نور الدين', '<EMAIL>'),
('5678901', 'سارة أحمد', '<EMAIL>')
ON CONFLICT (access_code) DO NOTHING;

-- إدراج فيديوهات تجريبية للكورس الأول
INSERT INTO videos (course_id, title, description, video_url, duration, order_index, is_free)
SELECT 
    c.id,
    'مقدمة عن البرمجة',
    'فيديو تعريفي عن عالم البرمجة',
    'https://example.com/video1.mp4',
    600,
    1,
    true
FROM courses c 
WHERE c.title = 'مقدمة في البرمجة'
ON CONFLICT DO NOTHING;

INSERT INTO videos (course_id, title, description, video_url, duration, order_index, is_free)
SELECT 
    c.id,
    'أساسيات المتغيرات',
    'تعلم كيفية استخدام المتغيرات في البرمجة',
    'https://example.com/video2.mp4',
    900,
    2,
    false
FROM courses c 
WHERE c.title = 'مقدمة في البرمجة'
ON CONFLICT DO NOTHING;

INSERT INTO videos (course_id, title, description, video_url, duration, order_index, is_free)
SELECT 
    c.id,
    'الحلقات والشروط',
    'فهم الحلقات والشروط في البرمجة',
    'https://example.com/video3.mp4',
    1200,
    3,
    false
FROM courses c 
WHERE c.title = 'مقدمة في البرمجة'
ON CONFLICT DO NOTHING;

-- إدراج اختبار تجريبي
INSERT INTO quizzes (course_id, title, description, passing_score, time_limit)
SELECT 
    c.id,
    'اختبار أساسيات البرمجة',
    'اختبار لقياس فهمك لأساسيات البرمجة',
    70,
    15
FROM courses c 
WHERE c.title = 'مقدمة في البرمجة'
ON CONFLICT DO NOTHING;

-- إدراج أسئلة الاختبار
INSERT INTO questions (quiz_id, question, options, correct_answer, points, order_index)
SELECT 
    q.id,
    'ما هو المتغير في البرمجة؟',
    '["مكان لتخزين البيانات", "نوع من الحلقات", "دالة رياضية", "أمر طباعة"]'::jsonb,
    0,
    1,
    1
FROM quizzes q 
WHERE q.title = 'اختبار أساسيات البرمجة'
ON CONFLICT DO NOTHING;

INSERT INTO questions (quiz_id, question, options, correct_answer, points, order_index)
SELECT 
    q.id,
    'أي من التالي يستخدم للتكرار؟',
    '["if", "for", "print", "var"]'::jsonb,
    1,
    1,
    2
FROM quizzes q 
WHERE q.title = 'اختبار أساسيات البرمجة'
ON CONFLICT DO NOTHING;

-- تسجيل بعض الطلاب في الكورسات
INSERT INTO student_enrollments (student_id, course_id, progress)
SELECT s.id, c.id, 25
FROM students s, courses c 
WHERE s.access_code = '1234567' AND c.title = 'مقدمة في البرمجة'
ON CONFLICT (student_id, course_id) DO NOTHING;

INSERT INTO student_enrollments (student_id, course_id, progress)
SELECT s.id, c.id, 50
FROM students s, courses c 
WHERE s.access_code = '2345678' AND c.title = 'مقدمة في البرمجة'
ON CONFLICT (student_id, course_id) DO NOTHING;
