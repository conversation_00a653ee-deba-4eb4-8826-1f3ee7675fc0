const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 تشغيل برنامج إدخال البيانات...');

try {
  // Compile and run the TypeScript seeding script
  const scriptPath = path.join(__dirname, 'seedDatabase.ts');
  
  // Use ts-node to run the TypeScript file directly
  execSync(`npx ts-node ${scriptPath}`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '../..')
  });
  
  console.log('✅ تم تشغيل برنامج إدخال البيانات بنجاح');
} catch (error) {
  console.error('❌ فشل في تشغيل برنامج إدخال البيانات:', error.message);
  process.exit(1);
}
