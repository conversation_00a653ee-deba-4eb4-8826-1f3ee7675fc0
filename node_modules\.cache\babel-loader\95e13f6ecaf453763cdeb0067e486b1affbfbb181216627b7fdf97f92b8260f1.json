{"ast": null, "code": "import{signInWithEmailAndPassword,signOut,onAuthStateChanged}from'firebase/auth';import{doc,getDoc}from'firebase/firestore';import{auth,db}from'../config/firebase';import{defaultAdmin,adminCredentials}from'../data/defaultAdmin';import{mockStudents}from'../data/mockStudents';import{supabaseService}from'./supabaseService';class AuthService{// Admin login\nasync loginAdmin(email,password){try{var _adminData$createdAt;// Check for default admin credentials\nif(email===adminCredentials.email&&password===adminCredentials.password){return defaultAdmin;}const userCredential=await signInWithEmailAndPassword(auth,email,password);const user=userCredential.user;// Get admin data from Firestore\nconst adminDoc=await getDoc(doc(db,'admins',user.uid));if(!adminDoc.exists()){throw new Error('المستخدم غير مخول كمدير');}const adminData=adminDoc.data();return{id:user.uid,email:user.email,role:'admin',name:adminData.name,avatar:adminData.avatar,permissions:adminData.permissions||[],createdAt:((_adminData$createdAt=adminData.createdAt)===null||_adminData$createdAt===void 0?void 0:_adminData$createdAt.toDate())||new Date()};}catch(error){throw new Error(this.getErrorMessage(error.code));}}// Student login with access code\nasync loginStudent(accessCode){try{var _studentData$student_,_studentData$student_2,_studentData$certific;// Check for mock students first (for backward compatibility)\nconst mockStudent=mockStudents.find(student=>student.accessCode===accessCode);if(mockStudent){return mockStudent;}// Find student by access code in Supabase\nconst studentData=await supabaseService.getStudentByAccessCode(accessCode);if(!studentData){throw new Error('كود الدخول غير صحيح');}if(!studentData.is_active){throw new Error('الحساب غير مفعل');}// Transform Supabase data to match our Student type\nconst enrolledCourses=((_studentData$student_=studentData.student_enrollments)===null||_studentData$student_===void 0?void 0:_studentData$student_.map(enrollment=>enrollment.course_id))||[];const completedCourses=((_studentData$student_2=studentData.student_enrollments)===null||_studentData$student_2===void 0?void 0:_studentData$student_2.filter(enrollment=>enrollment.completed_at).map(enrollment=>enrollment.course_id))||[];const certificates=((_studentData$certific=studentData.certificates)===null||_studentData$certific===void 0?void 0:_studentData$certific.map(cert=>cert.id))||[];return{id:studentData.id,email:studentData.email||'',role:'student',name:studentData.name||'',avatar:studentData.avatar_url||'',accessCode:studentData.access_code,enrolledCourses,completedCourses,certificates,createdAt:new Date(studentData.created_at)};}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Generate access code for student\ngenerateAccessCode(){return Math.floor(1000000+Math.random()*9000000).toString();}// Create student account\nasync createStudent(studentData){try{const accessCode=this.generateAccessCode();// Create student in Supabase\nconst newStudent=await supabaseService.createStudent({accessCode:accessCode,name:studentData.name,email:studentData.email});// Enroll student in courses if provided\nif(studentData.enrolledCourses&&studentData.enrolledCourses.length>0){for(const courseId of studentData.enrolledCourses){await supabaseService.enrollStudent(newStudent.id,courseId);}}return accessCode;}catch(error){// If access code already exists, try again\nif(error.message.includes('duplicate key')){return this.createStudent(studentData);}throw new Error('فشل في إنشاء حساب الطالب');}}// Logout\nasync logout(){try{await signOut(auth);}catch(error){throw new Error('فشل في تسجيل الخروج');}}// Get current user\ngetCurrentUser(){return new Promise(resolve=>{const unsubscribe=onAuthStateChanged(auth,user=>{unsubscribe();resolve(user);});});}// Auth state listener\nonAuthStateChange(callback){return onAuthStateChanged(auth,callback);}getErrorMessage(errorCode){switch(errorCode){case'auth/user-not-found':return'المستخدم غير موجود';case'auth/wrong-password':return'كلمة المرور غير صحيحة';case'auth/invalid-email':return'البريد الإلكتروني غير صحيح';case'auth/user-disabled':return'الحساب معطل';case'auth/too-many-requests':return'محاولات كثيرة، حاول مرة أخرى لاحقاً';default:return'حدث خطأ في تسجيل الدخول';}}}export const authService=new AuthService();", "map": {"version": 3, "names": ["signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "doc", "getDoc", "auth", "db", "defaultAdmin", "adminCredentials", "mockStudents", "supabaseService", "AuthService", "loginAdmin", "email", "password", "_adminData$createdAt", "userCredential", "user", "adminDoc", "uid", "exists", "Error", "adminData", "data", "id", "role", "name", "avatar", "permissions", "createdAt", "toDate", "Date", "error", "getErrorMessage", "code", "loginStudent", "accessCode", "_studentData$student_", "_studentData$student_2", "_studentData$certific", "mockStudent", "find", "student", "studentData", "getStudentByAccessCode", "is_active", "enrolledCourses", "student_enrollments", "map", "enrollment", "course_id", "completedCourses", "filter", "completed_at", "certificates", "cert", "avatar_url", "access_code", "created_at", "message", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "newStudent", "length", "courseId", "enrollStudent", "includes", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "onAuthStateChange", "callback", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import {\n  signInWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\nimport { defaultAdmin, adminCredentials } from '../data/defaultAdmin';\nimport { mockStudents, studentCredentials } from '../data/mockStudents';\nimport { supabaseService } from './supabaseService';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      // Check for default admin credentials\n      if (email === adminCredentials.email && password === adminCredentials.password) {\n        return defaultAdmin as Admin;\n      }\n\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      const user = userCredential.user;\n      \n      // Get admin data from Firestore\n      const adminDoc = await getDoc(doc(db, 'admins', user.uid));\n      if (!adminDoc.exists()) {\n        throw new Error('المستخدم غير مخول كمدير');\n      }\n      \n      const adminData = adminDoc.data();\n      return {\n        id: user.uid,\n        email: user.email!,\n        role: 'admin',\n        name: adminData.name,\n        avatar: adminData.avatar,\n        permissions: adminData.permissions || [],\n        createdAt: adminData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(this.getErrorMessage(error.code));\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Check for mock students first (for backward compatibility)\n      const mockStudent = mockStudents.find(student => student.accessCode === accessCode);\n      if (mockStudent) {\n        return mockStudent;\n      }\n\n      // Find student by access code in Supabase\n      const studentData = await supabaseService.getStudentByAccessCode(accessCode);\n\n      if (!studentData) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n\n      if (!studentData.is_active) {\n        throw new Error('الحساب غير مفعل');\n      }\n\n      // Transform Supabase data to match our Student type\n      const enrolledCourses = studentData.student_enrollments?.map((enrollment: any) => enrollment.course_id) || [];\n      const completedCourses = studentData.student_enrollments?.filter((enrollment: any) => enrollment.completed_at).map((enrollment: any) => enrollment.course_id) || [];\n      const certificates = studentData.certificates?.map((cert: any) => cert.id) || [];\n\n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name || '',\n        avatar: studentData.avatar_url || '',\n        accessCode: studentData.access_code,\n        enrolledCourses,\n        completedCourses,\n        certificates,\n        createdAt: new Date(studentData.created_at)\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n\n      // Create student in Supabase\n      const newStudent = await supabaseService.createStudent({\n        accessCode: accessCode,\n        name: studentData.name,\n        email: studentData.email\n      });\n\n      // Enroll student in courses if provided\n      if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {\n        for (const courseId of studentData.enrolledCourses) {\n          await supabaseService.enrollStudent(newStudent.id, courseId);\n        }\n      }\n\n      return accessCode;\n    } catch (error: any) {\n      // If access code already exists, try again\n      if (error.message.includes('duplicate key')) {\n        return this.createStudent(studentData);\n      }\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,OACEA,0BAA0B,CAC1BC,OAAO,CACPC,kBAAkB,KAEb,eAAe,CACtB,OAASC,GAAG,CAAEC,MAAM,KAAgB,oBAAoB,CACxD,OAASC,IAAI,CAAEC,EAAE,KAAQ,oBAAoB,CAE7C,OAASC,YAAY,CAAEC,gBAAgB,KAAQ,sBAAsB,CACrE,OAASC,YAAY,KAA4B,sBAAsB,CACvE,OAASC,eAAe,KAAQ,mBAAmB,CAEnD,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAa,CAAEC,QAAgB,CAAkB,CAChE,GAAI,KAAAC,oBAAA,CACF;AACA,GAAIF,KAAK,GAAKL,gBAAgB,CAACK,KAAK,EAAIC,QAAQ,GAAKN,gBAAgB,CAACM,QAAQ,CAAE,CAC9E,MAAO,CAAAP,YAAY,CACrB,CAEA,KAAM,CAAAS,cAAc,CAAG,KAAM,CAAAhB,0BAA0B,CAACK,IAAI,CAAEQ,KAAK,CAAEC,QAAQ,CAAC,CAC9E,KAAM,CAAAG,IAAI,CAAGD,cAAc,CAACC,IAAI,CAEhC;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAd,MAAM,CAACD,GAAG,CAACG,EAAE,CAAE,QAAQ,CAAEW,IAAI,CAACE,GAAG,CAAC,CAAC,CAC1D,GAAI,CAACD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAE,CACtB,KAAM,IAAI,CAAAC,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CAEA,KAAM,CAAAC,SAAS,CAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACjC,MAAO,CACLC,EAAE,CAAEP,IAAI,CAACE,GAAG,CACZN,KAAK,CAAEI,IAAI,CAACJ,KAAM,CAClBY,IAAI,CAAE,OAAO,CACbC,IAAI,CAAEJ,SAAS,CAACI,IAAI,CACpBC,MAAM,CAAEL,SAAS,CAACK,MAAM,CACxBC,WAAW,CAAEN,SAAS,CAACM,WAAW,EAAI,EAAE,CACxCC,SAAS,CAAE,EAAAd,oBAAA,CAAAO,SAAS,CAACO,SAAS,UAAAd,oBAAA,iBAAnBA,oBAAA,CAAqBe,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CACvD,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,IAAI,CAACY,eAAe,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAC,YAAYA,CAACC,UAAkB,CAAoB,CACvD,GAAI,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CACF;AACA,KAAM,CAAAC,WAAW,CAAG/B,YAAY,CAACgC,IAAI,CAACC,OAAO,EAAIA,OAAO,CAACN,UAAU,GAAKA,UAAU,CAAC,CACnF,GAAII,WAAW,CAAE,CACf,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,KAAM,CAAAG,WAAW,CAAG,KAAM,CAAAjC,eAAe,CAACkC,sBAAsB,CAACR,UAAU,CAAC,CAE5E,GAAI,CAACO,WAAW,CAAE,CAChB,KAAM,IAAI,CAAAtB,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAEA,GAAI,CAACsB,WAAW,CAACE,SAAS,CAAE,CAC1B,KAAM,IAAI,CAAAxB,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAEA;AACA,KAAM,CAAAyB,eAAe,CAAG,EAAAT,qBAAA,CAAAM,WAAW,CAACI,mBAAmB,UAAAV,qBAAA,iBAA/BA,qBAAA,CAAiCW,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CAC7G,KAAM,CAAAC,gBAAgB,CAAG,EAAAb,sBAAA,CAAAK,WAAW,CAACI,mBAAmB,UAAAT,sBAAA,iBAA/BA,sBAAA,CAAiCc,MAAM,CAAEH,UAAe,EAAKA,UAAU,CAACI,YAAY,CAAC,CAACL,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CACnK,KAAM,CAAAI,YAAY,CAAG,EAAAf,qBAAA,CAAAI,WAAW,CAACW,YAAY,UAAAf,qBAAA,iBAAxBA,qBAAA,CAA0BS,GAAG,CAAEO,IAAS,EAAKA,IAAI,CAAC/B,EAAE,CAAC,GAAI,EAAE,CAEhF,MAAO,CACLA,EAAE,CAAEmB,WAAW,CAACnB,EAAE,CAClBX,KAAK,CAAE8B,WAAW,CAAC9B,KAAK,EAAI,EAAE,CAC9BY,IAAI,CAAE,SAAS,CACfC,IAAI,CAAEiB,WAAW,CAACjB,IAAI,EAAI,EAAE,CAC5BC,MAAM,CAAEgB,WAAW,CAACa,UAAU,EAAI,EAAE,CACpCpB,UAAU,CAAEO,WAAW,CAACc,WAAW,CACnCX,eAAe,CACfK,gBAAgB,CAChBG,YAAY,CACZzB,SAAS,CAAE,GAAI,CAAAE,IAAI,CAACY,WAAW,CAACe,UAAU,CAC5C,CAAC,CACH,CAAE,MAAO1B,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAAC2B,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACAC,kBAAkBA,CAAA,CAAW,CAC3B,MAAO,CAAAC,IAAI,CAACC,KAAK,CAAC,OAAO,CAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACtB,WAInB,CAAmB,CAClB,GAAI,CACF,KAAM,CAAAP,UAAU,CAAG,IAAI,CAACwB,kBAAkB,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAM,UAAU,CAAG,KAAM,CAAAxD,eAAe,CAACuD,aAAa,CAAC,CACrD7B,UAAU,CAAEA,UAAU,CACtBV,IAAI,CAAEiB,WAAW,CAACjB,IAAI,CACtBb,KAAK,CAAE8B,WAAW,CAAC9B,KACrB,CAAC,CAAC,CAEF;AACA,GAAI8B,WAAW,CAACG,eAAe,EAAIH,WAAW,CAACG,eAAe,CAACqB,MAAM,CAAG,CAAC,CAAE,CACzE,IAAK,KAAM,CAAAC,QAAQ,GAAI,CAAAzB,WAAW,CAACG,eAAe,CAAE,CAClD,KAAM,CAAApC,eAAe,CAAC2D,aAAa,CAACH,UAAU,CAAC1C,EAAE,CAAE4C,QAAQ,CAAC,CAC9D,CACF,CAEA,MAAO,CAAAhC,UAAU,CACnB,CAAE,MAAOJ,KAAU,CAAE,CACnB;AACA,GAAIA,KAAK,CAAC2B,OAAO,CAACW,QAAQ,CAAC,eAAe,CAAC,CAAE,CAC3C,MAAO,KAAI,CAACL,aAAa,CAACtB,WAAW,CAAC,CACxC,CACA,KAAM,IAAI,CAAAtB,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAEA;AACA,KAAM,CAAAkD,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAAtE,OAAO,CAACI,IAAI,CAAC,CACrB,CAAE,MAAO2B,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAmD,cAAcA,CAAA,CAAiC,CAC7C,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,WAAW,CAAGzE,kBAAkB,CAACG,IAAI,CAAGY,IAAI,EAAK,CACrD0D,WAAW,CAAC,CAAC,CACbD,OAAO,CAACzD,IAAI,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACA2D,iBAAiBA,CAACC,QAA6C,CAAE,CAC/D,MAAO,CAAA3E,kBAAkB,CAACG,IAAI,CAAEwE,QAAQ,CAAC,CAC3C,CAEQ5C,eAAeA,CAAC6C,SAAiB,CAAU,CACjD,OAAQA,SAAS,EACf,IAAK,qBAAqB,CACxB,MAAO,oBAAoB,CAC7B,IAAK,qBAAqB,CACxB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,4BAA4B,CACrC,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,wBAAwB,CAC3B,MAAO,qCAAqC,CAC9C,QACE,MAAO,yBAAyB,CACpC,CACF,CACF,CAEA,MAAO,MAAM,CAAAC,WAAW,CAAG,GAAI,CAAApE,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}