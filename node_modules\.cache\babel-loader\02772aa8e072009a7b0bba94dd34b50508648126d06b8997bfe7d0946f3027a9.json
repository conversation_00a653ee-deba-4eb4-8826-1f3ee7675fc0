{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nexport default class StorageBucketApi {\n  constructor(url) {\n    let headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let fetch = arguments.length > 2 ? arguments[2] : undefined;\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  listBuckets() {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  getBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket/${id}`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   */\n  createBucket(id) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      public: false\n    };\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  updateBucket(id, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  emptyBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  deleteBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_HEADERS", "isStorageError", "get", "post", "put", "remove", "resolveFetch", "StorageBucketApi", "constructor", "url", "headers", "arguments", "length", "undefined", "fetch", "Object", "assign", "listBuckets", "data", "error", "getBucket", "id", "createBucket", "options", "public", "name", "file_size_limit", "fileSizeLimit", "allowed_mime_types", "allowedMimeTypes", "updateBucket", "emptyBucket", "deleteBucket"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\storage-js\\src\\packages\\StorageBucketApi.ts"], "sourcesContent": ["import { DEFAULT_HEADERS } from '../lib/constants'\nimport { isStorageError, StorageError } from '../lib/errors'\nimport { Fetch, get, post, put, remove } from '../lib/fetch'\nimport { resolveFetch } from '../lib/helpers'\nimport { Bucket } from '../lib/types'\n\nexport default class StorageBucketApi {\n  protected url: string\n  protected headers: { [key: string]: string }\n  protected fetch: Fetch\n\n  constructor(url: string, headers: { [key: string]: string } = {}, fetch?: Fetch) {\n    this.url = url\n    this.headers = { ...DEFAULT_HEADERS, ...headers }\n    this.fetch = resolveFetch(fetch)\n  }\n\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  async listBuckets(): Promise<\n    | {\n        data: Bucket[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await get(this.fetch, `${this.url}/bucket`, { headers: this.headers })\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  async getBucket(\n    id: string\n  ): Promise<\n    | {\n        data: Bucket\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await get(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers })\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   */\n  async createBucket(\n    id: string,\n    options: {\n      public: boolean\n      fileSizeLimit?: number | string | null\n      allowedMimeTypes?: string[] | null\n    } = {\n      public: false,\n    }\n  ): Promise<\n    | {\n        data: Pick<Bucket, 'name'>\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/bucket`,\n        {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  async updateBucket(\n    id: string,\n    options: {\n      public: boolean\n      fileSizeLimit?: number | string | null\n      allowedMimeTypes?: string[] | null\n    }\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await put(\n        this.fetch,\n        `${this.url}/bucket/${id}`,\n        {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  async emptyBucket(\n    id: string\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/bucket/${id}/empty`,\n        {},\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  async deleteBucket(\n    id: string\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await remove(\n        this.fetch,\n        `${this.url}/bucket/${id}`,\n        {},\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,eAAe,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAsB,eAAe;AAC5D,SAAgBC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAC5D,SAASC,YAAY,QAAQ,gBAAgB;AAG7C,eAAc,MAAOC,gBAAgB;EAKnCC,YAAYC,GAAW,EAAwD;IAAA,IAAtDC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqC,EAAE;IAAA,IAAEG,KAAa,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAC7E,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAAK,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQhB,eAAe,GAAKU,OAAO,CAAE;IACjD,IAAI,CAACI,KAAK,GAAGR,YAAY,CAACQ,KAAK,CAAC;EAClC;EAEA;;;EAGMG,WAAWA,CAAA;;MAUf,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMhB,GAAG,CAAC,IAAI,CAACY,KAAK,EAAE,GAAG,IAAI,CAACL,GAAG,SAAS,EAAE;UAAEC,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAAC;QACnF,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;EAKMC,SAASA,CACbC,EAAU;;MAWV,IAAI;QACF,MAAMH,IAAI,GAAG,MAAMhB,GAAG,CAAC,IAAI,CAACY,KAAK,EAAE,GAAG,IAAI,CAACL,GAAG,WAAWY,EAAE,EAAE,EAAE;UAAEX,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAAC;QACzF,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;;;;;;;EAaMG,YAAYA,CAChBD,EAAU,EAOT;IAAA,IANDE,OAAA,GAAAZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAII;MACFa,MAAM,EAAE;KACT;;MAWD,IAAI;QACF,MAAMN,IAAI,GAAG,MAAMf,IAAI,CACrB,IAAI,CAACW,KAAK,EACV,GAAG,IAAI,CAACL,GAAG,SAAS,EACpB;UACEY,EAAE;UACFI,IAAI,EAAEJ,EAAE;UACRG,MAAM,EAAED,OAAO,CAACC,MAAM;UACtBE,eAAe,EAAEH,OAAO,CAACI,aAAa;UACtCC,kBAAkB,EAAEL,OAAO,CAACM;SAC7B,EACD;UAAEnB,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;;;;;;EAYMW,YAAYA,CAChBT,EAAU,EACVE,OAIC;;MAWD,IAAI;QACF,MAAML,IAAI,GAAG,MAAMd,GAAG,CACpB,IAAI,CAACU,KAAK,EACV,GAAG,IAAI,CAACL,GAAG,WAAWY,EAAE,EAAE,EAC1B;UACEA,EAAE;UACFI,IAAI,EAAEJ,EAAE;UACRG,MAAM,EAAED,OAAO,CAACC,MAAM;UACtBE,eAAe,EAAEH,OAAO,CAACI,aAAa;UACtCC,kBAAkB,EAAEL,OAAO,CAACM;SAC7B,EACD;UAAEnB,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;EAKMY,WAAWA,CACfV,EAAU;;MAWV,IAAI;QACF,MAAMH,IAAI,GAAG,MAAMf,IAAI,CACrB,IAAI,CAACW,KAAK,EACV,GAAG,IAAI,CAACL,GAAG,WAAWY,EAAE,QAAQ,EAChC,EAAE,EACF;UAAEX,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;EAMMa,YAAYA,CAChBX,EAAU;;MAWV,IAAI;QACF,MAAMH,IAAI,GAAG,MAAMb,MAAM,CACvB,IAAI,CAACS,KAAK,EACV,GAAG,IAAI,CAACL,GAAG,WAAWY,EAAE,EAAE,EAC1B,EAAE,EACF;UAAEX,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEQ,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIlB,cAAc,CAACkB,KAAK,CAAC,EAAE;UACzB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}