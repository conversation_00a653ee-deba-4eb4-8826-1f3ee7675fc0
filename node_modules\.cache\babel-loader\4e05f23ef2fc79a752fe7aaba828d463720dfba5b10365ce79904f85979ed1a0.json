{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\CategoriesManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport { PlusIcon, PencilIcon, TrashIcon, FolderIcon, MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';\n\n// Services\n\n// Types\n\n// Components\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport CategoryModal from './CategoryModal';\nimport ConfirmDialog from '../common/ConfirmDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesManagement = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [categoryToDelete, setCategoryToDelete] = useState(null);\n  useEffect(() => {\n    loadCategories();\n  }, []);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await courseService.getCategories();\n      setCategories(data);\n    } catch (error) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateCategory = () => {\n    setEditingCategory(null);\n    setShowModal(true);\n  };\n  const handleEditCategory = category => {\n    setEditingCategory(category);\n    setShowModal(true);\n  };\n  const handleDeleteCategory = category => {\n    setCategoryToDelete(category);\n    setShowDeleteDialog(true);\n  };\n  const confirmDelete = async () => {\n    if (!categoryToDelete) return;\n    try {\n      await courseService.deleteCategory(categoryToDelete.id);\n      setCategories(categories.filter(c => c.id !== categoryToDelete.id));\n      toast.success('تم حذف القسم بنجاح');\n    } catch (error) {\n      toast.error(error.message);\n    } finally {\n      setShowDeleteDialog(false);\n      setCategoryToDelete(null);\n    }\n  };\n  const handleSaveCategory = async categoryData => {\n    try {\n      if (editingCategory) {\n        // Update existing category\n        await courseService.updateCategory(editingCategory.id, categoryData);\n        setCategories(categories.map(c => c.id === editingCategory.id ? {\n          ...c,\n          ...categoryData\n        } : c));\n        toast.success('تم تحديث القسم بنجاح');\n      } else {\n        // Create new category\n        const id = await courseService.createCategory(categoryData);\n        const newCategory = {\n          id,\n          ...categoryData,\n          createdAt: new Date()\n        };\n        setCategories([newCategory, ...categories]);\n        toast.success('تم إنشاء القسم بنجاح');\n      }\n      setShowModal(false);\n    } catch (error) {\n      toast.error(error.message);\n    }\n  };\n  const filteredCategories = categories.filter(category => category.name.toLowerCase().includes(searchTerm.toLowerCase()) || category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      text: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0623\\u0642\\u0633\\u0627\\u0645...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0642\\u0633\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-1\",\n          children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0623\\u0642\\u0633\\u0627\\u0645 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0648\\u0627\\u062F \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateCategory,\n        className: \"mt-4 sm:mt-0 btn-primary flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-5 h-5 ml-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl p-6 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0623\\u0642\\u0633\\u0627\\u0645...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"form-input pr-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-outline flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n            className: \"w-5 h-5 ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), \"\\u062A\\u0635\\u0641\\u064A\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: filteredCategories.map((category, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `\n                  w-12 h-12 rounded-lg flex items-center justify-center\n                  ${category.color ? `bg-${category.color}-100` : 'bg-primary-100'}\n                `,\n              children: /*#__PURE__*/_jsxDEV(FolderIcon, {\n                className: `\n                    w-6 h-6\n                    ${category.color ? `text-${category.color}-600` : 'text-primary-600'}\n                  `\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditCategory(category),\n                className: \"p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200\",\n                children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteCategory(category),\n                className: \"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\",\n                children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-2\",\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm mb-4 line-clamp-2\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `\n                  px-2 py-1 rounded-full text-xs font-medium\n                  ${category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}\n                `,\n              children: category.isActive ? 'نشط' : 'غير نشط'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500\",\n              children: category.createdAt.toLocaleDateString('ar-SA')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, category.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), filteredCategories.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(FolderIcon, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: searchTerm ? 'لا توجد نتائج' : 'لا توجد أقسام'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: searchTerm ? 'جرب البحث بكلمات مختلفة' : 'ابدأ بإنشاء قسم جديد لتنظيم الكورسات'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), !searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateCategory,\n        className: \"btn-primary\",\n        children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0642\\u0633\\u0645 \\u062C\\u062F\\u064A\\u062F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSaveCategory,\n      category: editingCategory\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      isOpen: showDeleteDialog,\n      onClose: () => setShowDeleteDialog(false),\n      onConfirm: confirmDelete,\n      title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0642\\u0633\\u0645\",\n      message: `هل أنت متأكد من حذف القسم \"${categoryToDelete === null || categoryToDelete === void 0 ? void 0 : categoryToDelete.name}\"؟ هذا الإجراء لا يمكن التراجع عنه.`,\n      confirmText: \"\\u062D\\u0630\\u0641\",\n      cancelText: \"\\u0625\\u0644\\u063A\\u0627\\u0621\",\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesManagement, \"iQURsr8YAoTwVXChzQ0zApdRpJg=\");\n_c = CategoriesManagement;\nexport default CategoriesManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoriesManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "toast", "PlusIcon", "PencilIcon", "TrashIcon", "FolderIcon", "MagnifyingGlassIcon", "FunnelIcon", "LoadingSpinner", "CategoryModal", "ConfirmDialog", "jsxDEV", "_jsxDEV", "CategoriesManagement", "_s", "categories", "setCategories", "loading", "setLoading", "searchTerm", "setSearchTerm", "showModal", "setShowModal", "editingCategory", "setEditingCategory", "showDeleteDialog", "setShowDeleteDialog", "categoryToDelete", "setCategoryToDelete", "loadCategories", "data", "courseService", "getCategories", "error", "message", "handleCreateCategory", "handleEditCategory", "category", "handleDeleteCategory", "confirmDelete", "deleteCategory", "id", "filter", "c", "success", "handleSaveCategory", "categoryData", "updateCategory", "map", "createCategory", "newCategory", "createdAt", "Date", "filteredCategories", "name", "toLowerCase", "includes", "description", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "index", "div", "initial", "opacity", "y", "animate", "exit", "transition", "delay", "color", "isActive", "toLocaleDateString", "length", "isOpen", "onClose", "onSave", "onConfirm", "title", "confirmText", "cancelText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/CategoriesManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  FolderIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon\n} from '@heroicons/react/24/outline';\n\n// Services\nimport { dataService } from '../../services/dataService';\n\n// Types\nimport { Category } from '../../types';\n\n// Components\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport CategoryModal from './CategoryModal';\nimport ConfirmDialog from '../common/ConfirmDialog';\n\nconst CategoriesManagement: React.FC = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await courseService.getCategories();\n      setCategories(data);\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateCategory = () => {\n    setEditingCategory(null);\n    setShowModal(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setEditingCategory(category);\n    setShowModal(true);\n  };\n\n  const handleDeleteCategory = (category: Category) => {\n    setCategoryToDelete(category);\n    setShowDeleteDialog(true);\n  };\n\n  const confirmDelete = async () => {\n    if (!categoryToDelete) return;\n\n    try {\n      await courseService.deleteCategory(categoryToDelete.id);\n      setCategories(categories.filter(c => c.id !== categoryToDelete.id));\n      toast.success('تم حذف القسم بنجاح');\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setShowDeleteDialog(false);\n      setCategoryToDelete(null);\n    }\n  };\n\n  const handleSaveCategory = async (categoryData: Omit<Category, 'id' | 'createdAt'>) => {\n    try {\n      if (editingCategory) {\n        // Update existing category\n        await courseService.updateCategory(editingCategory.id, categoryData);\n        setCategories(categories.map(c => \n          c.id === editingCategory.id \n            ? { ...c, ...categoryData }\n            : c\n        ));\n        toast.success('تم تحديث القسم بنجاح');\n      } else {\n        // Create new category\n        const id = await courseService.createCategory(categoryData);\n        const newCategory: Category = {\n          id,\n          ...categoryData,\n          createdAt: new Date()\n        };\n        setCategories([newCategory, ...categories]);\n        toast.success('تم إنشاء القسم بنجاح');\n      }\n      setShowModal(false);\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const filteredCategories = categories.filter(category =>\n    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  if (loading) {\n    return <LoadingSpinner text=\"جاري تحميل الأقسام...\" />;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الأقسام</h1>\n          <p className=\"text-gray-600 mt-1\">إدارة أقسام الكورسات والمواد التعليمية</p>\n        </div>\n        <button\n          onClick={handleCreateCategory}\n          className=\"mt-4 sm:mt-0 btn-primary flex items-center\"\n        >\n          <PlusIcon className=\"w-5 h-5 ml-2\" />\n          إضافة قسم جديد\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-xl p-6 shadow-sm\">\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1 relative\">\n            <MagnifyingGlassIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث في الأقسام...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"form-input pr-10\"\n            />\n          </div>\n          <button className=\"btn-outline flex items-center\">\n            <FunnelIcon className=\"w-5 h-5 ml-2\" />\n            تصفية\n          </button>\n        </div>\n      </div>\n\n      {/* Categories Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <AnimatePresence>\n          {filteredCategories.map((category, index) => (\n            <motion.div\n              key={category.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200\"\n            >\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className={`\n                  w-12 h-12 rounded-lg flex items-center justify-center\n                  ${category.color ? `bg-${category.color}-100` : 'bg-primary-100'}\n                `}>\n                  <FolderIcon className={`\n                    w-6 h-6\n                    ${category.color ? `text-${category.color}-600` : 'text-primary-600'}\n                  `} />\n                </div>\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleEditCategory(category)}\n                    className=\"p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200\"\n                  >\n                    <PencilIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteCategory(category)}\n                    className=\"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\n                  >\n                    <TrashIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {category.name}\n              </h3>\n              \n              {category.description && (\n                <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                  {category.description}\n                </p>\n              )}\n\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className={`\n                  px-2 py-1 rounded-full text-xs font-medium\n                  ${category.isActive \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                  }\n                `}>\n                  {category.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n                <span className=\"text-gray-500\">\n                  {category.createdAt.toLocaleDateString('ar-SA')}\n                </span>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </div>\n\n      {/* Empty State */}\n      {filteredCategories.length === 0 && !loading && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center py-12\"\n        >\n          <FolderIcon className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n            {searchTerm ? 'لا توجد نتائج' : 'لا توجد أقسام'}\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm \n              ? 'جرب البحث بكلمات مختلفة'\n              : 'ابدأ بإنشاء قسم جديد لتنظيم الكورسات'\n            }\n          </p>\n          {!searchTerm && (\n            <button\n              onClick={handleCreateCategory}\n              className=\"btn-primary\"\n            >\n              إنشاء قسم جديد\n            </button>\n          )}\n        </motion.div>\n      )}\n\n      {/* Category Modal */}\n      <CategoryModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSaveCategory}\n        category={editingCategory}\n      />\n\n      {/* Delete Confirmation Dialog */}\n      <ConfirmDialog\n        isOpen={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n        onConfirm={confirmDelete}\n        title=\"حذف القسم\"\n        message={`هل أنت متأكد من حذف القسم \"${categoryToDelete?.name}\"؟ هذا الإجراء لا يمكن التراجع عنه.`}\n        confirmText=\"حذف\"\n        cancelText=\"إلغاء\"\n        type=\"danger\"\n      />\n    </div>\n  );\n};\n\nexport default CategoriesManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,mBAAmB,EACnBC,UAAU,QACL,6BAA6B;;AAEpC;;AAGA;;AAGA;AACA,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAkB,IAAI,CAAC;EAE/EC,SAAS,CAAC,MAAM;IACd+B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,IAAI,GAAG,MAAMC,aAAa,CAACC,aAAa,CAAC,CAAC;MAChDhB,aAAa,CAACc,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnBhC,KAAK,CAACgC,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC;IAC5B,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,oBAAoB,GAAGA,CAAA,KAAM;IACjCX,kBAAkB,CAAC,IAAI,CAAC;IACxBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMc,kBAAkB,GAAIC,QAAkB,IAAK;IACjDb,kBAAkB,CAACa,QAAQ,CAAC;IAC5Bf,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMgB,oBAAoB,GAAID,QAAkB,IAAK;IACnDT,mBAAmB,CAACS,QAAQ,CAAC;IAC7BX,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACZ,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMI,aAAa,CAACS,cAAc,CAACb,gBAAgB,CAACc,EAAE,CAAC;MACvDzB,aAAa,CAACD,UAAU,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKd,gBAAgB,CAACc,EAAE,CAAC,CAAC;MACnExC,KAAK,CAAC2C,OAAO,CAAC,oBAAoB,CAAC;IACrC,CAAC,CAAC,OAAOX,KAAU,EAAE;MACnBhC,KAAK,CAACgC,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC;IAC5B,CAAC,SAAS;MACRR,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAOC,YAAgD,IAAK;IACrF,IAAI;MACF,IAAIvB,eAAe,EAAE;QACnB;QACA,MAAMQ,aAAa,CAACgB,cAAc,CAACxB,eAAe,CAACkB,EAAE,EAAEK,YAAY,CAAC;QACpE9B,aAAa,CAACD,UAAU,CAACiC,GAAG,CAACL,CAAC,IAC5BA,CAAC,CAACF,EAAE,KAAKlB,eAAe,CAACkB,EAAE,GACvB;UAAE,GAAGE,CAAC;UAAE,GAAGG;QAAa,CAAC,GACzBH,CACN,CAAC,CAAC;QACF1C,KAAK,CAAC2C,OAAO,CAAC,sBAAsB,CAAC;MACvC,CAAC,MAAM;QACL;QACA,MAAMH,EAAE,GAAG,MAAMV,aAAa,CAACkB,cAAc,CAACH,YAAY,CAAC;QAC3D,MAAMI,WAAqB,GAAG;UAC5BT,EAAE;UACF,GAAGK,YAAY;UACfK,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDpC,aAAa,CAAC,CAACkC,WAAW,EAAE,GAAGnC,UAAU,CAAC,CAAC;QAC3Cd,KAAK,CAAC2C,OAAO,CAAC,sBAAsB,CAAC;MACvC;MACAtB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOW,KAAU,EAAE;MACnBhC,KAAK,CAACgC,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC;IAC5B;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAGtC,UAAU,CAAC2B,MAAM,CAACL,QAAQ,IACnDA,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,UAAU,CAACoC,WAAW,CAAC,CAAC,CAAC,IAC7DlB,QAAQ,CAACoB,WAAW,IAAIpB,QAAQ,CAACoB,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,UAAU,CAACoC,WAAW,CAAC,CAAC,CAC/F,CAAC;EAED,IAAItC,OAAO,EAAE;IACX,oBAAOL,OAAA,CAACJ,cAAc;MAACkD,IAAI,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxD;EAEA,oBACElD,OAAA;IAAKmD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpD,OAAA;MAAKmD,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3EpD,OAAA;QAAAoD,QAAA,gBACEpD,OAAA;UAAImD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnElD,OAAA;UAAGmD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAsC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACNlD,OAAA;QACEqD,OAAO,EAAE9B,oBAAqB;QAC9B4B,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEtDpD,OAAA,CAACV,QAAQ;UAAC6D,SAAS,EAAC;QAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,8EAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlD,OAAA;MAAKmD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDpD,OAAA;QAAKmD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CpD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpD,OAAA,CAACN,mBAAmB;YAACyD,SAAS,EAAC;UAA2E;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7GlD,OAAA;YACEsD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,2FAAqB;YACjCC,KAAK,EAAEjD,UAAW;YAClBkD,QAAQ,EAAGC,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CL,SAAS,EAAC;UAAkB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlD,OAAA;UAAQmD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC/CpD,OAAA,CAACL,UAAU;YAACwD,SAAS,EAAC;UAAc;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kCAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA;MAAKmD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEpD,OAAA,CAACZ,eAAe;QAAAgE,QAAA,EACbX,kBAAkB,CAACL,GAAG,CAAC,CAACX,QAAQ,EAAEmC,KAAK,kBACtC5D,OAAA,CAACb,MAAM,CAAC0E,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BG,UAAU,EAAE;YAAEC,KAAK,EAAER,KAAK,GAAG;UAAI,CAAE;UACnCT,SAAS,EAAC,kFAAkF;UAAAC,QAAA,gBAE5FpD,OAAA;YAAKmD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpD,OAAA;cAAKmD,SAAS,EAAE;AAChC;AACA,oBAAoB1B,QAAQ,CAAC4C,KAAK,GAAG,MAAM5C,QAAQ,CAAC4C,KAAK,MAAM,GAAG,gBAAgB;AAClF,iBAAkB;cAAAjB,QAAA,eACApD,OAAA,CAACP,UAAU;gBAAC0D,SAAS,EAAE;AACzC;AACA,sBAAsB1B,QAAQ,CAAC4C,KAAK,GAAG,QAAQ5C,QAAQ,CAAC4C,KAAK,MAAM,GAAG,kBAAkB;AACxF;cAAoB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNlD,OAAA;cAAKmD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CpD,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAACC,QAAQ,CAAE;gBAC5C0B,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eAElHpD,OAAA,CAACT,UAAU;kBAAC4D,SAAS,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACTlD,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAM3B,oBAAoB,CAACD,QAAQ,CAAE;gBAC9C0B,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,eAE1GpD,OAAA,CAACR,SAAS;kBAAC2D,SAAS,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAImD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EACrD3B,QAAQ,CAACiB;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EAEJzB,QAAQ,CAACoB,WAAW,iBACnB7C,OAAA;YAAGmD,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACnD3B,QAAQ,CAACoB;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACJ,eAEDlD,OAAA;YAAKmD,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDpD,OAAA;cAAMmD,SAAS,EAAE;AACjC;AACA,oBAAoB1B,QAAQ,CAAC6C,QAAQ,GACf,6BAA6B,GAC7B,yBAAyB;AAC/C,iBACkB;cAAAlB,QAAA,EACC3B,QAAQ,CAAC6C,QAAQ,GAAG,KAAK,GAAG;YAAS;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACPlD,OAAA;cAAMmD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC5B3B,QAAQ,CAACc,SAAS,CAACgC,kBAAkB,CAAC,OAAO;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAxDDzB,QAAQ,CAACI,EAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyDN,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,EAGLT,kBAAkB,CAAC+B,MAAM,KAAK,CAAC,IAAI,CAACnE,OAAO,iBAC1CL,OAAA,CAACb,MAAM,CAAC0E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBE,OAAO,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACxBZ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAE7BpD,OAAA,CAACP,UAAU;QAAC0D,SAAS,EAAC;MAAsC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DlD,OAAA;QAAImD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EACnD7C,UAAU,GAAG,eAAe,GAAG;MAAe;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACLlD,OAAA;QAAGmD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAC9B7C,UAAU,GACP,yBAAyB,GACzB;MAAsC;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzC,CAAC,EACH,CAAC3C,UAAU,iBACVP,OAAA;QACEqD,OAAO,EAAE9B,oBAAqB;QAC9B4B,SAAS,EAAC,aAAa;QAAAC,QAAA,EACxB;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CACb,eAGDlD,OAAA,CAACH,aAAa;MACZ4E,MAAM,EAAEhE,SAAU;MAClBiE,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,KAAK,CAAE;MACnCiE,MAAM,EAAE1C,kBAAmB;MAC3BR,QAAQ,EAAEd;IAAgB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGFlD,OAAA,CAACF,aAAa;MACZ2E,MAAM,EAAE5D,gBAAiB;MACzB6D,OAAO,EAAEA,CAAA,KAAM5D,mBAAmB,CAAC,KAAK,CAAE;MAC1C8D,SAAS,EAAEjD,aAAc;MACzBkD,KAAK,EAAC,mDAAW;MACjBvD,OAAO,EAAE,8BAA8BP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,IAAI,qCAAsC;MACnGoC,WAAW,EAAC,oBAAK;MACjBC,UAAU,EAAC,gCAAO;MAClBzB,IAAI,EAAC;IAAQ;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChD,EAAA,CArPID,oBAA8B;AAAA+E,EAAA,GAA9B/E,oBAA8B;AAuPpC,eAAeA,oBAAoB;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}