[{"C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts": "3", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx": "8", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts": "9", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx": "13", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx": "15", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx": "16", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts": "17", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx": "21", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx": "22", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx": "23", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx": "24", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx": "25", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx": "26", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx": "29", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx": "30", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx": "31", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx": "32", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx": "33", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts": "34", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockStudents.ts": "35", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts": "36", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockCourses.ts": "37", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\supabaseService.ts": "38", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\supabase.ts": "39", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddCourseModal.tsx": "40", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddStudentModal.tsx": "41", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditStudentModal.tsx": "42", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditCourseModal.tsx": "43", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveCard.tsx": "44", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveGrid.tsx": "45", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveButton.tsx": "46", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveText.tsx": "47"}, {"size": 287, "mtime": 1752677446241, "results": "48", "hashOfConfig": "49"}, {"size": 4189, "mtime": 1752682096005, "results": "50", "hashOfConfig": "49"}, {"size": 5061, "mtime": 1752707859127, "results": "51", "hashOfConfig": "49"}, {"size": 10971, "mtime": 1752692029273, "results": "52", "hashOfConfig": "49"}, {"size": 2772, "mtime": 1752679863852, "results": "53", "hashOfConfig": "49"}, {"size": 2898, "mtime": 1752676959952, "results": "54", "hashOfConfig": "49"}, {"size": 1550, "mtime": 1752676894244, "results": "55", "hashOfConfig": "49"}, {"size": 25700, "mtime": 1752693728641, "results": "56", "hashOfConfig": "49"}, {"size": 833, "mtime": 1752681975589, "results": "57", "hashOfConfig": "49"}, {"size": 7348, "mtime": 1752692728556, "results": "58", "hashOfConfig": "49"}, {"size": 10172, "mtime": 1752677555299, "results": "59", "hashOfConfig": "49"}, {"size": 4734, "mtime": 1752687830131, "results": "60", "hashOfConfig": "49"}, {"size": 7678, "mtime": 1752682443047, "results": "61", "hashOfConfig": "49"}, {"size": 5922, "mtime": 1752676990944, "results": "62", "hashOfConfig": "49"}, {"size": 8022, "mtime": 1752687809104, "results": "63", "hashOfConfig": "49"}, {"size": 9336, "mtime": 1752684234560, "results": "64", "hashOfConfig": "49"}, {"size": 8385, "mtime": 1752676831470, "results": "65", "hashOfConfig": "49"}, {"size": 4977, "mtime": 1752677160727, "results": "66", "hashOfConfig": "49"}, {"size": 2740, "mtime": 1752677099694, "results": "67", "hashOfConfig": "49"}, {"size": 3926, "mtime": 1752677124767, "results": "68", "hashOfConfig": "49"}, {"size": 8369, "mtime": 1752677265900, "results": "69", "hashOfConfig": "49"}, {"size": 5660, "mtime": 1752677299906, "results": "70", "hashOfConfig": "49"}, {"size": 10600, "mtime": 1752685683050, "results": "71", "hashOfConfig": "49"}, {"size": 15419, "mtime": 1752687029487, "results": "72", "hashOfConfig": "49"}, {"size": 10777, "mtime": 1752686961967, "results": "73", "hashOfConfig": "49"}, {"size": 10091, "mtime": 1752707036293, "results": "74", "hashOfConfig": "49"}, {"size": 5141, "mtime": 1752680890303, "results": "75", "hashOfConfig": "49"}, {"size": 10772, "mtime": 1752693802266, "results": "76", "hashOfConfig": "49"}, {"size": 10901, "mtime": 1752693192972, "results": "77", "hashOfConfig": "49"}, {"size": 11421, "mtime": 1752707626290, "results": "78", "hashOfConfig": "49"}, {"size": 11382, "mtime": 1752681329670, "results": "79", "hashOfConfig": "49"}, {"size": 8199, "mtime": 1752681362339, "results": "80", "hashOfConfig": "49"}, {"size": 15051, "mtime": 1752684390342, "results": "81", "hashOfConfig": "49"}, {"size": 661, "mtime": 1752682975032, "results": "82", "hashOfConfig": "49"}, {"size": 2517, "mtime": 1752707198189, "results": "83", "hashOfConfig": "49"}, {"size": 9963, "mtime": 1752687011098, "results": "84", "hashOfConfig": "49"}, {"size": 3320, "mtime": 1752707834027, "results": "85", "hashOfConfig": "49"}, {"size": 15961, "mtime": 1752693430329, "results": "86", "hashOfConfig": "49"}, {"size": 1613, "mtime": 1752685035924, "results": "87", "hashOfConfig": "49"}, {"size": 9036, "mtime": 1752686724623, "results": "88", "hashOfConfig": "49"}, {"size": 7840, "mtime": 1752685885732, "results": "89", "hashOfConfig": "49"}, {"size": 7935, "mtime": 1752685920414, "results": "90", "hashOfConfig": "49"}, {"size": 9770, "mtime": 1752686944770, "results": "91", "hashOfConfig": "49"}, {"size": 2176, "mtime": 1752692204934, "results": "92", "hashOfConfig": "49"}, {"size": 2052, "mtime": 1752692149480, "results": "93", "hashOfConfig": "49"}, {"size": 3243, "mtime": 1752707797250, "results": "94", "hashOfConfig": "49"}, {"size": 1810, "mtime": 1752692164556, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbgu1t", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx", ["237"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts", ["238", "239"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx", ["240", "241", "242"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts", ["243"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx", ["244"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx", ["245", "246", "247", "248", "249", "250", "251", "252"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx", ["253", "254"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx", ["255", "256", "257", "258"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx", ["259", "260", "261", "262", "263", "264", "265", "266", "267", "268"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx", ["269"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx", ["270"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx", ["271", "272"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts", ["273"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockStudents.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts", ["274", "275", "276", "277", "278", "279", "280", "281", "282"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockCourses.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\supabaseService.ts", ["283", "284", "285", "286", "287", "288"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddCourseModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddStudentModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditStudentModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditCourseModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveText.tsx", [], [], {"ruleId": "289", "severity": 1, "message": "290", "line": 34, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 34, "endColumn": 22}, {"ruleId": "289", "severity": 1, "message": "293", "line": 2, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 29}, {"ruleId": "289", "severity": 1, "message": "294", "line": 8, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 8, "endColumn": 14}, {"ruleId": "289", "severity": 1, "message": "295", "line": 4, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 4, "endColumn": 26}, {"ruleId": "289", "severity": 1, "message": "296", "line": 7, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 7, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "297", "line": 11, "column": 26, "nodeType": "291", "messageId": "292", "endLine": 11, "endColumn": 43}, {"ruleId": "289", "severity": 1, "message": "298", "line": 14, "column": 44, "nodeType": "291", "messageId": "292", "endLine": 14, "endColumn": 56}, {"ruleId": "289", "severity": 1, "message": "299", "line": 15, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 15, "endColumn": 21}, {"ruleId": "289", "severity": 1, "message": "300", "line": 24, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 24, "endColumn": 17}, {"ruleId": "289", "severity": 1, "message": "301", "line": 25, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 25, "endColumn": 22}, {"ruleId": "289", "severity": 1, "message": "302", "line": 26, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 26, "endColumn": 23}, {"ruleId": "289", "severity": 1, "message": "303", "line": 27, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 27, "endColumn": 22}, {"ruleId": "304", "severity": 1, "message": "305", "line": 31, "column": 6, "nodeType": "306", "endLine": 31, "endColumn": 8, "suggestions": "307"}, {"ruleId": "304", "severity": 1, "message": "308", "line": 113, "column": 6, "nodeType": "306", "endLine": 113, "endColumn": 8, "suggestions": "309"}, {"ruleId": "289", "severity": 1, "message": "310", "line": 145, "column": 9, "nodeType": "291", "messageId": "292", "endLine": 145, "endColumn": 24}, {"ruleId": "289", "severity": 1, "message": "311", "line": 151, "column": 9, "nodeType": "291", "messageId": "292", "endLine": 151, "endColumn": 26}, {"ruleId": "304", "severity": 1, "message": "312", "line": 28, "column": 6, "nodeType": "306", "endLine": 28, "endColumn": 8, "suggestions": "313"}, {"ruleId": "304", "severity": 1, "message": "314", "line": 85, "column": 6, "nodeType": "306", "endLine": 85, "endColumn": 8, "suggestions": "315"}, {"ruleId": "289", "severity": 1, "message": "316", "line": 2, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 16}, {"ruleId": "289", "severity": 1, "message": "317", "line": 7, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 7, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "318", "line": 27, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 27, "endColumn": 16}, {"ruleId": "304", "severity": 1, "message": "319", "line": 43, "column": 6, "nodeType": "306", "endLine": 43, "endColumn": 15, "suggestions": "320"}, {"ruleId": "289", "severity": 1, "message": "321", "line": 11, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 11, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "322", "line": 12, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 12, "endColumn": 18}, {"ruleId": "289", "severity": 1, "message": "323", "line": 13, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 13, "endColumn": 12}, {"ruleId": "289", "severity": 1, "message": "324", "line": 18, "column": 8, "nodeType": "291", "messageId": "292", "endLine": 18, "endColumn": 22}, {"ruleId": "289", "severity": 1, "message": "325", "line": 33, "column": 9, "nodeType": "291", "messageId": "292", "endLine": 33, "endColumn": 17}, {"ruleId": "289", "severity": 1, "message": "300", "line": 38, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 38, "endColumn": 17}, {"ruleId": "289", "severity": 1, "message": "326", "line": 39, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 39, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "327", "line": 40, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 40, "endColumn": 23}, {"ruleId": "289", "severity": 1, "message": "328", "line": 40, "column": 25, "nodeType": "291", "messageId": "292", "endLine": 40, "endColumn": 41}, {"ruleId": "304", "severity": 1, "message": "329", "line": 46, "column": 6, "nodeType": "306", "endLine": 46, "endColumn": 16, "suggestions": "330"}, {"ruleId": "289", "severity": 1, "message": "331", "line": 22, "column": 20, "nodeType": "291", "messageId": "292", "endLine": 22, "endColumn": 31}, {"ruleId": "289", "severity": 1, "message": "332", "line": 1, "column": 17, "nodeType": "291", "messageId": "292", "endLine": 1, "endColumn": 25}, {"ruleId": "289", "severity": 1, "message": "333", "line": 5, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 5, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "334", "line": 6, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 6, "endColumn": 10}, {"ruleId": "289", "severity": 1, "message": "294", "line": 1, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 1, "endColumn": 14}, {"ruleId": "289", "severity": 1, "message": "335", "line": 1, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 1, "endColumn": 12}, {"ruleId": "289", "severity": 1, "message": "336", "line": 2, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 20}, {"ruleId": "289", "severity": 1, "message": "337", "line": 2, "column": 22, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 29}, {"ruleId": "289", "severity": 1, "message": "338", "line": 2, "column": 31, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 34}, {"ruleId": "289", "severity": 1, "message": "339", "line": 2, "column": 36, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 42}, {"ruleId": "289", "severity": 1, "message": "340", "line": 2, "column": 44, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 50}, {"ruleId": "289", "severity": 1, "message": "341", "line": 2, "column": 52, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 61}, {"ruleId": "289", "severity": 1, "message": "342", "line": 2, "column": 63, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 72}, {"ruleId": "289", "severity": 1, "message": "343", "line": 4, "column": 23, "nodeType": "291", "messageId": "292", "endLine": 4, "endColumn": 33}, {"ruleId": "289", "severity": 1, "message": "344", "line": 2, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 17}, {"ruleId": "289", "severity": 1, "message": "318", "line": 2, "column": 19, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 25}, {"ruleId": "289", "severity": 1, "message": "345", "line": 2, "column": 27, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 35}, {"ruleId": "289", "severity": 1, "message": "346", "line": 2, "column": 37, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 41}, {"ruleId": "289", "severity": 1, "message": "347", "line": 2, "column": 43, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 54}, {"ruleId": "289", "severity": 1, "message": "348", "line": 2, "column": 56, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 61}, "@typescript-eslint/no-unused-vars", "'firebaseUser' is assigned a value but never used.", "Identifier", "unusedVar", "'signInWithEmailAndPassword' is defined but never used.", "'User' is defined but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'SparklesIcon' is defined but never used.", "'SparklesIconSolid' is defined but never used.", "'deleteObject' is defined but never used.", "'authService' is defined but never used.", "'loading' is assigned a value but never used.", "'showAddModal' is assigned a value but never used.", "'showEditModal' is assigned a value but never used.", "'selectedQuiz' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadQuizzes'. Either include it or remove the dependency array.", "ArrayExpression", ["349"], "React Hook React.useEffect has a missing dependency: 'mockQuizzes'. Either include it or remove the dependency array.", ["350"], "'handleQuizAdded' is assigned a value but never used.", "'handleQuizUpdated' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCertificates'. Either include it or remove the dependency array.", ["351"], "React Hook React.useEffect has a missing dependency: 'mockCertificates'. Either include it or remove the dependency array.", ["352"], "'motion' is defined but never used.", "'DocumentIcon' is defined but never used.", "'Course' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEnrolledCourses'. Either include it or remove the dependency array.", ["353"], "'BookOpenIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'navigate' is assigned a value but never used.", "'error' is assigned a value but never used.", "'videoProgress' is assigned a value but never used.", "'setVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCourseData'. Either include it or remove the dependency array.", ["354"], "'setTimeLeft' is assigned a value but never used.", "'useState' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", "'db' is defined but never used.", "'collection' is defined but never used.", "'getDocs' is defined but never used.", "'doc' is defined but never used.", "'getDoc' is defined but never used.", "'addDoc' is defined but never used.", "'updateDoc' is defined but never used.", "'deleteDoc' is defined but never used.", "'mockVideos' is defined but never used.", "'Student' is defined but never used.", "'Category' is defined but never used.", "'Quiz' is defined but never used.", "'Certificate' is defined but never used.", "'Admin' is defined but never used.", {"desc": "355", "fix": "356"}, {"desc": "357", "fix": "358"}, {"desc": "359", "fix": "360"}, {"desc": "361", "fix": "362"}, {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, "Update the dependencies array to be: [loadQuizzes]", {"range": "367", "text": "368"}, "Update the dependencies array to be: [mockQuizzes]", {"range": "369", "text": "370"}, "Update the dependencies array to be: [loadCertificates]", {"range": "371", "text": "372"}, "Update the dependencies array to be: [mockCertificates]", {"range": "373", "text": "374"}, "Update the dependencies array to be: [loadEnrolledCourses, user.id]", {"range": "375", "text": "376"}, "Update the dependencies array to be: [courseId, loadCourseData]", {"range": "377", "text": "378"}, [926, 928], "[loadQuizzes]", [3213, 3215], "[mockQuizzes]", [758, 760], "[loadCertificates]", [2586, 2588], "[mockCertificates]", [1233, 1242], "[loadEnrolledCourses, user.id]", [1286, 1296], "[courseId, loadCourseData]"]