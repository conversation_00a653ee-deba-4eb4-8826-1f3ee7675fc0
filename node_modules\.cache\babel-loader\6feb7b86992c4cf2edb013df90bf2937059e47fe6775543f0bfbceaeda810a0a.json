{"ast": null, "code": "import{createClient}from'@supabase/supabase-js';const supabaseUrl='https://srnyumtbsyxiqkvwkcpi.supabase.co';const supabaseAnonKey='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybnl1bXRic3l4aXFrdndrY3BpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNjYzMjIsImV4cCI6MjA2Nzk0MjMyMn0.ROA5cGM5AQCvIBB-BGOLZPgEzR9rEoBkjLPboJJ0qJk';export const supabase=createClient(supabaseUrl,supabaseAnonKey);// Database types", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "supabaseAnonKey", "supabase"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/config/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = 'https://srnyumtbsyxiqkvwkcpi.supabase.co';\nconst supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybnl1bXRic3l4aXFrdndrY3BpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNjYzMjIsImV4cCI6MjA2Nzk0MjMyMn0.ROA5cGM5AQCvIBB-BGOLZPgEzR9rEoBkjLPboJJ0qJk';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\nexport interface Course {\n  id: string;\n  title: string;\n  description: string;\n  category_id: string;\n  instructor_id: string;\n  thumbnail_url?: string;\n  created_at: string;\n  updated_at: string;\n  is_active: boolean;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  description?: string;\n  created_at: string;\n  is_active: boolean;\n}\n\nexport interface Video {\n  id: string;\n  course_id: string;\n  title: string;\n  description?: string;\n  video_url: string;\n  duration?: number;\n  order_index: number;\n  created_at: string;\n}\n\nexport interface Student {\n  id: string;\n  access_code: string;\n  name?: string;\n  email?: string;\n  enrolled_courses: string[];\n  created_at: string;\n  is_active: boolean;\n}\n\nexport interface Quiz {\n  id: string;\n  course_id: string;\n  title: string;\n  description?: string;\n  questions: QuizQuestion[];\n  passing_score: number;\n  created_at: string;\n}\n\nexport interface QuizQuestion {\n  id: string;\n  question: string;\n  options: string[];\n  correct_answer: number;\n  points: number;\n}\n\nexport interface Certificate {\n  id: string;\n  student_id: string;\n  course_id: string;\n  certificate_url: string;\n  issued_at: string;\n}\n"], "mappings": "AAAA,OAASA,YAAY,KAAQ,uBAAuB,CAEpD,KAAM,CAAAC,WAAW,CAAG,0CAA0C,CAC9D,KAAM,CAAAC,eAAe,CAAG,kNAAkN,CAE1O,MAAO,MAAM,CAAAC,QAAQ,CAAGH,YAAY,CAACC,WAAW,CAAEC,eAAe,CAAC,CAElE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}