{"ast": null, "code": "export const mockStudents = [{\n  id: 'student-001',\n  email: '<EMAIL>',\n  name: 'أحمد محمد',\n  role: 'student',\n  avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n  accessCode: '12345',\n  enrolledCourses: ['1', '2'],\n  completedCourses: ['1'],\n  certificates: ['cert-001'],\n  createdAt: new Date('2024-01-15')\n}, {\n  id: 'student-002',\n  email: '<EMAIL>',\n  name: 'فاطمة أحمد',\n  role: 'student',\n  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n  accessCode: '67890',\n  enrolledCourses: ['2'],\n  completedCourses: [],\n  certificates: [],\n  createdAt: new Date('2024-01-20')\n}, {\n  id: 'student-003',\n  email: '<EMAIL>',\n  name: 'محمد علي',\n  role: 'student',\n  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n  accessCode: '11111',\n  enrolledCourses: ['1'],\n  completedCourses: [],\n  certificates: [],\n  createdAt: new Date('2024-02-01')\n}];\n\n// بيانات تسجيل دخول الطلاب للاختبار\nexport const studentCredentials = [{\n  email: '<EMAIL>',\n  password: 'Student@123',\n  accessCode: 'STU001'\n}, {\n  email: '<EMAIL>',\n  password: 'Student@123',\n  accessCode: 'STU002'\n}, {\n  email: '<EMAIL>',\n  password: 'Student@123',\n  accessCode: 'STU003'\n}];", "map": {"version": 3, "names": ["mockStudents", "id", "email", "name", "role", "avatar", "accessCode", "enrolledCourses", "completedCourses", "certificates", "createdAt", "Date", "studentCredentials", "password"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockStudents.ts"], "sourcesContent": ["import { Student } from '../types';\n\nexport const mockStudents: Student[] = [\n  {\n    id: 'student-001',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    accessCode: '12345',\n    enrolledCourses: ['1', '2'],\n    completedCourses: ['1'],\n    certificates: ['cert-001'],\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'student-002',\n    email: '<EMAIL>',\n    name: 'فاطمة أحمد',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    accessCode: '67890',\n    enrolledCourses: ['2'],\n    completedCourses: [],\n    certificates: [],\n    createdAt: new Date('2024-01-20')\n  },\n  {\n    id: 'student-003',\n    email: '<EMAIL>',\n    name: 'محمد علي',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    accessCode: '11111',\n    enrolledCourses: ['1'],\n    completedCourses: [],\n    certificates: [],\n    createdAt: new Date('2024-02-01')\n  }\n];\n\n// بيانات تسجيل دخول الطلاب للاختبار\nexport const studentCredentials = [\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU001' },\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU002' },\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU003' }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,YAAuB,GAAG,CACrC;EACEC,EAAE,EAAE,aAAa;EACjBC,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,6FAA6F;EACrGC,UAAU,EAAE,OAAO;EACnBC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3BC,gBAAgB,EAAE,CAAC,GAAG,CAAC;EACvBC,YAAY,EAAE,CAAC,UAAU,CAAC;EAC1BC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,aAAa;EACjBC,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,6FAA6F;EACrGC,UAAU,EAAE,OAAO;EACnBC,eAAe,EAAE,CAAC,GAAG,CAAC;EACtBC,gBAAgB,EAAE,EAAE;EACpBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,aAAa;EACjBC,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,6FAA6F;EACrGC,UAAU,EAAE,OAAO;EACnBC,eAAe,EAAE,CAAC,GAAG,CAAC;EACtBC,gBAAgB,EAAE,EAAE;EACpBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAG,CAChC;EAAEV,KAAK,EAAE,sBAAsB;EAAEW,QAAQ,EAAE,aAAa;EAAEP,UAAU,EAAE;AAAS,CAAC,EAChF;EAAEJ,KAAK,EAAE,sBAAsB;EAAEW,QAAQ,EAAE,aAAa;EAAEP,UAAU,EAAE;AAAS,CAAC,EAChF;EAAEJ,KAAK,EAAE,sBAAsB;EAAEW,QAAQ,EAAE,aAAa;EAAEP,UAAU,EAAE;AAAS,CAAC,CACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}