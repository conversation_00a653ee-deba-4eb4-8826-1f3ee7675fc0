import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

// Services
import { dataService } from '../../services/dataService';

// Types
import { Category } from '../../types';

// Components
import LoadingSpinner from '../common/LoadingSpinner';
import CategoryModal from './CategoryModal';
import ConfirmDialog from '../common/ConfirmDialog';

const CategoriesManagement: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await dataService.getCategories();
      setCategories(data);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = () => {
    setEditingCategory(null);
    setShowModal(true);
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setShowModal(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setCategoryToDelete(category);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!categoryToDelete) return;

    try {
      await dataService.deleteCategory(categoryToDelete.id);
      setCategories(categories.filter(c => c.id !== categoryToDelete.id));
      toast.success('تم حذف القسم بنجاح');
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setShowDeleteDialog(false);
      setCategoryToDelete(null);
    }
  };

  const handleSaveCategory = async (categoryData: Omit<Category, 'id' | 'createdAt'>) => {
    try {
      if (editingCategory) {
        // Update existing category
        await dataService.updateCategory(editingCategory.id, categoryData);
        setCategories(categories.map(c =>
          c.id === editingCategory.id
            ? { ...c, ...categoryData }
            : c
        ));
        toast.success('تم تحديث القسم بنجاح');
      } else {
        // Create new category
        const id = await dataService.createCategory(categoryData);
        const newCategory: Category = {
          id,
          ...categoryData,
          createdAt: new Date()
        };
        setCategories([newCategory, ...categories]);
        toast.success('تم إنشاء القسم بنجاح');
      }
      setShowModal(false);
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الأقسام..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الأقسام</h1>
          <p className="text-gray-600 mt-1">إدارة أقسام الكورسات والمواد التعليمية</p>
        </div>
        <button
          onClick={handleCreateCategory}
          className="mt-4 sm:mt-0 btn-primary flex items-center"
        >
          <PlusIcon className="w-5 h-5 ml-2" />
          إضافة قسم جديد
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في الأقسام..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pr-10"
            />
          </div>
          <button className="btn-outline flex items-center">
            <FunnelIcon className="w-5 h-5 ml-2" />
            تصفية
          </button>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredCategories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-start justify-between mb-4">
                <div className={`
                  w-12 h-12 rounded-lg flex items-center justify-center
                  ${category.color ? `bg-${category.color}-100` : 'bg-primary-100'}
                `}>
                  <FolderIcon className={`
                    w-6 h-6
                    ${category.color ? `text-${category.color}-600` : 'text-primary-600'}
                  `} />
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleEditCategory(category)}
                    className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteCategory(category)}
                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {category.name}
              </h3>
              
              {category.description && (
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {category.description}
                </p>
              )}

              <div className="flex items-center justify-between text-sm">
                <span className={`
                  px-2 py-1 rounded-full text-xs font-medium
                  ${category.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                  }
                `}>
                  {category.isActive ? 'نشط' : 'غير نشط'}
                </span>
                <span className="text-gray-500">
                  {category.createdAt.toLocaleDateString('ar-SA')}
                </span>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredCategories.length === 0 && !loading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <FolderIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'لا توجد نتائج' : 'لا توجد أقسام'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm 
              ? 'جرب البحث بكلمات مختلفة'
              : 'ابدأ بإنشاء قسم جديد لتنظيم الكورسات'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={handleCreateCategory}
              className="btn-primary"
            >
              إنشاء قسم جديد
            </button>
          )}
        </motion.div>
      )}

      {/* Category Modal */}
      <CategoryModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveCategory}
        category={editingCategory}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="حذف القسم"
        message={`هل أنت متأكد من حذف القسم "${categoryToDelete?.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
        confirmText="حذف"
        cancelText="إلغاء"
        type="danger"
      />
    </div>
  );
};

export default CategoriesManagement;
