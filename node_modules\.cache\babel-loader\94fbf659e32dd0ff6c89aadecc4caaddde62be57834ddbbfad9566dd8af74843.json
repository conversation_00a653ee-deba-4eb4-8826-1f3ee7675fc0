{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{toast}from'react-hot-toast';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,ClipboardDocumentListIcon,QuestionMarkCircleIcon}from'@heroicons/react/24/outline';import{supabaseService}from'../../services/supabaseService';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const QuizzesManagement=_ref=>{let{onBack}=_ref;const[quizzes,setQuizzes]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[loading,setLoading]=useState(true);const[showAddModal,setShowAddModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[selectedQuiz,setSelectedQuiz]=useState(null);useEffect(()=>{loadQuizzes();},[]);const loadQuizzes=async()=>{try{setLoading(true);// Try to get from Supabase first\nconst supabaseQuizzes=await supabaseService.getAllQuizzes();if(supabaseQuizzes&&supabaseQuizzes.length>0){// Transform Supabase data to match our Quiz type\nconst transformedQuizzes=supabaseQuizzes.map(quiz=>{var _quiz$questions;return{id:quiz.id,courseId:quiz.course_id,title:quiz.title,description:quiz.description||'',questions:((_quiz$questions=quiz.questions)===null||_quiz$questions===void 0?void 0:_quiz$questions.map(q=>({id:q.id,question:q.question,type:'multiple-choice',options:q.options,correctAnswer:q.correct_answer,points:q.points})))||[],passingScore:quiz.passing_score,timeLimit:quiz.time_limit,attempts:3,// Default value\nisActive:quiz.is_active,createdAt:new Date(quiz.created_at)};});setQuizzes(transformedQuizzes);}else{// Fallback to mock data\nsetQuizzes(mockQuizzes);}}catch(error){console.error('Error loading quizzes:',error);toast.error('حدث خطأ في تحميل الاختبارات');// Fallback to mock data\nsetQuizzes(mockQuizzes);}finally{setLoading(false);}};// Mock data for demonstration\nconst mockQuizzes=[{id:'1',courseId:'1',title:'اختبار أساسيات البرمجة',description:'اختبار شامل لأساسيات البرمجة',questions:[{id:'1',question:'ما هو المتغير؟',type:'multiple-choice',options:['مكان لتخزين البيانات','نوع من الدوال','أمر برمجي'],correctAnswer:0,points:10}],passingScore:70,timeLimit:30,attempts:3,isActive:true,createdAt:new Date()},{id:'2',courseId:'2',title:'اختبار تطوير المواقع',description:'اختبار في HTML و CSS',questions:[],passingScore:80,timeLimit:45,attempts:2,isActive:true,createdAt:new Date()}];React.useEffect(()=>{setQuizzes(mockQuizzes);},[]);const filteredQuizzes=quizzes.filter(quiz=>{var _quiz$description;return quiz.title.toLowerCase().includes(searchTerm.toLowerCase())||((_quiz$description=quiz.description)===null||_quiz$description===void 0?void 0:_quiz$description.toLowerCase().includes(searchTerm.toLowerCase()));});const handleAddQuiz=()=>{setShowAddModal(true);};const handleEditQuiz=quizId=>{const quiz=quizzes.find(q=>q.id===quizId);if(quiz){setSelectedQuiz(quiz);setShowEditModal(true);}};const handleDeleteQuiz=async quizId=>{if(window.confirm('هل أنت متأكد من حذف هذا الاختبار؟')){try{await supabaseService.deleteQuiz(quizId);toast.success('تم حذف الاختبار بنجاح');loadQuizzes();}catch(error){console.error('Error deleting quiz:',error);toast.error('حدث خطأ في حذف الاختبار');}}};const handleQuizAdded=()=>{setShowAddModal(false);loadQuizzes();toast.success('تم إضافة الاختبار بنجاح');};const handleQuizUpdated=()=>{setShowEditModal(false);setSelectedQuiz(null);loadQuizzes();toast.success('تم تحديث الاختبار بنجاح');};const handleViewQuiz=quizId=>{console.log('View quiz:',quizId);};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddQuiz,className:\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u062C\\u062F\\u064A\\u062F\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631...\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:filteredQuizzes.map((quiz,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.1},className:\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-6 h-6 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:quiz.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:quiz.description})]})]}),/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded-full ${quiz.isActive?'bg-green-100 text-green-800':'bg-red-100 text-red-800'}`,children:quiz.isActive?'نشط':'غير نشط'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2 text-sm text-gray-600 mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0623\\u0633\\u0626\\u0644\\u0629:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:quiz.questions.length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u062F\\u0631\\u062C\\u0629 \\u0627\\u0644\\u0646\\u062C\\u0627\\u062D:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium\",children:[quiz.passingScore,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062D\\u062F\\u062F:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium\",children:[quiz.timeLimit,\" \\u062F\\u0642\\u064A\\u0642\\u0629\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0627\\u062A:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:quiz.attempts})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between pt-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewQuiz(quiz.id),className:\"p-2 text-gray-600 hover:text-blue-600 transition-colors\",title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditQuiz(quiz.id),className:\"p-2 text-gray-600 hover:text-green-600 transition-colors\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteQuiz(quiz.id),className:\"p-2 text-gray-600 hover:text-red-600 transition-colors\",title:\"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:new Date(quiz.createdAt).toLocaleDateString('ar-SA')})]})]})},quiz.id))}),filteredQuizzes.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(QuestionMarkCircleIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B\"})]})]});};export default QuizzesManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "toast", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "ClipboardDocumentListIcon", "QuestionMarkCircleIcon", "supabaseService", "jsx", "_jsx", "jsxs", "_jsxs", "QuizzesManagement", "_ref", "onBack", "quizzes", "setQuizzes", "searchTerm", "setSearchTerm", "loading", "setLoading", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "selectedQuiz", "setSelectedQuiz", "loadQuizzes", "supabaseQuizzes", "getAllQuizzes", "length", "transformedQuizzes", "map", "quiz", "_quiz$questions", "id", "courseId", "course_id", "title", "description", "questions", "q", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "correct_answer", "points", "passingScore", "passing_score", "timeLimit", "time_limit", "attempts", "isActive", "is_active", "createdAt", "Date", "created_at", "mockQuizzes", "error", "console", "filteredQuizzes", "filter", "_quiz$description", "toLowerCase", "includes", "handleAddQuiz", "handleEditQuiz", "quizId", "find", "handleDeleteQuiz", "window", "confirm", "deleteQuiz", "success", "handleQuizAdded", "handleQuizUpdated", "handleViewQuiz", "log", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "value", "onChange", "e", "target", "placeholder", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "toLocaleDateString"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/QuizzesManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  ClipboardDocumentListIcon,\n  QuestionMarkCircleIcon\n} from '@heroicons/react/24/outline';\nimport { supabaseService } from '../../services/supabaseService';\n\n// Types\nimport { Quiz } from '../../types';\n\ninterface QuizzesManagementProps {\n  onBack?: () => void;\n}\n\nconst QuizzesManagement: React.FC<QuizzesManagementProps> = ({ onBack }) => {\n  const [quizzes, setQuizzes] = useState<Quiz[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null);\n\n  useEffect(() => {\n    loadQuizzes();\n  }, []);\n\n  const loadQuizzes = async () => {\n    try {\n      setLoading(true);\n      // Try to get from Supabase first\n      const supabaseQuizzes = await supabaseService.getAllQuizzes();\n      if (supabaseQuizzes && supabaseQuizzes.length > 0) {\n        // Transform Supabase data to match our Quiz type\n        const transformedQuizzes = supabaseQuizzes.map(quiz => ({\n          id: quiz.id,\n          courseId: quiz.course_id,\n          title: quiz.title,\n          description: quiz.description || '',\n          questions: quiz.questions?.map((q: any) => ({\n            id: q.id,\n            question: q.question,\n            type: 'multiple-choice' as const,\n            options: q.options,\n            correctAnswer: q.correct_answer,\n            points: q.points\n          })) || [],\n          passingScore: quiz.passing_score,\n          timeLimit: quiz.time_limit,\n          attempts: 3, // Default value\n          isActive: quiz.is_active,\n          createdAt: new Date(quiz.created_at)\n        }));\n        setQuizzes(transformedQuizzes);\n      } else {\n        // Fallback to mock data\n        setQuizzes(mockQuizzes);\n      }\n    } catch (error) {\n      console.error('Error loading quizzes:', error);\n      toast.error('حدث خطأ في تحميل الاختبارات');\n      // Fallback to mock data\n      setQuizzes(mockQuizzes);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock data for demonstration\n  const mockQuizzes: Quiz[] = [\n    {\n      id: '1',\n      courseId: '1',\n      title: 'اختبار أساسيات البرمجة',\n      description: 'اختبار شامل لأساسيات البرمجة',\n      questions: [\n        {\n          id: '1',\n          question: 'ما هو المتغير؟',\n          type: 'multiple-choice',\n          options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر برمجي'],\n          correctAnswer: 0,\n          points: 10\n        }\n      ],\n      passingScore: 70,\n      timeLimit: 30,\n      attempts: 3,\n      isActive: true,\n      createdAt: new Date()\n    },\n    {\n      id: '2',\n      courseId: '2',\n      title: 'اختبار تطوير المواقع',\n      description: 'اختبار في HTML و CSS',\n      questions: [],\n      passingScore: 80,\n      timeLimit: 45,\n      attempts: 2,\n      isActive: true,\n      createdAt: new Date()\n    }\n  ];\n\n  React.useEffect(() => {\n    setQuizzes(mockQuizzes);\n  }, []);\n\n  const filteredQuizzes = quizzes.filter(quiz =>\n    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleAddQuiz = () => {\n    setShowAddModal(true);\n  };\n\n  const handleEditQuiz = (quizId: string) => {\n    const quiz = quizzes.find(q => q.id === quizId);\n    if (quiz) {\n      setSelectedQuiz(quiz);\n      setShowEditModal(true);\n    }\n  };\n\n  const handleDeleteQuiz = async (quizId: string) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الاختبار؟')) {\n      try {\n        await supabaseService.deleteQuiz(quizId);\n        toast.success('تم حذف الاختبار بنجاح');\n        loadQuizzes();\n      } catch (error) {\n        console.error('Error deleting quiz:', error);\n        toast.error('حدث خطأ في حذف الاختبار');\n      }\n    }\n  };\n\n  const handleQuizAdded = () => {\n    setShowAddModal(false);\n    loadQuizzes();\n    toast.success('تم إضافة الاختبار بنجاح');\n  };\n\n  const handleQuizUpdated = () => {\n    setShowEditModal(false);\n    setSelectedQuiz(null);\n    loadQuizzes();\n    toast.success('تم تحديث الاختبار بنجاح');\n  };\n\n  const handleViewQuiz = (quizId: string) => {\n    console.log('View quiz:', quizId);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الاختبارات</h1>\n            <p className=\"text-gray-600\">إنشاء وإدارة اختبارات الكورسات</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddQuiz}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة اختبار جديد</span>\n        </button>\n      </div>\n\n      {/* Search */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            البحث في الاختبارات\n          </label>\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"ابحث عن اختبار...\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        </div>\n      </div>\n\n      {/* Quizzes Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredQuizzes.map((quiz, index) => (\n          <motion.div\n            key={quiz.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <div className=\"p-2 bg-purple-100 rounded-lg\">\n                    <ClipboardDocumentListIcon className=\"w-6 h-6 text-purple-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{quiz.title}</h3>\n                    <p className=\"text-sm text-gray-600\">{quiz.description}</p>\n                  </div>\n                </div>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  quiz.isActive \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {quiz.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n\n              <div className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span>عدد الأسئلة:</span>\n                  <span className=\"font-medium\">{quiz.questions.length}</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>درجة النجاح:</span>\n                  <span className=\"font-medium\">{quiz.passingScore}%</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>الوقت المحدد:</span>\n                  <span className=\"font-medium\">{quiz.timeLimit} دقيقة</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>عدد المحاولات:</span>\n                  <span className=\"font-medium\">{quiz.attempts}</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleViewQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                    title=\"عرض الاختبار\"\n                  >\n                    <EyeIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleEditQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-green-600 transition-colors\"\n                    title=\"تعديل الاختبار\"\n                  >\n                    <PencilIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-red-600 transition-colors\"\n                    title=\"حذف الاختبار\"\n                  >\n                    <TrashIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <span className=\"text-xs text-gray-500\">\n                  {new Date(quiz.createdAt).toLocaleDateString('ar-SA')}\n                </span>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {filteredQuizzes.length === 0 && (\n        <div className=\"text-center py-12\">\n          <QuestionMarkCircleIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد اختبارات</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي اختبارات تطابق البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default QuizzesManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,yBAAyB,CACzBC,sBAAsB,KACjB,6BAA6B,CACpC,OAASC,eAAe,KAAQ,gCAAgC,CAEhE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACrE,KAAM,CAACE,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0B,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC4B,YAAY,CAAEC,eAAe,CAAC,CAAG7B,QAAQ,CAAc,IAAI,CAAC,CAEnEC,SAAS,CAAC,IAAM,CACd6B,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,CAAAQ,eAAe,CAAG,KAAM,CAAArB,eAAe,CAACsB,aAAa,CAAC,CAAC,CAC7D,GAAID,eAAe,EAAIA,eAAe,CAACE,MAAM,CAAG,CAAC,CAAE,CACjD;AACA,KAAM,CAAAC,kBAAkB,CAAGH,eAAe,CAACI,GAAG,CAACC,IAAI,OAAAC,eAAA,OAAK,CACtDC,EAAE,CAAEF,IAAI,CAACE,EAAE,CACXC,QAAQ,CAAEH,IAAI,CAACI,SAAS,CACxBC,KAAK,CAAEL,IAAI,CAACK,KAAK,CACjBC,WAAW,CAAEN,IAAI,CAACM,WAAW,EAAI,EAAE,CACnCC,SAAS,CAAE,EAAAN,eAAA,CAAAD,IAAI,CAACO,SAAS,UAAAN,eAAA,iBAAdA,eAAA,CAAgBF,GAAG,CAAES,CAAM,GAAM,CAC1CN,EAAE,CAAEM,CAAC,CAACN,EAAE,CACRO,QAAQ,CAAED,CAAC,CAACC,QAAQ,CACpBC,IAAI,CAAE,iBAA0B,CAChCC,OAAO,CAAEH,CAAC,CAACG,OAAO,CAClBC,aAAa,CAAEJ,CAAC,CAACK,cAAc,CAC/BC,MAAM,CAAEN,CAAC,CAACM,MACZ,CAAC,CAAC,CAAC,GAAI,EAAE,CACTC,YAAY,CAAEf,IAAI,CAACgB,aAAa,CAChCC,SAAS,CAAEjB,IAAI,CAACkB,UAAU,CAC1BC,QAAQ,CAAE,CAAC,CAAE;AACbC,QAAQ,CAAEpB,IAAI,CAACqB,SAAS,CACxBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACvB,IAAI,CAACwB,UAAU,CACrC,CAAC,EAAC,CAAC,CACHzC,UAAU,CAACe,kBAAkB,CAAC,CAChC,CAAC,IAAM,CACL;AACAf,UAAU,CAAC0C,WAAW,CAAC,CACzB,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C3D,KAAK,CAAC2D,KAAK,CAAC,6BAA6B,CAAC,CAC1C;AACA3C,UAAU,CAAC0C,WAAW,CAAC,CACzB,CAAC,OAAS,CACRtC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAsC,WAAmB,CAAG,CAC1B,CACEvB,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,GAAG,CACbE,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,8BAA8B,CAC3CC,SAAS,CAAE,CACT,CACEL,EAAE,CAAE,GAAG,CACPO,QAAQ,CAAE,gBAAgB,CAC1BC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CAAC,sBAAsB,CAAE,eAAe,CAAE,WAAW,CAAC,CAC/DC,aAAa,CAAE,CAAC,CAChBE,MAAM,CAAE,EACV,CAAC,CACF,CACDC,YAAY,CAAE,EAAE,CAChBE,SAAS,CAAE,EAAE,CACbE,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACD,CACErB,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,GAAG,CACbE,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CAAE,sBAAsB,CACnCC,SAAS,CAAE,EAAE,CACbQ,YAAY,CAAE,EAAE,CAChBE,SAAS,CAAE,EAAE,CACbE,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACF,CAED5D,KAAK,CAACE,SAAS,CAAC,IAAM,CACpBkB,UAAU,CAAC0C,WAAW,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,eAAe,CAAG9C,OAAO,CAAC+C,MAAM,CAAC7B,IAAI,OAAA8B,iBAAA,OACzC,CAAA9B,IAAI,CAACK,KAAK,CAAC0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,UAAU,CAAC+C,WAAW,CAAC,CAAC,CAAC,IAAAD,iBAAA,CAC3D9B,IAAI,CAACM,WAAW,UAAAwB,iBAAA,iBAAhBA,iBAAA,CAAkBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,UAAU,CAAC+C,WAAW,CAAC,CAAC,CAAC,GACpE,CAAC,CAED,KAAM,CAAAE,aAAa,CAAGA,CAAA,GAAM,CAC1B5C,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAA6C,cAAc,CAAIC,MAAc,EAAK,CACzC,KAAM,CAAAnC,IAAI,CAAGlB,OAAO,CAACsD,IAAI,CAAC5B,CAAC,EAAIA,CAAC,CAACN,EAAE,GAAKiC,MAAM,CAAC,CAC/C,GAAInC,IAAI,CAAE,CACRP,eAAe,CAACO,IAAI,CAAC,CACrBT,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA8C,gBAAgB,CAAG,KAAO,CAAAF,MAAc,EAAK,CACjD,GAAIG,MAAM,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAE,CACvD,GAAI,CACF,KAAM,CAAAjE,eAAe,CAACkE,UAAU,CAACL,MAAM,CAAC,CACxCpE,KAAK,CAAC0E,OAAO,CAAC,uBAAuB,CAAC,CACtC/C,WAAW,CAAC,CAAC,CACf,CAAE,MAAOgC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C3D,KAAK,CAAC2D,KAAK,CAAC,yBAAyB,CAAC,CACxC,CACF,CACF,CAAC,CAED,KAAM,CAAAgB,eAAe,CAAGA,CAAA,GAAM,CAC5BrD,eAAe,CAAC,KAAK,CAAC,CACtBK,WAAW,CAAC,CAAC,CACb3B,KAAK,CAAC0E,OAAO,CAAC,yBAAyB,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAC9BpD,gBAAgB,CAAC,KAAK,CAAC,CACvBE,eAAe,CAAC,IAAI,CAAC,CACrBC,WAAW,CAAC,CAAC,CACb3B,KAAK,CAAC0E,OAAO,CAAC,yBAAyB,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAG,cAAc,CAAIT,MAAc,EAAK,CACzCR,OAAO,CAACkB,GAAG,CAAC,YAAY,CAAEV,MAAM,CAAC,CACnC,CAAC,CAED,mBACEzD,KAAA,QAAKoE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBrE,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrE,KAAA,QAAKoE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDlE,MAAM,eACLL,IAAA,WACEwE,OAAO,CAAEnE,MAAO,CAChBiE,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEvE,IAAA,QAAKsE,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EvE,IAAA,SAAM4E,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACD7E,KAAA,QAAAqE,QAAA,eACEvE,IAAA,OAAIsE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,cACtEvE,IAAA,MAAGsE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uKAA8B,CAAG,CAAC,EAC5D,CAAC,EACH,CAAC,cACNrE,KAAA,WACEsE,OAAO,CAAEf,aAAc,CACvBa,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvIvE,IAAA,CAACR,QAAQ,EAAC8E,SAAS,CAAC,SAAS,CAAE,CAAC,cAChCtE,IAAA,SAAAuE,QAAA,CAAM,8FAAiB,CAAM,CAAC,EACxB,CAAC,EACN,CAAC,cAGNvE,IAAA,QAAKsE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDrE,KAAA,QAAAqE,QAAA,eACEvE,IAAA,UAAOsE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0GAEhE,CAAO,CAAC,cACRvE,IAAA,UACEkC,IAAI,CAAC,MAAM,CACX8C,KAAK,CAAExE,UAAW,CAClByE,QAAQ,CAAGC,CAAC,EAAKzE,aAAa,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,+EAAmB,CAC/Bd,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,CACH,CAAC,cAGNtE,IAAA,QAAKsE,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEnB,eAAe,CAAC7B,GAAG,CAAC,CAACC,IAAI,CAAE6D,KAAK,gBAC/BrF,IAAA,CAACV,MAAM,CAACgG,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAEP,KAAK,CAAG,GAAI,CAAE,CACnCf,SAAS,CAAC,wGAAwG,CAAAC,QAAA,cAElHrE,KAAA,QAAKoE,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBrE,KAAA,QAAKoE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDrE,KAAA,QAAKoE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DvE,IAAA,QAAKsE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvE,IAAA,CAACJ,yBAAyB,EAAC0E,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC9D,CAAC,cACNpE,KAAA,QAAAqE,QAAA,eACEvE,IAAA,OAAIsE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAE/C,IAAI,CAACK,KAAK,CAAK,CAAC,cAC7D7B,IAAA,MAAGsE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE/C,IAAI,CAACM,WAAW,CAAI,CAAC,EACxD,CAAC,EACH,CAAC,cACN9B,IAAA,SAAMsE,SAAS,CAAE,kCACf9C,IAAI,CAACoB,QAAQ,CACT,6BAA6B,CAC7B,yBAAyB,EAC5B,CAAA2B,QAAA,CACA/C,IAAI,CAACoB,QAAQ,CAAG,KAAK,CAAG,SAAS,CAC9B,CAAC,EACJ,CAAC,cAEN1C,KAAA,QAAKoE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDrE,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvE,IAAA,SAAAuE,QAAA,CAAM,gEAAY,CAAM,CAAC,cACzBvE,IAAA,SAAMsE,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE/C,IAAI,CAACO,SAAS,CAACV,MAAM,CAAO,CAAC,EACzD,CAAC,cACNnB,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvE,IAAA,SAAAuE,QAAA,CAAM,gEAAY,CAAM,CAAC,cACzBrE,KAAA,SAAMoE,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAE/C,IAAI,CAACe,YAAY,CAAC,GAAC,EAAM,CAAC,EACtD,CAAC,cACNrC,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvE,IAAA,SAAAuE,QAAA,CAAM,sEAAa,CAAM,CAAC,cAC1BrE,KAAA,SAAMoE,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAE/C,IAAI,CAACiB,SAAS,CAAC,iCAAM,EAAM,CAAC,EACxD,CAAC,cACNvC,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvE,IAAA,SAAAuE,QAAA,CAAM,4EAAc,CAAM,CAAC,cAC3BvE,IAAA,SAAMsE,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE/C,IAAI,CAACmB,QAAQ,CAAO,CAAC,EACjD,CAAC,EACH,CAAC,cAENzC,KAAA,QAAKoE,SAAS,CAAC,iEAAiE,CAAAC,QAAA,eAC9ErE,KAAA,QAAKoE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DvE,IAAA,WACEwE,OAAO,CAAEA,CAAA,GAAMJ,cAAc,CAAC5C,IAAI,CAACE,EAAE,CAAE,CACvC4C,SAAS,CAAC,yDAAyD,CACnEzC,KAAK,CAAC,qEAAc,CAAA0C,QAAA,cAEpBvE,IAAA,CAACL,OAAO,EAAC2E,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACTtE,IAAA,WACEwE,OAAO,CAAEA,CAAA,GAAMd,cAAc,CAAClC,IAAI,CAACE,EAAE,CAAE,CACvC4C,SAAS,CAAC,0DAA0D,CACpEzC,KAAK,CAAC,iFAAgB,CAAA0C,QAAA,cAEtBvE,IAAA,CAACP,UAAU,EAAC6E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTtE,IAAA,WACEwE,OAAO,CAAEA,CAAA,GAAMX,gBAAgB,CAACrC,IAAI,CAACE,EAAE,CAAE,CACzC4C,SAAS,CAAC,wDAAwD,CAClEzC,KAAK,CAAC,qEAAc,CAAA0C,QAAA,cAEpBvE,IAAA,CAACN,SAAS,EAAC4E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cACNtE,IAAA,SAAMsE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpC,GAAI,CAAAxB,IAAI,CAACvB,IAAI,CAACsB,SAAS,CAAC,CAAC+C,kBAAkB,CAAC,OAAO,CAAC,CACjD,CAAC,EACJ,CAAC,EACH,CAAC,EAzEDrE,IAAI,CAACE,EA0EA,CACb,CAAC,CACC,CAAC,CAEL0B,eAAe,CAAC/B,MAAM,GAAK,CAAC,eAC3BnB,KAAA,QAAKoE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvE,IAAA,CAACH,sBAAsB,EAACyE,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC3EtE,IAAA,OAAIsE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wFAAgB,CAAI,CAAC,cAC5EvE,IAAA,MAAGsE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,qNAAyC,CAAG,CAAC,EACvE,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAApE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}