import { supabase } from '../config/supabase';
import fs from 'fs';
import path from 'path';

/**
 * إعداد قاعدة البيانات - تشغيل ملفات SQL
 * تم التطوير بواسطة فريق ALaa Abd <PERSON>hamied 2025
 */

async function runSQLFile(filePath: string, description: string) {
  try {
    console.log(`📄 تشغيل ${description}...`);
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // تقسيم الملف إلى استعلامات منفصلة
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
        
        if (error) {
          console.error(`❌ خطأ في تنفيذ الاستعلام: ${statement.substring(0, 50)}...`);
          console.error('تفاصيل الخطأ:', error);
        }
      }
    }
    
    console.log(`✅ تم تشغيل ${description} بنجاح`);
  } catch (error) {
    console.error(`❌ خطأ في قراءة أو تشغيل ${description}:`, error);
    throw error;
  }
}

async function setupDatabase() {
  try {
    console.log('🚀 بدء إعداد قاعدة البيانات...');
    
    const databaseDir = path.join(__dirname, '../database');
    
    // 1. تشغيل ملف إنشاء الجداول
    const schemaPath = path.join(databaseDir, 'schema.sql');
    if (fs.existsSync(schemaPath)) {
      await runSQLFile(schemaPath, 'ملف إنشاء الجداول والفهارس');
    } else {
      console.log('⚠️ ملف schema.sql غير موجود، سيتم تخطيه');
    }
    
    // 2. تشغيل ملف سياسات الأمان
    const securityPath = path.join(databaseDir, 'security.sql');
    if (fs.existsSync(securityPath)) {
      await runSQLFile(securityPath, 'ملف سياسات الأمان');
    } else {
      console.log('⚠️ ملف security.sql غير موجود، سيتم تخطيه');
    }
    
    // 3. تشغيل ملف البيانات الأولية
    const seedPath = path.join(databaseDir, 'seed.sql');
    if (fs.existsSync(seedPath)) {
      await runSQLFile(seedPath, 'ملف البيانات الأولية');
    } else {
      console.log('⚠️ ملف seed.sql غير موجود، سيتم تخطيه');
    }
    
    console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
    
    // عرض معلومات مفيدة
    console.log('\n📋 معلومات مهمة:');
    console.log('- تم إنشاء جميع الجداول والعلاقات');
    console.log('- تم تطبيق سياسات الأمان');
    console.log('- تم إدراج البيانات الأولية');
    console.log('\n🔑 بيانات تسجيل الدخول الافتراضية:');
    console.log('المدير: <EMAIL> / admin123');
    console.log('الطلاب: استخدم أكواد الدخول المعروضة في البيانات الأولية');
    
  } catch (error) {
    console.error('💥 فشل في إعداد قاعدة البيانات:', error);
    throw error;
  }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  setupDatabase()
    .then(() => {
      console.log('✅ تم إعداد قاعدة البيانات بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل إعداد قاعدة البيانات:', error);
      process.exit(1);
    });
}

export { setupDatabase };
