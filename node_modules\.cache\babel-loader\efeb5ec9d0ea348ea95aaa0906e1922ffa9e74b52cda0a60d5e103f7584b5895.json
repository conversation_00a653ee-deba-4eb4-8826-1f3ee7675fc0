{"ast": null, "code": "export const mockCourses=[{id:'1',title:'أساسيات البرمجة',description:'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',categoryId:'programming',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-01'),updatedAt:new Date('2024-01-15')},{id:'2',title:'تطوير المواقع الحديثة',description:'تعلم تطوير المواقع باستخدام أحدث التقنيات',categoryId:'web',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-10'),updatedAt:new Date('2024-01-20')},{id:'3',title:'الذكاء الاصطناعي للمبتدئين',description:'مقدمة شاملة في عالم الذكاء الاصطناعي',categoryId:'ai',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-15'),updatedAt:new Date('2024-01-25')}];export const mockVideos=[{id:'1',courseId:'1',title:'مقدمة في البرمجة',description:'تعرف على أساسيات البرمجة',videoUrl:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:930,// 15:30 in seconds\norderIndex:1,isActive:true,createdAt:new Date('2024-01-01')},{id:'2',courseId:'1',title:'المتغيرات والثوابت',description:'تعلم كيفية استخدام المتغيرات',videoUrl:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:1245,// 20:45 in seconds\norderIndex:2,isActive:true,createdAt:new Date('2024-01-01')}];export const mockQuizzes=[{id:'1',title:'اختبار أساسيات البرمجة',description:'اختبر معرفتك في أساسيات البرمجة',courseId:'1',questions:[{id:'1',question:'ما هو المتغير في البرمجة؟',type:'multiple-choice',options:['مكان لتخزين البيانات','نوع من الدوال','أمر للطباعة','لا شيء مما سبق'],correctAnswer:0,points:10,explanation:'المتغير هو مكان في الذاكرة لتخزين البيانات'}],timeLimit:30,passingScore:70,attempts:3,isActive:true,createdAt:new Date('2024-01-01')}];export const mockCertificates=[{id:'cert-001',studentId:'student-001',courseId:'1',templateUrl:'https://example.com/template/default.pdf',certificateUrl:'https://example.com/certificate/cert-001.pdf',issuedAt:new Date('2024-02-01'),verificationCode:'CERT-001-VERIFY'}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "videos", "pdfs", "quizzes", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "courseId", "videoUrl", "duration", "orderIndex", "mockQuizzes", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "points", "explanation", "timeLimit", "passingScore", "attempts", "mockCertificates", "studentId", "templateUrl", "certificateUrl", "issuedAt", "verificationCode"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n    categoryId: 'programming',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع الحديثة',\n    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n    categoryId: 'web',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    id: '3',\n    title: 'الذكاء الاصطناعي للمبتدئين',\n    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n    categoryId: 'ai',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-25')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    courseId: '1',\n    title: 'مقدمة في البرمجة',\n    description: 'تعرف على أساسيات البرمجة',\n    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: 930, // 15:30 in seconds\n    orderIndex: 1,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: '2',\n    courseId: '1',\n    title: 'المتغيرات والثوابت',\n    description: 'تعلم كيفية استخدام المتغيرات',\n    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: 1245, // 20:45 in seconds\n    orderIndex: 2,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبر معرفتك في أساسيات البرمجة',\n    courseId: '1',\n    questions: [\n      {\n        id: '1',\n        question: 'ما هو المتغير في البرمجة؟',\n        type: 'multiple-choice',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر للطباعة',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0,\n        points: 10,\n        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n      }\n    ],\n    timeLimit: 30,\n    passingScore: 70,\n    attempts: 3,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    templateUrl: 'https://example.com/template/default.pdf',\n    certificateUrl: 'https://example.com/certificate/cert-001.pdf',\n    issuedAt: new Date('2024-02-01'),\n    verificationCode: 'CERT-001-VERIFY'\n  }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,WAAqB,CAAG,CACnC,CACEC,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,4CAA4C,CACzDC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEV,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,2CAA2C,CACxDC,UAAU,CAAE,KAAK,CACjBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEV,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,4BAA4B,CACnCC,WAAW,CAAE,sCAAsC,CACnDC,UAAU,CAAE,IAAI,CAChBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,UAAmB,CAAG,CACjC,CACEZ,EAAE,CAAE,GAAG,CACPa,QAAQ,CAAE,GAAG,CACbZ,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,0BAA0B,CACvCY,QAAQ,CAAE,6CAA6C,CACvDC,QAAQ,CAAE,GAAG,CAAE;AACfC,UAAU,CAAE,CAAC,CACbR,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEV,EAAE,CAAE,GAAG,CACPa,QAAQ,CAAE,GAAG,CACbZ,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,8BAA8B,CAC3CY,QAAQ,CAAE,6CAA6C,CACvDC,QAAQ,CAAE,IAAI,CAAE;AAChBC,UAAU,CAAE,CAAC,CACbR,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAO,WAAmB,CAAG,CACjC,CACEjB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,iCAAiC,CAC9CW,QAAQ,CAAE,GAAG,CACbK,SAAS,CAAE,CACT,CACElB,EAAE,CAAE,GAAG,CACPmB,QAAQ,CAAE,2BAA2B,CACrCC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CACP,sBAAsB,CACtB,eAAe,CACf,aAAa,CACb,gBAAgB,CACjB,CACDC,aAAa,CAAE,CAAC,CAChBC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,4CACf,CAAC,CACF,CACDC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,QAAQ,CAAE,CAAC,CACXnB,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAkB,gBAA+B,CAAG,CAC7C,CACE5B,EAAE,CAAE,UAAU,CACd6B,SAAS,CAAE,aAAa,CACxBhB,QAAQ,CAAE,GAAG,CACbiB,WAAW,CAAE,0CAA0C,CACvDC,cAAc,CAAE,8CAA8C,CAC9DC,QAAQ,CAAE,GAAI,CAAAtB,IAAI,CAAC,YAAY,CAAC,CAChCuB,gBAAgB,CAAE,iBACpB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}