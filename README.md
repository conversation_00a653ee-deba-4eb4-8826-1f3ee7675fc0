# منصة ALaa Abd Hamied للكورسات الإلكترونية

![منصة ALaa Abd Hamied](https://img.shields.io/badge/Platform-ALaa%20Abd%20Hamied-blue)
![Developer](https://img.shields.io/badge/Developer-ALaa%20Abd%20Elhamied%20Team-green)
![Year](https://img.shields.io/badge/Year-2025-orange)
![Status](https://img.shields.io/badge/Status-Active-success)

## 🎯 نظرة عامة

منصة ALaa Abd Hamied هي منصة تعليمية إلكترونية متقدمة تم تطويرها بواسطة **فريق ALaa Abd Elhamied 2025** لتوفير تجربة تعليمية تفاعلية ومبتكرة.

## 👥 فريق التطوير

**فريق ALaa Abd <PERSON>hamied 2025**
- متخصص في تطوير الحلول التعليمية الرقمية
- خبرة في التقنيات الحديثة والمنصات التفاعلية
- التزام بأعلى معايير الجودة والأمان

## ✨ المميزات

### للطلاب:
- 📚 مشاهدة الكورسات التفاعلية
- 📝 أداء الاختبارات والتقييمات
- 🏆 الحصول على الشهادات
- 📊 متابعة التقدم الدراسي
- 🤖 مساعد ذكي للدعم

### للمدراء:
- 👥 إدارة الطلاب والمستخدمين
- 📚 إدارة الكورسات والمحتوى
- 📝 إنشاء وإدارة الاختبارات
- 🏆 إصدار الشهادات
- 📊 تقارير وإحصائيات شاملة

## 🛠 التقنيات المستخدمة

- **Frontend:** React 18, TypeScript, Tailwind CSS
- **Backend:** Supabase (PostgreSQL)
- **Authentication:** Firebase Auth + Supabase RLS
- **Hosting:** Firebase Hosting
- **UI/UX:** Framer Motion, Heroicons
- **State Management:** React Hooks

## 🚀 الرابط المباشر

**المنصة متاحة على:** [https://alaa-courses-platform.web.app](https://alaa-courses-platform.web.app)

## 🔑 بيانات تسجيل الدخول للاختبار

### المدير:
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Admin@123456`

### الطلاب (أكواد الدخول - 7 أرقام):
- أحمد محمد علي: `1234567`
- فاطمة أحمد حسن: `9876543`
- محمد علي سالم: `5555555`
- سارة خالد محمود: `1111111`
- عبدالله يوسف أحمد: `7777777`
- نور الهدى إبراهيم: `3333333`
- حسام الدين محمد: `8888888`
- ريم عبدالرحمن: `2222222`

## 📦 التثبيت والتشغيل

```bash
# تحميل المشروع
git clone [repository-url]

# الانتقال لمجلد المشروع
cd alaa-courses-platform

# تثبيت المكتبات
npm install

# تشغيل المشروع محلياً
npm start

# بناء المشروع للإنتاج
npm run build

# نشر على Firebase
firebase deploy
```

## 🗄 قاعدة البيانات

المشروع يستخدم نظام قاعدة بيانات هجين:
- **Supabase:** قاعدة البيانات الرئيسية (PostgreSQL)
- **Firebase:** المصادقة والاستضافة

### إدخال البيانات التجريبية:
```bash
npm run seed
```

## 🔒 الأمان

- Row Level Security (RLS) في Supabase
- مصادقة متعددة المستويات
- تشفير البيانات الحساسة
- حماية من هجمات CSRF و XSS

## 📱 التوافق

- ✅ جميع المتصفحات الحديثة
- ✅ الأجهزة المحمولة والأجهزة اللوحية
- ✅ دعم اللغة العربية (RTL)
- ✅ تصميم متجاوب

## 🤝 المساهمة

هذا المشروع تم تطويره بواسطة **فريق ALaa Abd Elhamied 2025**. 

## 📄 الترخيص

© 2025 فريق ALaa Abd Elhamied. جميع الحقوق محفوظة.

## 📞 التواصل

للاستفسارات والدعم التقني، يرجى التواصل مع فريق ALaa Abd Elhamied 2025.

---

**تم التطوير بـ ❤️ بواسطة فريق ALaa Abd Elhamied 2025**
