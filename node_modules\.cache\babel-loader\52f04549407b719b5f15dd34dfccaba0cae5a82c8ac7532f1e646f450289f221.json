{"ast": null, "code": "import { db } from '../config/firebase';\nimport { collection, doc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { mockCourses, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\nclass DataService {\n  // Courses\n  async getCourses() {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n  async getCourse(id) {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n  async addCourse(course) {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n  async updateCourse(id, course) {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n  async deleteCourse(id) {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents() {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n  async getStudent(id) {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes() {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n  async getQuiz(id) {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates() {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n  async getStudentCertificates(studentId) {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.length * 299,\n      // Mock revenue calculation\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\nexport const dataService = new DataService();", "map": {"version": 3, "names": ["db", "collection", "doc", "addDoc", "updateDoc", "deleteDoc", "mockCourses", "mockQuizzes", "mockCertificates", "mockStudents", "DataService", "getCourses", "error", "console", "getCourse", "id", "course", "find", "c", "addCourse", "doc<PERSON>ef", "updateCourse", "deleteCourse", "getStudents", "getStudent", "student", "s", "getQuizzes", "getQuiz", "quiz", "q", "getCertificates", "getStudentCertificates", "studentId", "filter", "cert", "getAnalytics", "totalStudents", "length", "totalCourses", "totalQuizzes", "totalCertificates", "revenue", "enrollments", "reduce", "sum", "enrolledCourses", "dataService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/dataService.ts"], "sourcesContent": ["import { db } from '../config/firebase';\nimport { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { Course, Student, Quiz, Certificate } from '../types';\nimport { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\n\nclass DataService {\n  // Courses\n  async getCourses(): Promise<Course[]> {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n\n  async getCourse(id: string): Promise<Course | null> {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n\n  async addCourse(course: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n\n  async updateCourse(id: string, course: Partial<Course>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n\n  async deleteCourse(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents(): Promise<Student[]> {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n\n  async getStudent(id: string): Promise<Student | null> {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes(): Promise<Quiz[]> {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n\n  async getQuiz(id: string): Promise<Quiz | null> {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates(): Promise<Certificate[]> {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n\n  async getStudentCertificates(studentId: string): Promise<Certificate[]> {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.length * 299, // Mock revenue calculation\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\n\nexport const dataService = new DataService();\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,oBAAoB;AACvC,SAASC,UAAU,EAAWC,GAAG,EAAUC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QAAQ,oBAAoB;AAEnG,SAASC,WAAW,EAAcC,WAAW,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC5F,SAASC,YAAY,QAAQ,sBAAsB;AAEnD,MAAMC,WAAW,CAAC;EAChB;EACA,MAAMC,UAAUA,CAAA,EAAsB;IACpC,IAAI;MACF;MACA,OAAOL,WAAW;IACpB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAON,WAAW,CAAC,CAAC;IACtB;EACF;EAEA,MAAMQ,SAASA,CAACC,EAAU,EAA0B;IAClD,IAAI;MACF,MAAMC,MAAM,GAAGV,WAAW,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,EAAE,KAAKA,EAAE,CAAC;MACjD,OAAOC,MAAM,IAAI,IAAI;IACvB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,IAAI;IACb;EACF;EAEA,MAAMO,SAASA,CAACH,MAA0B,EAAmB;IAC3D,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMjB,MAAM,CAACF,UAAU,CAACD,EAAE,EAAE,SAAS,CAAC,EAAEgB,MAAM,CAAC;MAC9D,OAAOI,MAAM,CAACL,EAAE;IAClB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMS,YAAYA,CAACN,EAAU,EAAEC,MAAuB,EAAiB;IACrE,IAAI;MACF,MAAMZ,SAAS,CAACF,GAAG,CAACF,EAAE,EAAE,SAAS,EAAEe,EAAE,CAAC,EAAEC,MAAM,CAAC;IACjD,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMU,YAAYA,CAACP,EAAU,EAAiB;IAC5C,IAAI;MACF,MAAMV,SAAS,CAACH,GAAG,CAACF,EAAE,EAAE,SAAS,EAAEe,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMW,WAAWA,CAAA,EAAuB;IACtC,IAAI;MACF,OAAOd,YAAY;IACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAOH,YAAY;IACrB;EACF;EAEA,MAAMe,UAAUA,CAACT,EAAU,EAA2B;IACpD,IAAI;MACF,MAAMU,OAAO,GAAGhB,YAAY,CAACQ,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKA,EAAE,CAAC;MACnD,OAAOU,OAAO,IAAI,IAAI;IACxB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMe,UAAUA,CAAA,EAAoB;IAClC,IAAI;MACF,OAAOpB,WAAW;IACpB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOL,WAAW;IACpB;EACF;EAEA,MAAMqB,OAAOA,CAACb,EAAU,EAAwB;IAC9C,IAAI;MACF,MAAMc,IAAI,GAAGtB,WAAW,CAACU,IAAI,CAACa,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKA,EAAE,CAAC;MAC/C,OAAOc,IAAI,IAAI,IAAI;IACrB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMmB,eAAeA,CAAA,EAA2B;IAC9C,IAAI;MACF,OAAOvB,gBAAgB;IACzB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOJ,gBAAgB;IACzB;EACF;EAEA,MAAMwB,sBAAsBA,CAACC,SAAiB,EAA0B;IACtE,IAAI;MACF,OAAOzB,gBAAgB,CAAC0B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,SAAS,KAAKA,SAAS,CAAC;IACtE,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,EAAE;IACX;EACF;;EAEA;EACA,MAAMwB,YAAYA,CAAA,EAAG;IACnB,OAAO;MACLC,aAAa,EAAE5B,YAAY,CAAC6B,MAAM;MAClCC,YAAY,EAAEjC,WAAW,CAACgC,MAAM;MAChCE,YAAY,EAAEjC,WAAW,CAAC+B,MAAM;MAChCG,iBAAiB,EAAEjC,gBAAgB,CAAC8B,MAAM;MAC1CI,OAAO,EAAEpC,WAAW,CAACgC,MAAM,GAAG,GAAG;MAAE;MACnCK,WAAW,EAAElC,YAAY,CAACmC,MAAM,CAAC,CAACC,GAAG,EAAEpB,OAAO,KAAKoB,GAAG,GAAGpB,OAAO,CAACqB,eAAe,CAACR,MAAM,EAAE,CAAC;IAC5F,CAAC;EACH;AACF;AAEA,OAAO,MAAMS,WAAW,GAAG,IAAIrC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}