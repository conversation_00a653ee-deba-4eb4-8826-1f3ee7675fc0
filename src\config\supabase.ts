import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://srnyumtbsyxiqkvwkcpi.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybnl1bXRic3l4aXFrdndrY3BpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNjYzMjIsImV4cCI6MjA2Nzk0MjMyMn0.ROA5cGM5AQCvIBB-BGOLZPgEzR9rEoBkjLPboJJ0qJk';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Course {
  id: string;
  title: string;
  description: string;
  category_id: string;
  instructor_id: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  is_active: boolean;
}

export interface Video {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  video_url: string;
  duration?: number;
  order_index: number;
  created_at: string;
}

export interface Student {
  id: string;
  access_code: string;
  name?: string;
  email?: string;
  enrolled_courses: string[];
  created_at: string;
  is_active: boolean;
}

export interface Quiz {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  passing_score: number;
  created_at: string;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correct_answer: number;
  points: number;
}

export interface Certificate {
  id: string;
  student_id: string;
  course_id: string;
  certificate_url: string;
  issued_at: string;
}
