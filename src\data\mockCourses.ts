import { Course, Video, Quiz, Certificate } from '../types';

export const mockCourses: Course[] = [
  {
    id: '1',
    title: 'أساسيات البرمجة',
    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',
    categoryId: 'programming',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    title: 'تطوير المواقع الحديثة',
    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',
    categoryId: 'web',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '3',
    title: 'الذكاء الاصطناعي للمبتدئين',
    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',
    categoryId: 'ai',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-25')
  }
];

export const mockVideos: Video[] = [
  {
    id: '1',
    courseId: '1',
    title: 'مقدمة في البرمجة',
    description: 'تعرف على أساسيات البرمجة',
    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    duration: 930, // 15:30 in seconds
    orderIndex: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: '2',
    courseId: '1',
    title: 'المتغيرات والثوابت',
    description: 'تعلم كيفية استخدام المتغيرات',
    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    duration: 1245, // 20:45 in seconds
    orderIndex: 2,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockQuizzes: Quiz[] = [
  {
    id: '1',
    title: 'اختبار أساسيات البرمجة',
    description: 'اختبر معرفتك في أساسيات البرمجة',
    courseId: '1',
    questions: [
      {
        id: '1',
        question: 'ما هو المتغير في البرمجة؟',
        type: 'multiple-choice',
        options: [
          'مكان لتخزين البيانات',
          'نوع من الدوال',
          'أمر للطباعة',
          'لا شيء مما سبق'
        ],
        correctAnswer: 0,
        points: 10,
        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'
      }
    ],
    timeLimit: 30,
    passingScore: 70,
    attempts: 3,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockCertificates: Certificate[] = [
  {
    id: 'cert-001',
    studentId: 'student-001',
    courseId: '1',
    templateUrl: 'https://example.com/template/default.pdf',
    certificateUrl: 'https://example.com/certificate/cert-001.pdf',
    issuedAt: new Date('2024-02-01'),
    verificationCode: 'CERT-001-VERIFY'
  }
];
