import { Course, Video, Quiz, Certificate } from '../types';

export const mockCourses: Course[] = [
  {
    id: '1',
    title: 'أساسيات البرمجة بـ JavaScript',
    description: 'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',
    categoryId: 'cat-1',
    instructorId: 'admin-001',
    thumbnailUrl: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',
    price: 299,
    duration: 25,
    level: 'beginner',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 15,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    title: 'تطوير المواقع بـ React',
    description: 'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',
    categoryId: 'cat-1',
    instructorId: 'admin-001',
    thumbnailUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
    price: 499,
    duration: 40,
    level: 'intermediate',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 12,
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '3',
    title: 'تطوير تطبيقات الهاتف بـ React Native',
    description: 'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',
    categoryId: 'cat-2',
    instructorId: 'admin-001',
    thumbnailUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    price: 699,
    duration: 50,
    level: 'intermediate',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 8,
    isActive: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01')
  }
];

export const mockVideos: Video[] = [
  {
    id: 'video-1',
    courseId: '1',
    title: 'مقدمة في JavaScript',
    videoUrl: 'https://example.com/video1.mp4',
    duration: 15,
    orderIndex: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'video-2',
    courseId: '1',
    title: 'المتغيرات والثوابت',
    videoUrl: 'https://example.com/video2.mp4',
    duration: 20,
    orderIndex: 2,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockQuizzes: Quiz[] = [
  {
    id: 'quiz-1',
    courseId: '1',
    title: 'اختبار JavaScript الأساسي',
    description: 'اختبار لقياس فهمك لأساسيات JavaScript',
    questions: [
      {
        id: 'q1',
        question: 'ما هو JavaScript؟',
        type: 'multiple-choice',
        options: ['لغة برمجة', 'قاعدة بيانات', 'نظام تشغيل', 'متصفح'],
        correctAnswer: 0,
        points: 1
      }
    ],
    passingScore: 70,
    timeLimit: 30,
    attempts: 3,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockCertificates: Certificate[] = [
  {
    id: 'cert-001',
    studentId: 'student-001',
    courseId: '1',
    templateUrl: '/templates/cert-template.pdf',
    certificateUrl: '/certificates/cert-001.pdf',
    issuedAt: new Date('2024-01-20'),
    verificationCode: 'CERT-2024-001'
  }
];
