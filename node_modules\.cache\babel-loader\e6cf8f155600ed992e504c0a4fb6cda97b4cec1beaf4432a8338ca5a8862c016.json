{"ast": null, "code": "import{supabase}from'../config/supabase';export class SupabaseService{// Admin Services\nasync createAdmin(adminData){const{data,error}=await supabase.from('admins').insert([{email:adminData.email,name:adminData.name,password_hash:adminData.passwordHash,permissions:adminData.permissions||['all'],avatar_url:adminData.avatarUrl}]).select().single();if(error)throw error;return data;}async getAdminByEmail(email){const{data,error}=await supabase.from('admins').select('*').eq('email',email).eq('is_active',true).single();if(error){console.error('Error fetching admin:',error);return null;}return data;}// Student Services\nasync createStudent(studentData){const{data,error}=await supabase.from('students').insert([{access_code:studentData.accessCode,name:studentData.name,email:studentData.email,avatar_url:studentData.avatarUrl}]).select().single();if(error)throw error;return data;}async getStudentByAccessCode(accessCode){try{const{data,error}=await supabase.from('students').select(`\n          *,\n          student_enrollments (\n            course_id,\n            enrolled_at,\n            completed_at,\n            progress\n          ),\n          certificates (\n            id,\n            course_id,\n            certificate_url,\n            grade,\n            issued_at\n          )\n        `).eq('access_code',accessCode).eq('is_active',true).single();if(error){console.warn('Supabase error, falling back to mock data:',error);return null;// Will trigger fallback in authService\n}return data;}catch(error){console.warn('Supabase connection failed, falling back to mock data:',error);return null;// Will trigger fallback in authService\n}}async getAllStudents(){const{data,error}=await supabase.from('students').select(`\n        *,\n        student_enrollments (\n          course_id,\n          enrolled_at,\n          completed_at,\n          progress\n        ),\n        certificates (\n          id,\n          course_id,\n          certificate_url,\n          grade,\n          issued_at\n        )\n      `).eq('is_active',true).order('created_at',{ascending:false});if(error)throw error;return data;}async updateStudent(id,updates){const{data,error}=await supabase.from('students').update({name:updates.name,email:updates.email,avatar_url:updates.avatarUrl,is_active:updates.isActive,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteStudent(id){const{error}=await supabase.from('students').delete().eq('id',id);if(error)throw error;}// Category Services\nasync createCategory(categoryData){const{data,error}=await supabase.from('categories').insert([{name:categoryData.name,description:categoryData.description}]).select().single();if(error)throw error;return data;}async getAllCategories(){const{data,error}=await supabase.from('categories').select('*').eq('is_active',true).order('name');if(error)throw error;return data;}async updateCategory(id,updates){const{data,error}=await supabase.from('categories').update({name:updates.name,description:updates.description,is_active:updates.isActive,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteCategory(id){const{error}=await supabase.from('categories').delete().eq('id',id);if(error)throw error;}// Course Services\nasync createCourse(courseData){const{data,error}=await supabase.from('courses').insert([{title:courseData.title,description:courseData.description,category_id:courseData.categoryId,instructor_id:courseData.instructorId,thumbnail_url:courseData.thumbnailUrl,price:courseData.price||0,duration_hours:courseData.durationHours||0,level:courseData.level||'beginner'}]).select().single();if(error)throw error;return data;}async getAllCourses(){const{data,error}=await supabase.from('courses').select(`\n        *,\n        categories (\n          id,\n          name\n        ),\n        admins (\n          id,\n          name\n        ),\n        videos (\n          id,\n          title,\n          duration,\n          order_index\n        ),\n        quizzes (\n          id,\n          title\n        )\n      `).eq('is_active',true).order('created_at',{ascending:false});if(error)throw error;return data;}async getCourseById(id){const{data,error}=await supabase.from('courses').select(`\n        *,\n        categories (\n          id,\n          name\n        ),\n        admins (\n          id,\n          name\n        ),\n        videos (\n          id,\n          title,\n          description,\n          video_url,\n          duration,\n          order_index\n        ),\n        quizzes (\n          id,\n          title,\n          description,\n          passing_score,\n          time_limit,\n          questions (\n            id,\n            question,\n            options,\n            correct_answer,\n            points,\n            order_index\n          )\n        )\n      `).eq('id',id).eq('is_active',true).single();if(error)throw error;return data;}async updateCourse(id,updates){const{data,error}=await supabase.from('courses').update({title:updates.title,description:updates.description,category_id:updates.categoryId,thumbnail_url:updates.thumbnailUrl,price:updates.price,duration_hours:updates.durationHours,level:updates.level,is_active:updates.isActive,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteCourse(id){const{error}=await supabase.from('courses').delete().eq('id',id);if(error)throw error;}// Video Services\nasync createVideo(videoData){const{data,error}=await supabase.from('videos').insert([{course_id:videoData.courseId,title:videoData.title,description:videoData.description,video_url:videoData.videoUrl,duration:videoData.duration||0,order_index:videoData.orderIndex||0}]).select().single();if(error)throw error;return data;}async getVideosByCourseId(courseId){const{data,error}=await supabase.from('videos').select('*').eq('course_id',courseId).order('order_index');if(error)throw error;return data;}async updateVideo(id,updates){const{data,error}=await supabase.from('videos').update({title:updates.title,description:updates.description,video_url:updates.videoUrl,duration:updates.duration,order_index:updates.orderIndex,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteVideo(id){const{error}=await supabase.from('videos').delete().eq('id',id);if(error)throw error;}// Quiz Services\nasync createQuiz(quizData){const{data,error}=await supabase.from('quizzes').insert([{course_id:quizData.courseId,title:quizData.title,description:quizData.description,passing_score:quizData.passingScore||70,time_limit:quizData.timeLimit||30}]).select().single();if(error)throw error;return data;}async getQuizzesByCourseId(courseId){const{data,error}=await supabase.from('quizzes').select(`\n        *,\n        questions (\n          id,\n          question,\n          options,\n          correct_answer,\n          points,\n          order_index\n        )\n      `).eq('course_id',courseId).eq('is_active',true).order('created_at');if(error)throw error;return data;}async getAllQuizzes(){const{data,error}=await supabase.from('quizzes').select(`\n        *,\n        courses (\n          id,\n          title\n        ),\n        questions (\n          id,\n          question,\n          options,\n          correct_answer,\n          points,\n          order_index\n        )\n      `).eq('is_active',true).order('created_at',{ascending:false});if(error)throw error;return data;}async updateQuiz(id,updates){const{data,error}=await supabase.from('quizzes').update({title:updates.title,description:updates.description,passing_score:updates.passingScore,time_limit:updates.timeLimit,is_active:updates.isActive,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteQuiz(id){const{error}=await supabase.from('quizzes').delete().eq('id',id);if(error)throw error;}// Question Services\nasync createQuestion(questionData){const{data,error}=await supabase.from('questions').insert([{quiz_id:questionData.quizId,question:questionData.question,options:questionData.options,correct_answer:questionData.correctAnswer,points:questionData.points||1,order_index:questionData.orderIndex||0}]).select().single();if(error)throw error;return data;}async getQuestionsByQuizId(quizId){const{data,error}=await supabase.from('questions').select('*').eq('quiz_id',quizId).order('order_index');if(error)throw error;return data;}async updateQuestion(id,updates){const{data,error}=await supabase.from('questions').update({question:updates.question,options:updates.options,correct_answer:updates.correctAnswer,points:updates.points,order_index:updates.orderIndex,updated_at:new Date().toISOString()}).eq('id',id).select().single();if(error)throw error;return data;}async deleteQuestion(id){const{error}=await supabase.from('questions').delete().eq('id',id);if(error)throw error;}// Certificate Services\nasync createCertificate(certificateData){const{data,error}=await supabase.from('certificates').insert([{student_id:certificateData.studentId,course_id:certificateData.courseId,certificate_url:certificateData.certificateUrl,grade:certificateData.grade}]).select().single();if(error)throw error;return data;}async getCertificatesByStudentId(studentId){const{data,error}=await supabase.from('certificates').select(`\n        *,\n        courses (\n          id,\n          title,\n          thumbnail_url\n        )\n      `).eq('student_id',studentId).order('issued_at',{ascending:false});if(error)throw error;return data;}async getAllCertificates(){const{data,error}=await supabase.from('certificates').select(`\n        *,\n        students (\n          id,\n          name,\n          access_code\n        ),\n        courses (\n          id,\n          title,\n          thumbnail_url\n        )\n      `).order('issued_at',{ascending:false});if(error)throw error;return data;}async deleteCertificate(id){const{error}=await supabase.from('certificates').delete().eq('id',id);if(error)throw error;}// Student Enrollment Services\nasync enrollStudent(studentId,courseId){const{data,error}=await supabase.from('student_enrollments').insert([{student_id:studentId,course_id:courseId}]).select().single();if(error)throw error;return data;}async updateStudentProgress(studentId,courseId,progress){const{data,error}=await supabase.from('student_enrollments').update({progress:progress,completed_at:progress>=100?new Date().toISOString():null}).eq('student_id',studentId).eq('course_id',courseId).select().single();if(error)throw error;return data;}async getStudentEnrollments(studentId){const{data,error}=await supabase.from('student_enrollments').select(`\n        *,\n        courses (\n          id,\n          title,\n          description,\n          thumbnail_url,\n          videos (\n            id,\n            title,\n            duration\n          )\n        )\n      `).eq('student_id',studentId).order('enrolled_at',{ascending:false});if(error)throw error;return data;}}export const supabaseService=new SupabaseService();", "map": {"version": 3, "names": ["supabase", "SupabaseService", "createAdmin", "adminData", "data", "error", "from", "insert", "email", "name", "password_hash", "passwordHash", "permissions", "avatar_url", "avatarUrl", "select", "single", "getAdminByEmail", "eq", "console", "createStudent", "studentData", "access_code", "accessCode", "getStudentByAccessCode", "warn", "getAllStudents", "order", "ascending", "updateStudent", "id", "updates", "update", "is_active", "isActive", "updated_at", "Date", "toISOString", "deleteStudent", "delete", "createCategory", "categoryData", "description", "getAllCategories", "updateCategory", "deleteCategory", "createCourse", "courseData", "title", "category_id", "categoryId", "instructor_id", "instructorId", "thumbnail_url", "thumbnailUrl", "price", "duration_hours", "durationHours", "level", "getAllCourses", "getCourseById", "updateCourse", "deleteCourse", "createVideo", "videoData", "course_id", "courseId", "video_url", "videoUrl", "duration", "order_index", "orderIndex", "getVideosByCourseId", "updateVideo", "deleteVideo", "createQuiz", "quizData", "passing_score", "passingScore", "time_limit", "timeLimit", "getQuizzesByCourseId", "getAllQuizzes", "updateQuiz", "deleteQuiz", "createQuestion", "questionData", "quiz_id", "quizId", "question", "options", "correct_answer", "<PERSON><PERSON><PERSON><PERSON>", "points", "getQuestionsByQuizId", "updateQuestion", "deleteQuestion", "createCertificate", "certificateData", "student_id", "studentId", "certificate_url", "certificateUrl", "grade", "getCertificatesByStudentId", "getAllCertificates", "deleteCertificate", "enrollStudent", "updateStudentProgress", "progress", "completed_at", "getStudentEnrollments", "supabaseService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/supabaseService.ts"], "sourcesContent": ["import { supabase } from '../config/supabase';\nimport { Student, Course, Category, Quiz, Certificate, Admin } from '../types';\n\nexport class SupabaseService {\n  // Admin Services\n  async createAdmin(adminData: {\n    email: string;\n    name: string;\n    passwordHash: string;\n    permissions?: string[];\n    avatarUrl?: string;\n  }) {\n    const { data, error } = await supabase\n      .from('admins')\n      .insert([{\n        email: adminData.email,\n        name: adminData.name,\n        password_hash: adminData.passwordHash,\n        permissions: adminData.permissions || ['all'],\n        avatar_url: adminData.avatarUrl\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getAdminByEmail(email: string) {\n    const { data, error } = await supabase\n      .from('admins')\n      .select('*')\n      .eq('email', email)\n      .eq('is_active', true)\n      .single();\n\n    if (error) {\n      console.error('Error fetching admin:', error);\n      return null;\n    }\n    return data;\n  }\n\n  // Student Services\n  async createStudent(studentData: {\n    accessCode: string;\n    name?: string;\n    email?: string;\n    avatarUrl?: string;\n  }) {\n    const { data, error } = await supabase\n      .from('students')\n      .insert([{\n        access_code: studentData.accessCode,\n        name: studentData.name,\n        email: studentData.email,\n        avatar_url: studentData.avatarUrl\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getStudentByAccessCode(accessCode: string) {\n    try {\n      const { data, error } = await supabase\n        .from('students')\n        .select(`\n          *,\n          student_enrollments (\n            course_id,\n            enrolled_at,\n            completed_at,\n            progress\n          ),\n          certificates (\n            id,\n            course_id,\n            certificate_url,\n            grade,\n            issued_at\n          )\n        `)\n        .eq('access_code', accessCode)\n        .eq('is_active', true)\n        .single();\n\n      if (error) {\n        console.warn('Supabase error, falling back to mock data:', error);\n        return null; // Will trigger fallback in authService\n      }\n      return data;\n    } catch (error) {\n      console.warn('Supabase connection failed, falling back to mock data:', error);\n      return null; // Will trigger fallback in authService\n    }\n  }\n\n  async getAllStudents() {\n    const { data, error } = await supabase\n      .from('students')\n      .select(`\n        *,\n        student_enrollments (\n          course_id,\n          enrolled_at,\n          completed_at,\n          progress\n        ),\n        certificates (\n          id,\n          course_id,\n          certificate_url,\n          grade,\n          issued_at\n        )\n      `)\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateStudent(id: string, updates: Partial<{\n    name: string;\n    email: string;\n    avatarUrl: string;\n    isActive: boolean;\n  }>) {\n    const { data, error } = await supabase\n      .from('students')\n      .update({\n        name: updates.name,\n        email: updates.email,\n        avatar_url: updates.avatarUrl,\n        is_active: updates.isActive,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteStudent(id: string) {\n    const { error } = await supabase\n      .from('students')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Category Services\n  async createCategory(categoryData: {\n    name: string;\n    description?: string;\n  }) {\n    const { data, error } = await supabase\n      .from('categories')\n      .insert([{\n        name: categoryData.name,\n        description: categoryData.description\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getAllCategories() {\n    const { data, error } = await supabase\n      .from('categories')\n      .select('*')\n      .eq('is_active', true)\n      .order('name');\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateCategory(id: string, updates: Partial<{\n    name: string;\n    description: string;\n    isActive: boolean;\n  }>) {\n    const { data, error } = await supabase\n      .from('categories')\n      .update({\n        name: updates.name,\n        description: updates.description,\n        is_active: updates.isActive,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteCategory(id: string) {\n    const { error } = await supabase\n      .from('categories')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Course Services\n  async createCourse(courseData: {\n    title: string;\n    description: string;\n    categoryId: string;\n    instructorId: string;\n    thumbnailUrl?: string;\n    price?: number;\n    durationHours?: number;\n    level?: string;\n  }) {\n    const { data, error } = await supabase\n      .from('courses')\n      .insert([{\n        title: courseData.title,\n        description: courseData.description,\n        category_id: courseData.categoryId,\n        instructor_id: courseData.instructorId,\n        thumbnail_url: courseData.thumbnailUrl,\n        price: courseData.price || 0,\n        duration_hours: courseData.durationHours || 0,\n        level: courseData.level || 'beginner'\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getAllCourses() {\n    const { data, error } = await supabase\n      .from('courses')\n      .select(`\n        *,\n        categories (\n          id,\n          name\n        ),\n        admins (\n          id,\n          name\n        ),\n        videos (\n          id,\n          title,\n          duration,\n          order_index\n        ),\n        quizzes (\n          id,\n          title\n        )\n      `)\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getCourseById(id: string) {\n    const { data, error } = await supabase\n      .from('courses')\n      .select(`\n        *,\n        categories (\n          id,\n          name\n        ),\n        admins (\n          id,\n          name\n        ),\n        videos (\n          id,\n          title,\n          description,\n          video_url,\n          duration,\n          order_index\n        ),\n        quizzes (\n          id,\n          title,\n          description,\n          passing_score,\n          time_limit,\n          questions (\n            id,\n            question,\n            options,\n            correct_answer,\n            points,\n            order_index\n          )\n        )\n      `)\n      .eq('id', id)\n      .eq('is_active', true)\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateCourse(id: string, updates: Partial<{\n    title: string;\n    description: string;\n    categoryId: string;\n    thumbnailUrl: string;\n    price: number;\n    durationHours: number;\n    level: string;\n    isActive: boolean;\n  }>) {\n    const { data, error } = await supabase\n      .from('courses')\n      .update({\n        title: updates.title,\n        description: updates.description,\n        category_id: updates.categoryId,\n        thumbnail_url: updates.thumbnailUrl,\n        price: updates.price,\n        duration_hours: updates.durationHours,\n        level: updates.level,\n        is_active: updates.isActive,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteCourse(id: string) {\n    const { error } = await supabase\n      .from('courses')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Video Services\n  async createVideo(videoData: {\n    courseId: string;\n    title: string;\n    description?: string;\n    videoUrl: string;\n    duration?: number;\n    orderIndex?: number;\n  }) {\n    const { data, error } = await supabase\n      .from('videos')\n      .insert([{\n        course_id: videoData.courseId,\n        title: videoData.title,\n        description: videoData.description,\n        video_url: videoData.videoUrl,\n        duration: videoData.duration || 0,\n        order_index: videoData.orderIndex || 0\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getVideosByCourseId(courseId: string) {\n    const { data, error } = await supabase\n      .from('videos')\n      .select('*')\n      .eq('course_id', courseId)\n      .order('order_index');\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateVideo(id: string, updates: Partial<{\n    title: string;\n    description: string;\n    videoUrl: string;\n    duration: number;\n    orderIndex: number;\n  }>) {\n    const { data, error } = await supabase\n      .from('videos')\n      .update({\n        title: updates.title,\n        description: updates.description,\n        video_url: updates.videoUrl,\n        duration: updates.duration,\n        order_index: updates.orderIndex,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteVideo(id: string) {\n    const { error } = await supabase\n      .from('videos')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Quiz Services\n  async createQuiz(quizData: {\n    courseId: string;\n    title: string;\n    description?: string;\n    passingScore?: number;\n    timeLimit?: number;\n  }) {\n    const { data, error } = await supabase\n      .from('quizzes')\n      .insert([{\n        course_id: quizData.courseId,\n        title: quizData.title,\n        description: quizData.description,\n        passing_score: quizData.passingScore || 70,\n        time_limit: quizData.timeLimit || 30\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getQuizzesByCourseId(courseId: string) {\n    const { data, error } = await supabase\n      .from('quizzes')\n      .select(`\n        *,\n        questions (\n          id,\n          question,\n          options,\n          correct_answer,\n          points,\n          order_index\n        )\n      `)\n      .eq('course_id', courseId)\n      .eq('is_active', true)\n      .order('created_at');\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getAllQuizzes() {\n    const { data, error } = await supabase\n      .from('quizzes')\n      .select(`\n        *,\n        courses (\n          id,\n          title\n        ),\n        questions (\n          id,\n          question,\n          options,\n          correct_answer,\n          points,\n          order_index\n        )\n      `)\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateQuiz(id: string, updates: Partial<{\n    title: string;\n    description: string;\n    passingScore: number;\n    timeLimit: number;\n    isActive: boolean;\n  }>) {\n    const { data, error } = await supabase\n      .from('quizzes')\n      .update({\n        title: updates.title,\n        description: updates.description,\n        passing_score: updates.passingScore,\n        time_limit: updates.timeLimit,\n        is_active: updates.isActive,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteQuiz(id: string) {\n    const { error } = await supabase\n      .from('quizzes')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Question Services\n  async createQuestion(questionData: {\n    quizId: string;\n    question: string;\n    options: string[];\n    correctAnswer: number;\n    points?: number;\n    orderIndex?: number;\n  }) {\n    const { data, error } = await supabase\n      .from('questions')\n      .insert([{\n        quiz_id: questionData.quizId,\n        question: questionData.question,\n        options: questionData.options,\n        correct_answer: questionData.correctAnswer,\n        points: questionData.points || 1,\n        order_index: questionData.orderIndex || 0\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getQuestionsByQuizId(quizId: string) {\n    const { data, error } = await supabase\n      .from('questions')\n      .select('*')\n      .eq('quiz_id', quizId)\n      .order('order_index');\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateQuestion(id: string, updates: Partial<{\n    question: string;\n    options: string[];\n    correctAnswer: number;\n    points: number;\n    orderIndex: number;\n  }>) {\n    const { data, error } = await supabase\n      .from('questions')\n      .update({\n        question: updates.question,\n        options: updates.options,\n        correct_answer: updates.correctAnswer,\n        points: updates.points,\n        order_index: updates.orderIndex,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteQuestion(id: string) {\n    const { error } = await supabase\n      .from('questions')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Certificate Services\n  async createCertificate(certificateData: {\n    studentId: string;\n    courseId: string;\n    certificateUrl?: string;\n    grade?: number;\n  }) {\n    const { data, error } = await supabase\n      .from('certificates')\n      .insert([{\n        student_id: certificateData.studentId,\n        course_id: certificateData.courseId,\n        certificate_url: certificateData.certificateUrl,\n        grade: certificateData.grade\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getCertificatesByStudentId(studentId: string) {\n    const { data, error } = await supabase\n      .from('certificates')\n      .select(`\n        *,\n        courses (\n          id,\n          title,\n          thumbnail_url\n        )\n      `)\n      .eq('student_id', studentId)\n      .order('issued_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getAllCertificates() {\n    const { data, error } = await supabase\n      .from('certificates')\n      .select(`\n        *,\n        students (\n          id,\n          name,\n          access_code\n        ),\n        courses (\n          id,\n          title,\n          thumbnail_url\n        )\n      `)\n      .order('issued_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n\n  async deleteCertificate(id: string) {\n    const { error } = await supabase\n      .from('certificates')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // Student Enrollment Services\n  async enrollStudent(studentId: string, courseId: string) {\n    const { data, error } = await supabase\n      .from('student_enrollments')\n      .insert([{\n        student_id: studentId,\n        course_id: courseId\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async updateStudentProgress(studentId: string, courseId: string, progress: number) {\n    const { data, error } = await supabase\n      .from('student_enrollments')\n      .update({\n        progress: progress,\n        completed_at: progress >= 100 ? new Date().toISOString() : null\n      })\n      .eq('student_id', studentId)\n      .eq('course_id', courseId)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  async getStudentEnrollments(studentId: string) {\n    const { data, error } = await supabase\n      .from('student_enrollments')\n      .select(`\n        *,\n        courses (\n          id,\n          title,\n          description,\n          thumbnail_url,\n          videos (\n            id,\n            title,\n            duration\n          )\n        )\n      `)\n      .eq('student_id', studentId)\n      .order('enrolled_at', { ascending: false });\n\n    if (error) throw error;\n    return data;\n  }\n}\n\nexport const supabaseService = new SupabaseService();\n"], "mappings": "AAAA,OAASA,QAAQ,KAAQ,oBAAoB,CAG7C,MAAO,MAAM,CAAAC,eAAgB,CAC3B;AACA,KAAM,CAAAC,WAAWA,CAACC,SAMjB,CAAE,CACD,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC,CAAC,CACPC,KAAK,CAAEL,SAAS,CAACK,KAAK,CACtBC,IAAI,CAAEN,SAAS,CAACM,IAAI,CACpBC,aAAa,CAAEP,SAAS,CAACQ,YAAY,CACrCC,WAAW,CAAET,SAAS,CAACS,WAAW,EAAI,CAAC,KAAK,CAAC,CAC7CC,UAAU,CAAEV,SAAS,CAACW,SACxB,CAAC,CAAC,CAAC,CACFC,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAa,eAAeA,CAACT,KAAa,CAAE,CACnC,KAAM,CAAEJ,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,QAAQ,CAAC,CACdS,MAAM,CAAC,GAAG,CAAC,CACXG,EAAE,CAAC,OAAO,CAAEV,KAAK,CAAC,CAClBU,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBF,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,CACTc,OAAO,CAACd,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,KAAI,CACb,CACA,MAAO,CAAAD,IAAI,CACb,CAEA;AACA,KAAM,CAAAgB,aAAaA,CAACC,WAKnB,CAAE,CACD,KAAM,CAAEjB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,CAAC,CACPe,WAAW,CAAED,WAAW,CAACE,UAAU,CACnCd,IAAI,CAAEY,WAAW,CAACZ,IAAI,CACtBD,KAAK,CAAEa,WAAW,CAACb,KAAK,CACxBK,UAAU,CAAEQ,WAAW,CAACP,SAC1B,CAAC,CAAC,CAAC,CACFC,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAoB,sBAAsBA,CAACD,UAAkB,CAAE,CAC/C,GAAI,CACF,KAAM,CAAEnB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDG,EAAE,CAAC,aAAa,CAAEK,UAAU,CAAC,CAC7BL,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBF,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,CACTc,OAAO,CAACM,IAAI,CAAC,4CAA4C,CAAEpB,KAAK,CAAC,CACjE,MAAO,KAAI,CAAE;AACf,CACA,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOC,KAAK,CAAE,CACdc,OAAO,CAACM,IAAI,CAAC,wDAAwD,CAAEpB,KAAK,CAAC,CAC7E,MAAO,KAAI,CAAE;AACf,CACF,CAEA,KAAM,CAAAqB,cAAcA,CAAA,CAAG,CACrB,KAAM,CAAEtB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBS,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE5C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAyB,aAAaA,CAACC,EAAU,CAAEC,OAK9B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,UAAU,CAAC,CAChB0B,MAAM,CAAC,CACNvB,IAAI,CAAEsB,OAAO,CAACtB,IAAI,CAClBD,KAAK,CAAEuB,OAAO,CAACvB,KAAK,CACpBK,UAAU,CAAEkB,OAAO,CAACjB,SAAS,CAC7BmB,SAAS,CAAEF,OAAO,CAACG,QAAQ,CAC3BC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAkC,aAAaA,CAACR,EAAU,CAAE,CAC9B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,UAAU,CAAC,CAChBiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAmC,cAAcA,CAACC,YAGpB,CAAE,CACD,KAAM,CAAErC,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC,CAAC,CACPE,IAAI,CAAEgC,YAAY,CAAChC,IAAI,CACvBiC,WAAW,CAAED,YAAY,CAACC,WAC5B,CAAC,CAAC,CAAC,CACF3B,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAuC,gBAAgBA,CAAA,CAAG,CACvB,KAAM,CAAEvC,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClBS,MAAM,CAAC,GAAG,CAAC,CACXG,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBS,KAAK,CAAC,MAAM,CAAC,CAEhB,GAAItB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAwC,cAAcA,CAACd,EAAU,CAAEC,OAI/B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,YAAY,CAAC,CAClB0B,MAAM,CAAC,CACNvB,IAAI,CAAEsB,OAAO,CAACtB,IAAI,CAClBiC,WAAW,CAAEX,OAAO,CAACW,WAAW,CAChCT,SAAS,CAAEF,OAAO,CAACG,QAAQ,CAC3BC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAyC,cAAcA,CAACf,EAAU,CAAE,CAC/B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,YAAY,CAAC,CAClBiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAyC,YAAYA,CAACC,UASlB,CAAE,CACD,KAAM,CAAE3C,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,CAAC,CACPyC,KAAK,CAAED,UAAU,CAACC,KAAK,CACvBN,WAAW,CAAEK,UAAU,CAACL,WAAW,CACnCO,WAAW,CAAEF,UAAU,CAACG,UAAU,CAClCC,aAAa,CAAEJ,UAAU,CAACK,YAAY,CACtCC,aAAa,CAAEN,UAAU,CAACO,YAAY,CACtCC,KAAK,CAAER,UAAU,CAACQ,KAAK,EAAI,CAAC,CAC5BC,cAAc,CAAET,UAAU,CAACU,aAAa,EAAI,CAAC,CAC7CC,KAAK,CAAEX,UAAU,CAACW,KAAK,EAAI,UAC7B,CAAC,CAAC,CAAC,CACF3C,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAuD,aAAaA,CAAA,CAAG,CACpB,KAAM,CAAEvD,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBS,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE5C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAwD,aAAaA,CAAC9B,EAAU,CAAE,CAC9B,KAAM,CAAE1B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZZ,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBF,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAyD,YAAYA,CAAC/B,EAAU,CAAEC,OAS7B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACf0B,MAAM,CAAC,CACNgB,KAAK,CAAEjB,OAAO,CAACiB,KAAK,CACpBN,WAAW,CAAEX,OAAO,CAACW,WAAW,CAChCO,WAAW,CAAElB,OAAO,CAACmB,UAAU,CAC/BG,aAAa,CAAEtB,OAAO,CAACuB,YAAY,CACnCC,KAAK,CAAExB,OAAO,CAACwB,KAAK,CACpBC,cAAc,CAAEzB,OAAO,CAAC0B,aAAa,CACrCC,KAAK,CAAE3B,OAAO,CAAC2B,KAAK,CACpBzB,SAAS,CAAEF,OAAO,CAACG,QAAQ,CAC3BC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA0D,YAAYA,CAAChC,EAAU,CAAE,CAC7B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,SAAS,CAAC,CACfiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAA0D,WAAWA,CAACC,SAOjB,CAAE,CACD,KAAM,CAAE5D,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC,CAAC,CACP0D,SAAS,CAAED,SAAS,CAACE,QAAQ,CAC7BlB,KAAK,CAAEgB,SAAS,CAAChB,KAAK,CACtBN,WAAW,CAAEsB,SAAS,CAACtB,WAAW,CAClCyB,SAAS,CAAEH,SAAS,CAACI,QAAQ,CAC7BC,QAAQ,CAAEL,SAAS,CAACK,QAAQ,EAAI,CAAC,CACjCC,WAAW,CAAEN,SAAS,CAACO,UAAU,EAAI,CACvC,CAAC,CAAC,CAAC,CACFxD,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAoE,mBAAmBA,CAACN,QAAgB,CAAE,CAC1C,KAAM,CAAE9D,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,QAAQ,CAAC,CACdS,MAAM,CAAC,GAAG,CAAC,CACXG,EAAE,CAAC,WAAW,CAAEgD,QAAQ,CAAC,CACzBvC,KAAK,CAAC,aAAa,CAAC,CAEvB,GAAItB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAqE,WAAWA,CAAC3C,EAAU,CAAEC,OAM5B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,QAAQ,CAAC,CACd0B,MAAM,CAAC,CACNgB,KAAK,CAAEjB,OAAO,CAACiB,KAAK,CACpBN,WAAW,CAAEX,OAAO,CAACW,WAAW,CAChCyB,SAAS,CAAEpC,OAAO,CAACqC,QAAQ,CAC3BC,QAAQ,CAAEtC,OAAO,CAACsC,QAAQ,CAC1BC,WAAW,CAAEvC,OAAO,CAACwC,UAAU,CAC/BpC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAsE,WAAWA,CAAC5C,EAAU,CAAE,CAC5B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,QAAQ,CAAC,CACdiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAsE,UAAUA,CAACC,QAMhB,CAAE,CACD,KAAM,CAAExE,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,CAAC,CACP0D,SAAS,CAAEW,QAAQ,CAACV,QAAQ,CAC5BlB,KAAK,CAAE4B,QAAQ,CAAC5B,KAAK,CACrBN,WAAW,CAAEkC,QAAQ,CAAClC,WAAW,CACjCmC,aAAa,CAAED,QAAQ,CAACE,YAAY,EAAI,EAAE,CAC1CC,UAAU,CAAEH,QAAQ,CAACI,SAAS,EAAI,EACpC,CAAC,CAAC,CAAC,CACFjE,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA6E,oBAAoBA,CAACf,QAAgB,CAAE,CAC3C,KAAM,CAAE9D,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,WAAW,CAAEgD,QAAQ,CAAC,CACzBhD,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBS,KAAK,CAAC,YAAY,CAAC,CAEtB,GAAItB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA8E,aAAaA,CAAA,CAAG,CACpB,KAAM,CAAE9E,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBS,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE5C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA+E,UAAUA,CAACrD,EAAU,CAAEC,OAM3B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACf0B,MAAM,CAAC,CACNgB,KAAK,CAAEjB,OAAO,CAACiB,KAAK,CACpBN,WAAW,CAAEX,OAAO,CAACW,WAAW,CAChCmC,aAAa,CAAE9C,OAAO,CAAC+C,YAAY,CACnCC,UAAU,CAAEhD,OAAO,CAACiD,SAAS,CAC7B/C,SAAS,CAAEF,OAAO,CAACG,QAAQ,CAC3BC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAgF,UAAUA,CAACtD,EAAU,CAAE,CAC3B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,SAAS,CAAC,CACfiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAgF,cAAcA,CAACC,YAOpB,CAAE,CACD,KAAM,CAAElF,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,CAAC,CACPgF,OAAO,CAAED,YAAY,CAACE,MAAM,CAC5BC,QAAQ,CAAEH,YAAY,CAACG,QAAQ,CAC/BC,OAAO,CAAEJ,YAAY,CAACI,OAAO,CAC7BC,cAAc,CAAEL,YAAY,CAACM,aAAa,CAC1CC,MAAM,CAAEP,YAAY,CAACO,MAAM,EAAI,CAAC,CAChCvB,WAAW,CAAEgB,YAAY,CAACf,UAAU,EAAI,CAC1C,CAAC,CAAC,CAAC,CACFxD,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA0F,oBAAoBA,CAACN,MAAc,CAAE,CACzC,KAAM,CAAEpF,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,WAAW,CAAC,CACjBS,MAAM,CAAC,GAAG,CAAC,CACXG,EAAE,CAAC,SAAS,CAAEsE,MAAM,CAAC,CACrB7D,KAAK,CAAC,aAAa,CAAC,CAEvB,GAAItB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA2F,cAAcA,CAACjE,EAAU,CAAEC,OAM/B,CAAE,CACF,KAAM,CAAE3B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,WAAW,CAAC,CACjB0B,MAAM,CAAC,CACNyD,QAAQ,CAAE1D,OAAO,CAAC0D,QAAQ,CAC1BC,OAAO,CAAE3D,OAAO,CAAC2D,OAAO,CACxBC,cAAc,CAAE5D,OAAO,CAAC6D,aAAa,CACrCC,MAAM,CAAE9D,OAAO,CAAC8D,MAAM,CACtBvB,WAAW,CAAEvC,OAAO,CAACwC,UAAU,CAC/BpC,UAAU,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CAAC,CACDnB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CACZf,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA4F,cAAcA,CAAClE,EAAU,CAAE,CAC/B,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,WAAW,CAAC,CACjBiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAA4F,iBAAiBA,CAACC,eAKvB,CAAE,CACD,KAAM,CAAE9F,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC,CAAC,CACP4F,UAAU,CAAED,eAAe,CAACE,SAAS,CACrCnC,SAAS,CAAEiC,eAAe,CAAChC,QAAQ,CACnCmC,eAAe,CAAEH,eAAe,CAACI,cAAc,CAC/CC,KAAK,CAAEL,eAAe,CAACK,KACzB,CAAC,CAAC,CAAC,CACFxF,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAoG,0BAA0BA,CAACJ,SAAiB,CAAE,CAClD,KAAM,CAAEhG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,cAAc,CAAC,CACpBS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,YAAY,CAAEkF,SAAS,CAAC,CAC3BzE,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE3C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAqG,kBAAkBA,CAAA,CAAG,CACzB,KAAM,CAAErG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,cAAc,CAAC,CACpBS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDY,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE3C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAsG,iBAAiBA,CAAC5E,EAAU,CAAE,CAClC,KAAM,CAAEzB,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAC7BM,IAAI,CAAC,cAAc,CAAC,CACpBiC,MAAM,CAAC,CAAC,CACRrB,EAAE,CAAC,IAAI,CAAEY,EAAE,CAAC,CAEf,GAAIzB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAsG,aAAaA,CAACP,SAAiB,CAAElC,QAAgB,CAAE,CACvD,KAAM,CAAE9D,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,CAAC,CACP4F,UAAU,CAAEC,SAAS,CACrBnC,SAAS,CAAEC,QACb,CAAC,CAAC,CAAC,CACFnD,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAwG,qBAAqBA,CAACR,SAAiB,CAAElC,QAAgB,CAAE2C,QAAgB,CAAE,CACjF,KAAM,CAAEzG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,qBAAqB,CAAC,CAC3B0B,MAAM,CAAC,CACN6E,QAAQ,CAAEA,QAAQ,CAClBC,YAAY,CAAED,QAAQ,EAAI,GAAG,CAAG,GAAI,CAAAzE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,IAC7D,CAAC,CAAC,CACDnB,EAAE,CAAC,YAAY,CAAEkF,SAAS,CAAC,CAC3BlF,EAAE,CAAC,WAAW,CAAEgD,QAAQ,CAAC,CACzBnD,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC,CAEX,GAAIX,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAA2G,qBAAqBA,CAACX,SAAiB,CAAE,CAC7C,KAAM,CAAEhG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACnCM,IAAI,CAAC,qBAAqB,CAAC,CAC3BS,MAAM,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC,CACDG,EAAE,CAAC,YAAY,CAAEkF,SAAS,CAAC,CAC3BzE,KAAK,CAAC,aAAa,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE7C,GAAIvB,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAD,IAAI,CACb,CACF,CAEA,MAAO,MAAM,CAAA4G,eAAe,CAAG,GAAI,CAAA/G,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}