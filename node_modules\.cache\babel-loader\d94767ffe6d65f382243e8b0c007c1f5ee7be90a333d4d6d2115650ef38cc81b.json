{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{toast}from'react-hot-toast';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,UserIcon,AcademicCapIcon,CheckBadgeIcon}from'@heroicons/react/24/outline';import{dataService}from'../../services/dataService';import{supabaseService}from'../../services/supabaseService';// Types\nimport AddStudentModal from'./modals/AddStudentModal';import EditStudentModal from'./modals/EditStudentModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentsManagement=_ref=>{let{onBack}=_ref;const[students,setStudents]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('all');const[loading,setLoading]=useState(true);const[showAddModal,setShowAddModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[selectedStudent,setSelectedStudent]=useState(null);useEffect(()=>{loadStudents();},[]);const loadStudents=async()=>{try{setLoading(true);// Try to get from Supabase first\nconst supabaseStudents=await supabaseService.getAllStudents();if(supabaseStudents&&supabaseStudents.length>0){// Transform Supabase data to match our Student type\nconst transformedStudents=supabaseStudents.map(student=>{var _student$student_enro,_student$student_enro2,_student$certificates;return{id:student.id,email:student.email||'',name:student.name||'',role:'student',avatar:student.avatar_url||'',accessCode:student.access_code,enrolledCourses:((_student$student_enro=student.student_enrollments)===null||_student$student_enro===void 0?void 0:_student$student_enro.map(enrollment=>enrollment.course_id))||[],completedCourses:((_student$student_enro2=student.student_enrollments)===null||_student$student_enro2===void 0?void 0:_student$student_enro2.filter(enrollment=>enrollment.completed_at).map(enrollment=>enrollment.course_id))||[],certificates:((_student$certificates=student.certificates)===null||_student$certificates===void 0?void 0:_student$certificates.map(cert=>cert.id))||[],createdAt:new Date(student.created_at)};});setStudents(transformedStudents);}else{// Fallback to mock data\nconst studentsData=await dataService.getStudents();setStudents(studentsData);}}catch(error){console.error('Error loading students:',error);toast.error('حدث خطأ في تحميل الطلاب');// Fallback to mock data\ntry{const studentsData=await dataService.getStudents();setStudents(studentsData);}catch(fallbackError){console.error('Error loading fallback data:',fallbackError);}}finally{setLoading(false);}};const filteredStudents=students.filter(student=>{var _student$name;const matchesSearch=((_student$name=student.name)===null||_student$name===void 0?void 0:_student$name.toLowerCase().includes(searchTerm.toLowerCase()))||student.email.toLowerCase().includes(searchTerm.toLowerCase())||student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());let matchesStatus=true;if(statusFilter==='active'){matchesStatus=student.enrolledCourses.length>0;}else if(statusFilter==='completed'){matchesStatus=student.completedCourses.length>0;}return matchesSearch&&matchesStatus;});const handleAddStudent=()=>{setShowAddModal(true);};const handleEditStudent=studentId=>{const student=students.find(s=>s.id===studentId);if(student){setSelectedStudent(student);setShowEditModal(true);}};const handleDeleteStudent=async studentId=>{if(window.confirm('هل أنت متأكد من حذف هذا الطالب؟')){try{await supabaseService.deleteStudent(studentId);toast.success('تم حذف الطالب بنجاح');loadStudents();}catch(error){console.error('Error deleting student:',error);toast.error('حدث خطأ في حذف الطالب');}}};const handleStudentAdded=()=>{setShowAddModal(false);loadStudents();toast.success('تم إضافة الطالب بنجاح');};const handleStudentUpdated=()=>{setShowEditModal(false);setSelectedStudent(null);loadStudents();toast.success('تم تحديث الطالب بنجاح');};const handleViewStudent=studentId=>{// TODO: Implement view student functionality\nconsole.log('View student:',studentId);};const getStudentStatus=student=>{if(student.completedCourses.length>0){return{status:'مكتمل',color:'green'};}else if(student.enrolledCourses.length>0){return{status:'نشط',color:'blue'};}else{return{status:'غير نشط',color:'gray'};}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddStudent,className:\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0637\\u0627\\u0644\\u0628 \\u062C\\u062F\\u064A\\u062F\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0627\\u0644\\u0627\\u0633\\u0645\\u060C \\u0627\\u0644\\u0625\\u064A\\u0645\\u064A\\u0644\\u060C \\u0623\\u0648 \\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644...\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0646\\u0634\\u0637\\u0648\\u0646\"}),/*#__PURE__*/_jsx(\"option\",{value:\"completed\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0648\\u0646\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredStudents.map((student,index)=>{const studentStatus=getStudentStatus(student);return/*#__PURE__*/_jsxs(motion.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.05},className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-blue-600\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:student.name||'غير محدد'}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:student.email})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\",children:student.accessCode})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.enrolledCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(CheckBadgeIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.completedCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.certificates.length})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${studentStatus.color==='green'?'bg-green-100 text-green-800':studentStatus.color==='blue'?'bg-blue-100 text-blue-800':'bg-gray-100 text-gray-800'}`,children:studentStatus.status})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(student.createdAt).toLocaleDateString('ar-SA')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewStudent(student.id),className:\"text-blue-600 hover:text-blue-900\",title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditStudent(student.id),className:\"text-green-600 hover:text-green-900\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteStudent(student.id),className:\"text-red-600 hover:text-red-900\",title:\"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]})})]},student.id);})})]})})}),filteredStudents.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u064A\\u0648\\u062C\\u062F \\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0637\\u0644\\u0627\\u0628 \\u064A\\u0637\\u0627\\u0628\\u0642\\u0648\\u0646 \\u0627\\u0644\\u0628\\u062D\\u062B\"})]}),loading&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628...\"})]}),showAddModal&&/*#__PURE__*/_jsx(AddStudentModal,{onClose:()=>setShowAddModal(false),onSuccess:handleStudentAdded}),showEditModal&&selectedStudent&&/*#__PURE__*/_jsx(EditStudentModal,{student:selectedStudent,onClose:()=>{setShowEditModal(false);setSelectedStudent(null);},onSuccess:handleStudentUpdated})]});};export default StudentsManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "toast", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "UserIcon", "AcademicCapIcon", "CheckBadgeIcon", "dataService", "supabaseService", "AddStudentModal", "EditStudentModal", "jsx", "_jsx", "jsxs", "_jsxs", "StudentsManagement", "_ref", "onBack", "students", "setStudents", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "loading", "setLoading", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "selectedStudent", "setSelectedStudent", "loadStudents", "supabaseStudents", "getAllStudents", "length", "transformedStudents", "map", "student", "_student$student_enro", "_student$student_enro2", "_student$certificates", "id", "email", "name", "role", "avatar", "avatar_url", "accessCode", "access_code", "enrolledCourses", "student_enrollments", "enrollment", "course_id", "completedCourses", "filter", "completed_at", "certificates", "cert", "createdAt", "Date", "created_at", "studentsData", "getStudents", "error", "console", "fallback<PERSON><PERSON>r", "filteredStudents", "_student$name", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "handleAddStudent", "handleEditStudent", "studentId", "find", "s", "handleDeleteStudent", "window", "confirm", "deleteStudent", "success", "handleStudentAdded", "handleStudentUpdated", "handleViewStudent", "log", "getStudentStatus", "status", "color", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "target", "placeholder", "index", "studentStatus", "tr", "initial", "opacity", "y", "animate", "transition", "delay", "toLocaleDateString", "title", "onClose", "onSuccess"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/StudentsManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  UserIcon,\n  AcademicCapIcon,\n  CheckBadgeIcon\n} from '@heroicons/react/24/outline';\nimport { dataService } from '../../services/dataService';\nimport { supabaseService } from '../../services/supabaseService';\nimport { authService } from '../../services/authService';\n\n// Types\nimport { Student } from '../../types';\nimport AddStudentModal from './modals/AddStudentModal';\nimport EditStudentModal from './modals/EditStudentModal';\n\ninterface StudentsManagementProps {\n  onBack?: () => void;\n}\n\nconst StudentsManagement: React.FC<StudentsManagementProps> = ({ onBack }) => {\n  const [students, setStudents] = useState<Student[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [loading, setLoading] = useState(true);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);\n\n  useEffect(() => {\n    loadStudents();\n  }, []);\n\n  const loadStudents = async () => {\n    try {\n      setLoading(true);\n      // Try to get from Supabase first\n      const supabaseStudents = await supabaseService.getAllStudents();\n      if (supabaseStudents && supabaseStudents.length > 0) {\n        // Transform Supabase data to match our Student type\n        const transformedStudents = supabaseStudents.map(student => ({\n          id: student.id,\n          email: student.email || '',\n          name: student.name || '',\n          role: 'student' as const,\n          avatar: student.avatar_url || '',\n          accessCode: student.access_code,\n          enrolledCourses: student.student_enrollments?.map((enrollment: any) => enrollment.course_id) || [],\n          completedCourses: student.student_enrollments?.filter((enrollment: any) => enrollment.completed_at).map((enrollment: any) => enrollment.course_id) || [],\n          certificates: student.certificates?.map((cert: any) => cert.id) || [],\n          createdAt: new Date(student.created_at)\n        }));\n        setStudents(transformedStudents);\n      } else {\n        // Fallback to mock data\n        const studentsData = await dataService.getStudents();\n        setStudents(studentsData);\n      }\n    } catch (error) {\n      console.error('Error loading students:', error);\n      toast.error('حدث خطأ في تحميل الطلاب');\n      // Fallback to mock data\n      try {\n        const studentsData = await dataService.getStudents();\n        setStudents(studentsData);\n      } catch (fallbackError) {\n        console.error('Error loading fallback data:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const filteredStudents = students.filter(student => {\n    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    let matchesStatus = true;\n    if (statusFilter === 'active') {\n      matchesStatus = student.enrolledCourses.length > 0;\n    } else if (statusFilter === 'completed') {\n      matchesStatus = student.completedCourses.length > 0;\n    }\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const handleAddStudent = () => {\n    setShowAddModal(true);\n  };\n\n  const handleEditStudent = (studentId: string) => {\n    const student = students.find(s => s.id === studentId);\n    if (student) {\n      setSelectedStudent(student);\n      setShowEditModal(true);\n    }\n  };\n\n  const handleDeleteStudent = async (studentId: string) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {\n      try {\n        await supabaseService.deleteStudent(studentId);\n        toast.success('تم حذف الطالب بنجاح');\n        loadStudents();\n      } catch (error) {\n        console.error('Error deleting student:', error);\n        toast.error('حدث خطأ في حذف الطالب');\n      }\n    }\n  };\n\n  const handleStudentAdded = () => {\n    setShowAddModal(false);\n    loadStudents();\n    toast.success('تم إضافة الطالب بنجاح');\n  };\n\n  const handleStudentUpdated = () => {\n    setShowEditModal(false);\n    setSelectedStudent(null);\n    loadStudents();\n    toast.success('تم تحديث الطالب بنجاح');\n  };\n\n  const handleViewStudent = (studentId: string) => {\n    // TODO: Implement view student functionality\n    console.log('View student:', studentId);\n  };\n\n  const getStudentStatus = (student: Student) => {\n    if (student.completedCourses.length > 0) {\n      return { status: 'مكتمل', color: 'green' };\n    } else if (student.enrolledCourses.length > 0) {\n      return { status: 'نشط', color: 'blue' };\n    } else {\n      return { status: 'غير نشط', color: 'gray' };\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الطلاب</h1>\n            <p className=\"text-gray-600\">إدارة وتتبع جميع الطلاب المسجلين</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddStudent}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة طالب جديد</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البحث في الطلاب\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"ابحث بالاسم، الإيميل، أو رمز الوصول...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              حالة الطالب\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">جميع الطلاب</option>\n              <option value=\"active\">الطلاب النشطون</option>\n              <option value=\"completed\">الطلاب المكتملون</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Students Table */}\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  رمز الوصول\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المسجلة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المكتملة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الشهادات\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الحالة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ التسجيل\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredStudents.map((student, index) => {\n                const studentStatus = getStudentStatus(student);\n                return (\n                  <motion.tr\n                    key={student.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                    className=\"hover:bg-gray-50\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <UserIcon className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {student.name || 'غير محدد'}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">{student.email}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\">\n                        {student.accessCode}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <AcademicCapIcon className=\"w-4 h-4 text-blue-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.enrolledCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <CheckBadgeIcon className=\"w-4 h-4 text-green-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.completedCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm text-gray-900\">{student.certificates.length}</span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        studentStatus.color === 'green' \n                          ? 'bg-green-100 text-green-800'\n                          : studentStatus.color === 'blue'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {studentStatus.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {new Date(student.createdAt).toLocaleDateString('ar-SA')}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <button\n                          onClick={() => handleViewStudent(student.id)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"عرض الطالب\"\n                        >\n                          <EyeIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleEditStudent(student.id)}\n                          className=\"text-green-600 hover:text-green-900\"\n                          title=\"تعديل الطالب\"\n                        >\n                          <PencilIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteStudent(student.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"حذف الطالب\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </motion.tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {filteredStudents.length === 0 && (\n        <div className=\"text-center py-12\">\n          <UserIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا يوجد طلاب</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي طلاب يطابقون البحث</p>\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري تحميل الطلاب...</p>\n        </div>\n      )}\n\n      {/* Add Student Modal */}\n      {showAddModal && (\n        <AddStudentModal\n          onClose={() => setShowAddModal(false)}\n          onSuccess={handleStudentAdded}\n        />\n      )}\n\n      {/* Edit Student Modal */}\n      {showEditModal && selectedStudent && (\n        <EditStudentModal\n          student={selectedStudent}\n          onClose={() => {\n            setShowEditModal(false);\n            setSelectedStudent(null);\n          }}\n          onSuccess={handleStudentUpdated}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default StudentsManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,QAAQ,CACRC,eAAe,CACfC,cAAc,KACT,6BAA6B,CACpC,OAASC,WAAW,KAAQ,4BAA4B,CACxD,OAASC,eAAe,KAAQ,gCAAgC,CAGhE;AAEA,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMzD,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACvE,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAiB,IAAI,CAAC,CAE5EC,SAAS,CAAC,IAAM,CACdmC,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,CAAAQ,gBAAgB,CAAG,KAAM,CAAAzB,eAAe,CAAC0B,cAAc,CAAC,CAAC,CAC/D,GAAID,gBAAgB,EAAIA,gBAAgB,CAACE,MAAM,CAAG,CAAC,CAAE,CACnD;AACA,KAAM,CAAAC,mBAAmB,CAAGH,gBAAgB,CAACI,GAAG,CAACC,OAAO,OAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,OAAK,CAC3DC,EAAE,CAAEJ,OAAO,CAACI,EAAE,CACdC,KAAK,CAAEL,OAAO,CAACK,KAAK,EAAI,EAAE,CAC1BC,IAAI,CAAEN,OAAO,CAACM,IAAI,EAAI,EAAE,CACxBC,IAAI,CAAE,SAAkB,CACxBC,MAAM,CAAER,OAAO,CAACS,UAAU,EAAI,EAAE,CAChCC,UAAU,CAAEV,OAAO,CAACW,WAAW,CAC/BC,eAAe,CAAE,EAAAX,qBAAA,CAAAD,OAAO,CAACa,mBAAmB,UAAAZ,qBAAA,iBAA3BA,qBAAA,CAA6BF,GAAG,CAAEe,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CAClGC,gBAAgB,CAAE,EAAAd,sBAAA,CAAAF,OAAO,CAACa,mBAAmB,UAAAX,sBAAA,iBAA3BA,sBAAA,CAA6Be,MAAM,CAAEH,UAAe,EAAKA,UAAU,CAACI,YAAY,CAAC,CAACnB,GAAG,CAAEe,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CACxJI,YAAY,CAAE,EAAAhB,qBAAA,CAAAH,OAAO,CAACmB,YAAY,UAAAhB,qBAAA,iBAApBA,qBAAA,CAAsBJ,GAAG,CAAEqB,IAAS,EAAKA,IAAI,CAAChB,EAAE,CAAC,GAAI,EAAE,CACrEiB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACtB,OAAO,CAACuB,UAAU,CACxC,CAAC,EAAC,CAAC,CACH1C,WAAW,CAACiB,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACL;AACA,KAAM,CAAA0B,YAAY,CAAG,KAAM,CAAAvD,WAAW,CAACwD,WAAW,CAAC,CAAC,CACpD5C,WAAW,CAAC2C,YAAY,CAAC,CAC3B,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CjE,KAAK,CAACiE,KAAK,CAAC,yBAAyB,CAAC,CACtC;AACA,GAAI,CACF,KAAM,CAAAF,YAAY,CAAG,KAAM,CAAAvD,WAAW,CAACwD,WAAW,CAAC,CAAC,CACpD5C,WAAW,CAAC2C,YAAY,CAAC,CAC3B,CAAE,MAAOI,aAAa,CAAE,CACtBD,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEE,aAAa,CAAC,CAC9D,CACF,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAID,KAAM,CAAA0C,gBAAgB,CAAGjD,QAAQ,CAACqC,MAAM,CAACjB,OAAO,EAAI,KAAA8B,aAAA,CAClD,KAAM,CAAAC,aAAa,CAAG,EAAAD,aAAA,CAAA9B,OAAO,CAACM,IAAI,UAAAwB,aAAA,iBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,GAC/DhC,OAAO,CAACK,KAAK,CAAC2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,EAC9DhC,OAAO,CAACU,UAAU,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,CAExF,GAAI,CAAAE,aAAa,CAAG,IAAI,CACxB,GAAIlD,YAAY,GAAK,QAAQ,CAAE,CAC7BkD,aAAa,CAAGlC,OAAO,CAACY,eAAe,CAACf,MAAM,CAAG,CAAC,CACpD,CAAC,IAAM,IAAIb,YAAY,GAAK,WAAW,CAAE,CACvCkD,aAAa,CAAGlC,OAAO,CAACgB,gBAAgB,CAACnB,MAAM,CAAG,CAAC,CACrD,CAEA,MAAO,CAAAkC,aAAa,EAAIG,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B9C,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAA+C,iBAAiB,CAAIC,SAAiB,EAAK,CAC/C,KAAM,CAAArC,OAAO,CAAGpB,QAAQ,CAAC0D,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACnC,EAAE,GAAKiC,SAAS,CAAC,CACtD,GAAIrC,OAAO,CAAE,CACXP,kBAAkB,CAACO,OAAO,CAAC,CAC3BT,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAiD,mBAAmB,CAAG,KAAO,CAAAH,SAAiB,EAAK,CACvD,GAAII,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAE,CACrD,GAAI,CACF,KAAM,CAAAxE,eAAe,CAACyE,aAAa,CAACN,SAAS,CAAC,CAC9C5E,KAAK,CAACmF,OAAO,CAAC,qBAAqB,CAAC,CACpClD,YAAY,CAAC,CAAC,CAChB,CAAE,MAAOgC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CjE,KAAK,CAACiE,KAAK,CAAC,uBAAuB,CAAC,CACtC,CACF,CACF,CAAC,CAED,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BxD,eAAe,CAAC,KAAK,CAAC,CACtBK,YAAY,CAAC,CAAC,CACdjC,KAAK,CAACmF,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAC,CAED,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CACjCvD,gBAAgB,CAAC,KAAK,CAAC,CACvBE,kBAAkB,CAAC,IAAI,CAAC,CACxBC,YAAY,CAAC,CAAC,CACdjC,KAAK,CAACmF,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAIV,SAAiB,EAAK,CAC/C;AACAV,OAAO,CAACqB,GAAG,CAAC,eAAe,CAAEX,SAAS,CAAC,CACzC,CAAC,CAED,KAAM,CAAAY,gBAAgB,CAAIjD,OAAgB,EAAK,CAC7C,GAAIA,OAAO,CAACgB,gBAAgB,CAACnB,MAAM,CAAG,CAAC,CAAE,CACvC,MAAO,CAAEqD,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC5C,CAAC,IAAM,IAAInD,OAAO,CAACY,eAAe,CAACf,MAAM,CAAG,CAAC,CAAE,CAC7C,MAAO,CAAEqD,MAAM,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAC,CACzC,CAAC,IAAM,CACL,MAAO,CAAED,MAAM,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC7C,CACF,CAAC,CAED,mBACE3E,KAAA,QAAK4E,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB7E,KAAA,QAAK4E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7E,KAAA,QAAK4E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzD1E,MAAM,eACLL,IAAA,WACEgF,OAAO,CAAE3E,MAAO,CAChByE,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnE/E,IAAA,QAAK8E,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5E/E,IAAA,SAAMoF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDrF,KAAA,QAAA6E,QAAA,eACE/E,IAAA,OAAI8E,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,cAClE/E,IAAA,MAAG8E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8KAAgC,CAAG,CAAC,EAC9D,CAAC,EACH,CAAC,cACN7E,KAAA,WACE8E,OAAO,CAAEnB,gBAAiB,CAC1BiB,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvI/E,IAAA,CAACZ,QAAQ,EAAC0F,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC9E,IAAA,SAAA+E,QAAA,CAAM,kFAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,cAGN/E,IAAA,QAAK8E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChD7E,KAAA,QAAK4E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD7E,KAAA,QAAA6E,QAAA,eACE/E,IAAA,UAAO8E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kFAEhE,CAAO,CAAC,cACR/E,IAAA,UACEwF,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEjF,UAAW,CAClBkF,QAAQ,CAAGC,CAAC,EAAKlF,aAAa,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,8LAAwC,CACpDf,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cACN5E,KAAA,QAAA6E,QAAA,eACE/E,IAAA,UAAO8E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACR7E,KAAA,WACEuF,KAAK,CAAE/E,YAAa,CACpBgF,QAAQ,CAAGC,CAAC,EAAKhF,eAAe,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDX,SAAS,CAAC,wGAAwG,CAAAC,QAAA,eAElH/E,IAAA,WAAQyF,KAAK,CAAC,KAAK,CAAAV,QAAA,CAAC,+DAAW,CAAQ,CAAC,cACxC/E,IAAA,WAAQyF,KAAK,CAAC,QAAQ,CAAAV,QAAA,CAAC,iFAAc,CAAQ,CAAC,cAC9C/E,IAAA,WAAQyF,KAAK,CAAC,WAAW,CAAAV,QAAA,CAAC,6FAAgB,CAAQ,CAAC,EAC7C,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN/E,IAAA,QAAK8E,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5D/E,IAAA,QAAK8E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B7E,KAAA,UAAO4E,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD/E,IAAA,UAAO8E,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B7E,KAAA,OAAA6E,QAAA,eACE/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,yDAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,6FAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,mGAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,kDAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,2EAEhG,CAAI,CAAC,cACL/E,IAAA,OAAI8E,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,wDAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR/E,IAAA,UAAO8E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDxB,gBAAgB,CAAC9B,GAAG,CAAC,CAACC,OAAO,CAAEoE,KAAK,GAAK,CACxC,KAAM,CAAAC,aAAa,CAAGpB,gBAAgB,CAACjD,OAAO,CAAC,CAC/C,mBACExB,KAAA,CAAChB,MAAM,CAAC8G,EAAE,EAERC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAER,KAAK,CAAG,IAAK,CAAE,CACpChB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5B/E,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7E,KAAA,QAAK4E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D/E,IAAA,QAAK8E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/E,IAAA,QAAK8E,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClF/E,IAAA,CAACR,QAAQ,EAACsF,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,CACH,CAAC,cACN5E,KAAA,QAAA6E,QAAA,eACE/E,IAAA,QAAK8E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CrD,OAAO,CAACM,IAAI,EAAI,UAAU,CACxB,CAAC,cACNhC,IAAA,QAAK8E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAErD,OAAO,CAACK,KAAK,CAAM,CAAC,EACzD,CAAC,EACH,CAAC,CACJ,CAAC,cACL/B,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC/E,IAAA,SAAM8E,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC5ErD,OAAO,CAACU,UAAU,CACf,CAAC,CACL,CAAC,cACLpC,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7E,KAAA,QAAK4E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D/E,IAAA,CAACP,eAAe,EAACqF,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACrD9E,IAAA,SAAM8E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAErD,OAAO,CAACY,eAAe,CAACf,MAAM,CAAO,CAAC,EAC5E,CAAC,CACJ,CAAC,cACLvB,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC7E,KAAA,QAAK4E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D/E,IAAA,CAACN,cAAc,EAACoF,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACrD9E,IAAA,SAAM8E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAErD,OAAO,CAACgB,gBAAgB,CAACnB,MAAM,CAAO,CAAC,EAC7E,CAAC,CACJ,CAAC,cACLvB,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC/E,IAAA,SAAM8E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAErD,OAAO,CAACmB,YAAY,CAACtB,MAAM,CAAO,CAAC,CAC1E,CAAC,cACLvB,IAAA,OAAI8E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC/E,IAAA,SAAM8E,SAAS,CAAE,4DACfiB,aAAa,CAAClB,KAAK,GAAK,OAAO,CAC3B,6BAA6B,CAC7BkB,aAAa,CAAClB,KAAK,GAAK,MAAM,CAC9B,2BAA2B,CAC3B,2BAA2B,EAC9B,CAAAE,QAAA,CACAgB,aAAa,CAACnB,MAAM,CACjB,CAAC,CACL,CAAC,cACL5E,IAAA,OAAI8E,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAA/B,IAAI,CAACtB,OAAO,CAACqB,SAAS,CAAC,CAACwD,kBAAkB,CAAC,OAAO,CAAC,CACtD,CAAC,cACLvG,IAAA,OAAI8E,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7D7E,KAAA,QAAK4E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D/E,IAAA,WACEgF,OAAO,CAAEA,CAAA,GAAMP,iBAAiB,CAAC/C,OAAO,CAACI,EAAE,CAAE,CAC7CgD,SAAS,CAAC,mCAAmC,CAC7C0B,KAAK,CAAC,yDAAY,CAAAzB,QAAA,cAElB/E,IAAA,CAACT,OAAO,EAACuF,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACT9E,IAAA,WACEgF,OAAO,CAAEA,CAAA,GAAMlB,iBAAiB,CAACpC,OAAO,CAACI,EAAE,CAAE,CAC7CgD,SAAS,CAAC,qCAAqC,CAC/C0B,KAAK,CAAC,qEAAc,CAAAzB,QAAA,cAEpB/E,IAAA,CAACX,UAAU,EAACyF,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT9E,IAAA,WACEgF,OAAO,CAAEA,CAAA,GAAMd,mBAAmB,CAACxC,OAAO,CAACI,EAAE,CAAE,CAC/CgD,SAAS,CAAC,iCAAiC,CAC3C0B,KAAK,CAAC,yDAAY,CAAAzB,QAAA,cAElB/E,IAAA,CAACV,SAAS,EAACwF,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GA/EApD,OAAO,CAACI,EAgFJ,CAAC,CAEhB,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACH,CAAC,CAELyB,gBAAgB,CAAChC,MAAM,GAAK,CAAC,eAC5BrB,KAAA,QAAK4E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/E,IAAA,CAACR,QAAQ,EAACsF,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7D9E,IAAA,OAAI8E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,gEAAY,CAAI,CAAC,cACxE/E,IAAA,MAAG8E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yMAAuC,CAAG,CAAC,EACrE,CACN,CAEAnE,OAAO,eACNV,KAAA,QAAK4E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/E,IAAA,QAAK8E,SAAS,CAAC,8FAA8F,CAAM,CAAC,cACpH9E,IAAA,MAAG8E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iGAAoB,CAAG,CAAC,EAClD,CACN,CAGAjE,YAAY,eACXd,IAAA,CAACH,eAAe,EACd4G,OAAO,CAAEA,CAAA,GAAM1F,eAAe,CAAC,KAAK,CAAE,CACtC2F,SAAS,CAAEnC,kBAAmB,CAC/B,CACF,CAGAvD,aAAa,EAAIE,eAAe,eAC/BlB,IAAA,CAACF,gBAAgB,EACf4B,OAAO,CAAER,eAAgB,CACzBuF,OAAO,CAAEA,CAAA,GAAM,CACbxF,gBAAgB,CAAC,KAAK,CAAC,CACvBE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACFuF,SAAS,CAAElC,oBAAqB,CACjC,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAArE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}