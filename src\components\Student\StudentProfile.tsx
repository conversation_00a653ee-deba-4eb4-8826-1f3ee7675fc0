import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  CameraIcon,
  CheckCircleIcon,
  AcademicCapIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

// Types
import { Student } from '../../types';

interface StudentProfileProps {
  user?: Student;
  onBack?: () => void;
}

const StudentProfile: React.FC<StudentProfileProps> = ({ user, onBack }) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  
  // Mock student data
  const [studentData, setStudentData] = useState({
    name: 'أحمد محمد',
    email: '<EMAIL>',
    accessCode: '1234567',
    joinDate: new Date('2024-01-15'),
    avatar: null,
    bio: 'طالب مهتم بتعلم البرمجة وتطوير المواقع',
    phone: '+966501234567',
    city: 'الرياض'
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Mock stats
  const stats = {
    enrolledCourses: 5,
    completedCourses: 3,
    certificates: 2,
    totalWatchTime: 45, // hours
    averageScore: 87
  };

  const handleSaveProfile = () => {
    // TODO: Implement save profile functionality
    console.log('Save profile:', studentData);
    setIsEditing(false);
  };

  const handleChangePassword = () => {
    // TODO: Implement change password functionality
    console.log('Change password');
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // TODO: Implement avatar upload
    console.log('Avatar change:', event.target.files);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        {onBack && (
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الملف الشخصي</h1>
          <p className="text-gray-600">إدارة معلوماتك الشخصية وإعداداتك</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="lg:col-span-1"
        >
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <div className="relative inline-block mb-4">
              <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                {studentData.avatar ? (
                  <img
                    src={studentData.avatar}
                    alt="Profile"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                ) : (
                  <UserIcon className="w-12 h-12 text-blue-600" />
                )}
              </div>
              <label className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors">
                <CameraIcon className="w-4 h-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
            </div>
            
            <h2 className="text-xl font-bold text-gray-900 mb-1">{studentData.name}</h2>
            <p className="text-gray-600 mb-2">{studentData.email}</p>
            <p className="text-sm text-gray-500 mb-4">
              رمز الوصول: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{studentData.accessCode}</span>
            </p>
            <p className="text-sm text-gray-500">
              عضو منذ {studentData.joinDate.toLocaleDateString('ar-SA')}
            </p>
          </div>

          {/* Stats */}
          <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائياتي</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <AcademicCapIcon className="w-5 h-5 text-blue-600" />
                  <span className="text-sm text-gray-600">الكورسات المسجلة</span>
                </div>
                <span className="font-semibold text-gray-900">{stats.enrolledCourses}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <CheckCircleIcon className="w-5 h-5 text-green-600" />
                  <span className="text-sm text-gray-600">الكورسات المكتملة</span>
                </div>
                <span className="font-semibold text-gray-900">{stats.completedCourses}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <TrophyIcon className="w-5 h-5 text-yellow-600" />
                  <span className="text-sm text-gray-600">الشهادات</span>
                </div>
                <span className="font-semibold text-gray-900">{stats.certificates}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">ساعات المشاهدة</span>
                <span className="font-semibold text-gray-900">{stats.totalWatchTime}h</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">متوسط النتائج</span>
                <span className="font-semibold text-green-600">{stats.averageScore}%</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="lg:col-span-2"
        >
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex space-x-4 space-x-reverse border-b border-gray-200">
              <button
                onClick={() => setActiveTab('profile')}
                className={`pb-2 px-1 border-b-2 transition-colors ${
                  activeTab === 'profile'
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                المعلومات الشخصية
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`pb-2 px-1 border-b-2 transition-colors ${
                  activeTab === 'security'
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                الأمان
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">المعلومات الشخصية</h3>
                  <button
                    onClick={() => setIsEditing(!isEditing)}
                    className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    {isEditing ? 'إلغاء' : 'تعديل'}
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل
                    </label>
                    <input
                      type="text"
                      value={studentData.name}
                      onChange={(e) => setStudentData(prev => ({ ...prev, name: e.target.value }))}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      value={studentData.email}
                      onChange={(e) => setStudentData(prev => ({ ...prev, email: e.target.value }))}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      value={studentData.phone}
                      onChange={(e) => setStudentData(prev => ({ ...prev, phone: e.target.value }))}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المدينة
                    </label>
                    <input
                      type="text"
                      value={studentData.city}
                      onChange={(e) => setStudentData(prev => ({ ...prev, city: e.target.value }))}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نبذة شخصية
                  </label>
                  <textarea
                    value={studentData.bio}
                    onChange={(e) => setStudentData(prev => ({ ...prev, bio: e.target.value }))}
                    disabled={!isEditing}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                  />
                </div>

                {isEditing && (
                  <div className="flex justify-end space-x-3 space-x-reverse">
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      إلغاء
                    </button>
                    <button
                      onClick={handleSaveProfile}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      حفظ التغييرات
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">تغيير كلمة المرور</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور الحالية
                    </label>
                    <input
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تأكيد كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <button
                    onClick={handleChangePassword}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    تغيير كلمة المرور
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default StudentProfile;
