{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{jsx as _jsx}from\"react/jsx-runtime\";const ResponsiveGrid=_ref=>{let{children,className='',columns={mobile:1,tablet:2,desktop:3},gap='md',animated=false,staggerChildren=false}=_ref;const gapClasses={sm:'gap-2 sm:gap-3 md:gap-4',md:'gap-4 sm:gap-6 md:gap-8',lg:'gap-6 sm:gap-8 md:gap-12',xl:'gap-8 sm:gap-12 md:gap-16'};const getGridCols=()=>{const{mobile=1,tablet=2,desktop=3}=columns;const mobileClass=`grid-cols-${mobile}`;const tabletClass=tablet?`md:grid-cols-${tablet}`:'';const desktopClass=desktop?`lg:grid-cols-${desktop}`:'';return`${mobileClass} ${tabletClass} ${desktopClass}`.trim();};const gridClasses=`\n    grid\n    ${getGridCols()}\n    ${gapClasses[gap]}\n    ${className}\n  `.trim();if(animated){const containerVariants={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:staggerChildren?0.1:0,delayChildren:0.2}}};const itemVariants={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}};return/*#__PURE__*/_jsx(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",className:gridClasses,children:React.Children.map(children,(child,index)=>/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:child},index))});}return/*#__PURE__*/_jsx(\"div\",{className:gridClasses,children:children});};export default ResponsiveGrid;", "map": {"version": 3, "names": ["React", "motion", "jsx", "_jsx", "ResponsiveGrid", "_ref", "children", "className", "columns", "mobile", "tablet", "desktop", "gap", "animated", "stagger<PERSON><PERSON><PERSON><PERSON>", "gapClasses", "sm", "md", "lg", "xl", "getGridCols", "mobileClass", "tabletClass", "desktopClass", "trim", "gridClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "div", "variants", "initial", "animate", "Children", "map", "child", "index"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/ResponsiveGrid.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ResponsiveGridProps {\n  children: React.ReactNode;\n  className?: string;\n  columns?: {\n    mobile?: number;\n    tablet?: number;\n    desktop?: number;\n  };\n  gap?: 'sm' | 'md' | 'lg' | 'xl';\n  animated?: boolean;\n  staggerChildren?: boolean;\n}\n\nconst ResponsiveGrid: React.FC<ResponsiveGridProps> = ({\n  children,\n  className = '',\n  columns = { mobile: 1, tablet: 2, desktop: 3 },\n  gap = 'md',\n  animated = false,\n  staggerChildren = false\n}) => {\n  const gapClasses = {\n    sm: 'gap-2 sm:gap-3 md:gap-4',\n    md: 'gap-4 sm:gap-6 md:gap-8',\n    lg: 'gap-6 sm:gap-8 md:gap-12',\n    xl: 'gap-8 sm:gap-12 md:gap-16'\n  };\n\n  const getGridCols = () => {\n    const { mobile = 1, tablet = 2, desktop = 3 } = columns;\n    \n    const mobileClass = `grid-cols-${mobile}`;\n    const tabletClass = tablet ? `md:grid-cols-${tablet}` : '';\n    const desktopClass = desktop ? `lg:grid-cols-${desktop}` : '';\n    \n    return `${mobileClass} ${tabletClass} ${desktopClass}`.trim();\n  };\n\n  const gridClasses = `\n    grid\n    ${getGridCols()}\n    ${gapClasses[gap]}\n    ${className}\n  `.trim();\n\n  if (animated) {\n    const containerVariants = {\n      hidden: { opacity: 0 },\n      visible: {\n        opacity: 1,\n        transition: {\n          staggerChildren: staggerChildren ? 0.1 : 0,\n          delayChildren: 0.2\n        }\n      }\n    };\n\n    const itemVariants = {\n      hidden: { opacity: 0, y: 20 },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: { duration: 0.6 }\n      }\n    };\n\n    return (\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className={gridClasses}\n      >\n        {React.Children.map(children, (child, index) => (\n          <motion.div key={index} variants={itemVariants}>\n            {child}\n          </motion.div>\n        ))}\n      </motion.div>\n    );\n  }\n\n  return (\n    <div className={gridClasses}>\n      {children}\n    </div>\n  );\n};\n\nexport default ResponsiveGrid;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAevC,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAOhD,IAPiD,CACrDC,QAAQ,CACRC,SAAS,CAAG,EAAE,CACdC,OAAO,CAAG,CAAEC,MAAM,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAE,CAAC,CAC9CC,GAAG,CAAG,IAAI,CACVC,QAAQ,CAAG,KAAK,CAChBC,eAAe,CAAG,KACpB,CAAC,CAAAT,IAAA,CACC,KAAM,CAAAU,UAAU,CAAG,CACjBC,EAAE,CAAE,yBAAyB,CAC7BC,EAAE,CAAE,yBAAyB,CAC7BC,EAAE,CAAE,0BAA0B,CAC9BC,EAAE,CAAE,2BACN,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEX,MAAM,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAEC,OAAO,CAAG,CAAE,CAAC,CAAGH,OAAO,CAEvD,KAAM,CAAAa,WAAW,CAAG,aAAaZ,MAAM,EAAE,CACzC,KAAM,CAAAa,WAAW,CAAGZ,MAAM,CAAG,gBAAgBA,MAAM,EAAE,CAAG,EAAE,CAC1D,KAAM,CAAAa,YAAY,CAAGZ,OAAO,CAAG,gBAAgBA,OAAO,EAAE,CAAG,EAAE,CAE7D,MAAO,GAAGU,WAAW,IAAIC,WAAW,IAAIC,YAAY,EAAE,CAACC,IAAI,CAAC,CAAC,CAC/D,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG;AACtB;AACA,MAAML,WAAW,CAAC,CAAC;AACnB,MAAML,UAAU,CAACH,GAAG,CAAC;AACrB,MAAML,SAAS;AACf,GAAG,CAACiB,IAAI,CAAC,CAAC,CAER,GAAIX,QAAQ,CAAE,CACZ,KAAM,CAAAa,iBAAiB,CAAG,CACxBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVhB,eAAe,CAAEA,eAAe,CAAG,GAAG,CAAG,CAAC,CAC1CiB,aAAa,CAAE,GACjB,CACF,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBL,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAC,CAC7BJ,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVK,CAAC,CAAE,CAAC,CACJH,UAAU,CAAE,CAAEI,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED,mBACE/B,IAAA,CAACF,MAAM,CAACkC,GAAG,EACTC,QAAQ,CAAEV,iBAAkB,CAC5BW,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,SAAS,CACjB/B,SAAS,CAAEkB,WAAY,CAAAnB,QAAA,CAEtBN,KAAK,CAACuC,QAAQ,CAACC,GAAG,CAAClC,QAAQ,CAAE,CAACmC,KAAK,CAAEC,KAAK,gBACzCvC,IAAA,CAACF,MAAM,CAACkC,GAAG,EAAaC,QAAQ,CAAEJ,YAAa,CAAA1B,QAAA,CAC5CmC,KAAK,EADSC,KAEL,CACb,CAAC,CACQ,CAAC,CAEjB,CAEA,mBACEvC,IAAA,QAAKI,SAAS,CAAEkB,WAAY,CAAAnB,QAAA,CACzBA,QAAQ,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}