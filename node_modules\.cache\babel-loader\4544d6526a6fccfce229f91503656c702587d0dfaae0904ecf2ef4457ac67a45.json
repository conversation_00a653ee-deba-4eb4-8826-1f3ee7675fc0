{"ast": null, "code": "import { db } from '../config/firebase';\nimport { collection, doc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { mockCourses, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\n\n// Mock categories data\nconst mockCategories = [{\n  id: 'programming',\n  name: 'البرمجة',\n  description: 'كورسات البرمجة وتطوير البرمجيات',\n  color: 'blue',\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}, {\n  id: 'web',\n  name: 'تطوير الويب',\n  description: 'كورسات تطوير المواقع والتطبيقات',\n  color: 'green',\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}, {\n  id: 'ai',\n  name: 'الذكاء الاصطناعي',\n  description: 'كورسات الذكاء الاصطناعي وتعلم الآلة',\n  color: 'purple',\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}];\nclass DataService {\n  // Courses\n  async getCourses() {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n  async getCourse(id) {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n  async addCourse(course) {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n  async updateCourse(id, course) {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n  async deleteCourse(id) {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents() {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n  async getStudent(id) {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes() {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n  async getQuiz(id) {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates() {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n  async getStudentCertificates(studentId) {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Categories\n  async getCategories() {\n    try {\n      return mockCategories;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      return mockCategories;\n    }\n  }\n  async getCategory(id) {\n    try {\n      const category = mockCategories.find(c => c.id === id);\n      return category || null;\n    } catch (error) {\n      console.error('Error fetching category:', error);\n      return null;\n    }\n  }\n  async createCategory(category) {\n    try {\n      const docRef = await addDoc(collection(db, 'categories'), category);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding category:', error);\n      throw error;\n    }\n  }\n  async updateCategory(id, category) {\n    try {\n      await updateDoc(doc(db, 'categories', id), category);\n    } catch (error) {\n      console.error('Error updating category:', error);\n      throw error;\n    }\n  }\n  async deleteCategory(id) {\n    try {\n      await deleteDoc(doc(db, 'categories', id));\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      throw error;\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.length * 299,\n      // Mock revenue calculation\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\nexport const dataService = new DataService();", "map": {"version": 3, "names": ["db", "collection", "doc", "addDoc", "updateDoc", "deleteDoc", "mockCourses", "mockQuizzes", "mockCertificates", "mockStudents", "mockCategories", "id", "name", "description", "color", "isActive", "createdAt", "Date", "DataService", "getCourses", "error", "console", "getCourse", "course", "find", "c", "addCourse", "doc<PERSON>ef", "updateCourse", "deleteCourse", "getStudents", "getStudent", "student", "s", "getQuizzes", "getQuiz", "quiz", "q", "getCertificates", "getStudentCertificates", "studentId", "filter", "cert", "getCategories", "getCategory", "category", "createCategory", "updateCategory", "deleteCategory", "getAnalytics", "totalStudents", "length", "totalCourses", "totalQuizzes", "totalCertificates", "revenue", "enrollments", "reduce", "sum", "enrolledCourses", "dataService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/dataService.ts"], "sourcesContent": ["import { db } from '../config/firebase';\nimport { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { Course, Student, Quiz, Certificate, Category } from '../types';\nimport { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\n\n// Mock categories data\nconst mockCategories: Category[] = [\n  {\n    id: 'programming',\n    name: 'البرمجة',\n    description: 'كورسات البرمجة وتطوير البرمجيات',\n    color: 'blue',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: 'web',\n    name: 'تطوير الويب',\n    description: 'كورسات تطوير المواقع والتطبيقات',\n    color: 'green',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: 'ai',\n    name: 'الذكاء الاصطناعي',\n    description: 'كورسات الذكاء الاصطناعي وتعلم الآلة',\n    color: 'purple',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nclass DataService {\n  // Courses\n  async getCourses(): Promise<Course[]> {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n\n  async getCourse(id: string): Promise<Course | null> {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n\n  async addCourse(course: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n\n  async updateCourse(id: string, course: Partial<Course>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n\n  async deleteCourse(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents(): Promise<Student[]> {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n\n  async getStudent(id: string): Promise<Student | null> {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes(): Promise<Quiz[]> {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n\n  async getQuiz(id: string): Promise<Quiz | null> {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates(): Promise<Certificate[]> {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n\n  async getStudentCertificates(studentId: string): Promise<Certificate[]> {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Categories\n  async getCategories(): Promise<Category[]> {\n    try {\n      return mockCategories;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      return mockCategories;\n    }\n  }\n\n  async getCategory(id: string): Promise<Category | null> {\n    try {\n      const category = mockCategories.find(c => c.id === id);\n      return category || null;\n    } catch (error) {\n      console.error('Error fetching category:', error);\n      return null;\n    }\n  }\n\n  async createCategory(category: Omit<Category, 'id' | 'createdAt'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'categories'), category);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding category:', error);\n      throw error;\n    }\n  }\n\n  async updateCategory(id: string, category: Partial<Category>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'categories', id), category);\n    } catch (error) {\n      console.error('Error updating category:', error);\n      throw error;\n    }\n  }\n\n  async deleteCategory(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'categories', id));\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      throw error;\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.length * 299, // Mock revenue calculation\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\n\nexport const dataService = new DataService();\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,oBAAoB;AACvC,SAASC,UAAU,EAAWC,GAAG,EAAUC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QAAQ,oBAAoB;AAEnG,SAASC,WAAW,EAAcC,WAAW,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC5F,SAASC,YAAY,QAAQ,sBAAsB;;AAEnD;AACA,MAAMC,cAA0B,GAAG,CACjC;EACEC,EAAE,EAAE,aAAa;EACjBC,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,iCAAiC;EAC9CC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEN,EAAE,EAAE,KAAK;EACTC,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,iCAAiC;EAC9CC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEN,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,qCAAqC;EAClDC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,MAAMC,WAAW,CAAC;EAChB;EACA,MAAMC,UAAUA,CAAA,EAAsB;IACpC,IAAI;MACF;MACA,OAAOb,WAAW;IACpB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOd,WAAW,CAAC,CAAC;IACtB;EACF;EAEA,MAAMgB,SAASA,CAACX,EAAU,EAA0B;IAClD,IAAI;MACF,MAAMY,MAAM,GAAGjB,WAAW,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKA,EAAE,CAAC;MACjD,OAAOY,MAAM,IAAI,IAAI;IACvB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,IAAI;IACb;EACF;EAEA,MAAMM,SAASA,CAACH,MAA0B,EAAmB;IAC3D,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMxB,MAAM,CAACF,UAAU,CAACD,EAAE,EAAE,SAAS,CAAC,EAAEuB,MAAM,CAAC;MAC9D,OAAOI,MAAM,CAAChB,EAAE;IAClB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMQ,YAAYA,CAACjB,EAAU,EAAEY,MAAuB,EAAiB;IACrE,IAAI;MACF,MAAMnB,SAAS,CAACF,GAAG,CAACF,EAAE,EAAE,SAAS,EAAEW,EAAE,CAAC,EAAEY,MAAM,CAAC;IACjD,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMS,YAAYA,CAAClB,EAAU,EAAiB;IAC5C,IAAI;MACF,MAAMN,SAAS,CAACH,GAAG,CAACF,EAAE,EAAE,SAAS,EAAEW,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMU,WAAWA,CAAA,EAAuB;IACtC,IAAI;MACF,OAAOrB,YAAY;IACrB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAOX,YAAY;IACrB;EACF;EAEA,MAAMsB,UAAUA,CAACpB,EAAU,EAA2B;IACpD,IAAI;MACF,MAAMqB,OAAO,GAAGvB,YAAY,CAACe,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKA,EAAE,CAAC;MACnD,OAAOqB,OAAO,IAAI,IAAI;IACxB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMc,UAAUA,CAAA,EAAoB;IAClC,IAAI;MACF,OAAO3B,WAAW;IACpB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOb,WAAW;IACpB;EACF;EAEA,MAAM4B,OAAOA,CAACxB,EAAU,EAAwB;IAC9C,IAAI;MACF,MAAMyB,IAAI,GAAG7B,WAAW,CAACiB,IAAI,CAACa,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKA,EAAE,CAAC;MAC/C,OAAOyB,IAAI,IAAI,IAAI;IACrB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMkB,eAAeA,CAAA,EAA2B;IAC9C,IAAI;MACF,OAAO9B,gBAAgB;IACzB,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOZ,gBAAgB;IACzB;EACF;EAEA,MAAM+B,sBAAsBA,CAACC,SAAiB,EAA0B;IACtE,IAAI;MACF,OAAOhC,gBAAgB,CAACiC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,SAAS,KAAKA,SAAS,CAAC;IACtE,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,EAAE;IACX;EACF;;EAEA;EACA,MAAMuB,aAAaA,CAAA,EAAwB;IACzC,IAAI;MACF,OAAOjC,cAAc;IACvB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOV,cAAc;IACvB;EACF;EAEA,MAAMkC,WAAWA,CAACjC,EAAU,EAA4B;IACtD,IAAI;MACF,MAAMkC,QAAQ,GAAGnC,cAAc,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKA,EAAE,CAAC;MACtD,OAAOkC,QAAQ,IAAI,IAAI;IACzB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;EACF;EAEA,MAAM0B,cAAcA,CAACD,QAA4C,EAAmB;IAClF,IAAI;MACF,MAAMlB,MAAM,GAAG,MAAMxB,MAAM,CAACF,UAAU,CAACD,EAAE,EAAE,YAAY,CAAC,EAAE6C,QAAQ,CAAC;MACnE,OAAOlB,MAAM,CAAChB,EAAE;IAClB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;EAEA,MAAM2B,cAAcA,CAACpC,EAAU,EAAEkC,QAA2B,EAAiB;IAC3E,IAAI;MACF,MAAMzC,SAAS,CAACF,GAAG,CAACF,EAAE,EAAE,YAAY,EAAEW,EAAE,CAAC,EAAEkC,QAAQ,CAAC;IACtD,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;EAEA,MAAM4B,cAAcA,CAACrC,EAAU,EAAiB;IAC9C,IAAI;MACF,MAAMN,SAAS,CAACH,GAAG,CAACF,EAAE,EAAE,YAAY,EAAEW,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM6B,YAAYA,CAAA,EAAG;IACnB,OAAO;MACLC,aAAa,EAAEzC,YAAY,CAAC0C,MAAM;MAClCC,YAAY,EAAE9C,WAAW,CAAC6C,MAAM;MAChCE,YAAY,EAAE9C,WAAW,CAAC4C,MAAM;MAChCG,iBAAiB,EAAE9C,gBAAgB,CAAC2C,MAAM;MAC1CI,OAAO,EAAEjD,WAAW,CAAC6C,MAAM,GAAG,GAAG;MAAE;MACnCK,WAAW,EAAE/C,YAAY,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAE1B,OAAO,KAAK0B,GAAG,GAAG1B,OAAO,CAAC2B,eAAe,CAACR,MAAM,EAAE,CAAC;IAC5F,CAAC;EACH;AACF;AAEA,OAAO,MAAMS,WAAW,GAAG,IAAI1C,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}