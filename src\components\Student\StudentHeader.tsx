import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  CogIcon
} from '@heroicons/react/24/outline';

// Types
import { Student } from '../../types';

interface StudentHeaderProps {
  user: Student;
  onMenuClick: () => void;
  onLogout: () => void;
}

const StudentHeader: React.FC<StudentHeaderProps> = ({ user, onMenuClick, onLogout }) => {
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Side - Menu Button */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 touch-manipulation"
          >
            <Bars3Icon className="w-6 h-6" />
          </button>

          <div className="hidden md:block">
            <h1 className="text-2xl font-bold text-gray-900">
              مرحباً، {user.name || 'الطالب'}
            </h1>
            <p className="text-sm text-gray-500">
              استمر في رحلة التعلم مع منصة ALaa Abd Hamied | فريق ALaa Abd Elhamied 2025
            </p>
          </div>
        </div>

        {/* Right Side - Profile */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* Notifications */}
          <button className="relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200 touch-manipulation">
            <BellIcon className="w-6 h-6" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              3
            </span>
          </button>

          {/* Profile Menu */}
          <div className="relative">
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200 touch-manipulation"
            >
              <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                <UserCircleIcon className="w-5 h-5 text-white" />
              </div>
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium">{user.name || 'الطالب'}</p>
                <p className="text-xs text-gray-500">كود: {user.accessCode}</p>
              </div>
            </button>

            <AnimatePresence>
              {showProfileMenu && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                >
                  <div className="py-2">
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <UserCircleIcon className="w-4 h-4 ml-2" />
                      الملف الشخصي
                    </button>
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <CogIcon className="w-4 h-4 ml-2" />
                      الإعدادات
                    </button>
                    <hr className="my-2" />
                    <button
                      onClick={onLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <ArrowRightOnRectangleIcon className="w-4 h-4 ml-2" />
                      تسجيل الخروج
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Mobile Title */}
      <div className="lg:hidden px-6 pb-4">
        <h1 className="text-xl font-bold text-gray-900">
          مرحباً، {user.name || 'الطالب'}
        </h1>
        <p className="text-sm text-gray-500">
          كود الدخول: {user.accessCode}
        </p>
      </div>

      {/* Click outside to close menu */}
      {showProfileMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowProfileMenu(false)}
        />
      )}
    </header>
  );
};

export default StudentHeader;
