{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const ResponsiveText=_ref=>{let{children,as:Component='p',size='base',weight='normal',color='gray',align='right',className=''}=_ref;const sizeClasses={xs:'text-xs sm:text-sm',sm:'text-sm sm:text-base',base:'text-base sm:text-lg',lg:'text-lg sm:text-xl',xl:'text-xl sm:text-2xl','2xl':'text-2xl sm:text-3xl','3xl':'text-3xl sm:text-4xl','4xl':'text-4xl sm:text-5xl','5xl':'text-5xl sm:text-6xl'};const weightClasses={light:'font-light',normal:'font-normal',medium:'font-medium',semibold:'font-semibold',bold:'font-bold',extrabold:'font-extrabold'};const colorClasses={primary:'text-primary-600',secondary:'text-secondary-600',accent:'text-accent-600',gray:'text-gray-700',white:'text-white',black:'text-black'};const alignClasses={left:'text-left',center:'text-center',right:'text-right',justify:'text-justify'};const textClasses=`\n    ${sizeClasses[size]}\n    ${weightClasses[weight]}\n    ${colorClasses[color]}\n    ${alignClasses[align]}\n    ${className}\n  `.trim();return/*#__PURE__*/_jsx(Component,{className:textClasses,children:children});};export default ResponsiveText;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "ResponsiveText", "_ref", "children", "as", "Component", "size", "weight", "color", "align", "className", "sizeClasses", "xs", "sm", "base", "lg", "xl", "weightClasses", "light", "normal", "medium", "semibold", "bold", "extrabold", "colorClasses", "primary", "secondary", "accent", "gray", "white", "black", "alignClasses", "left", "center", "right", "justify", "textClasses", "trim"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/ResponsiveText.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ResponsiveTextProps {\n  children: React.ReactNode;\n  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';\n  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';\n  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';\n  color?: 'primary' | 'secondary' | 'accent' | 'gray' | 'white' | 'black';\n  align?: 'left' | 'center' | 'right' | 'justify';\n  className?: string;\n}\n\nconst ResponsiveText: React.FC<ResponsiveTextProps> = ({\n  children,\n  as: Component = 'p',\n  size = 'base',\n  weight = 'normal',\n  color = 'gray',\n  align = 'right',\n  className = ''\n}) => {\n  const sizeClasses = {\n    xs: 'text-xs sm:text-sm',\n    sm: 'text-sm sm:text-base',\n    base: 'text-base sm:text-lg',\n    lg: 'text-lg sm:text-xl',\n    xl: 'text-xl sm:text-2xl',\n    '2xl': 'text-2xl sm:text-3xl',\n    '3xl': 'text-3xl sm:text-4xl',\n    '4xl': 'text-4xl sm:text-5xl',\n    '5xl': 'text-5xl sm:text-6xl'\n  };\n\n  const weightClasses = {\n    light: 'font-light',\n    normal: 'font-normal',\n    medium: 'font-medium',\n    semibold: 'font-semibold',\n    bold: 'font-bold',\n    extrabold: 'font-extrabold'\n  };\n\n  const colorClasses = {\n    primary: 'text-primary-600',\n    secondary: 'text-secondary-600',\n    accent: 'text-accent-600',\n    gray: 'text-gray-700',\n    white: 'text-white',\n    black: 'text-black'\n  };\n\n  const alignClasses = {\n    left: 'text-left',\n    center: 'text-center',\n    right: 'text-right',\n    justify: 'text-justify'\n  };\n\n  const textClasses = `\n    ${sizeClasses[size]}\n    ${weightClasses[weight]}\n    ${colorClasses[color]}\n    ${alignClasses[align]}\n    ${className}\n  `.trim();\n\n  return (\n    <Component className={textClasses}>\n      {children}\n    </Component>\n  );\n};\n\nexport default ResponsiveText;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAY1B,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAQhD,IARiD,CACrDC,QAAQ,CACRC,EAAE,CAAEC,SAAS,CAAG,GAAG,CACnBC,IAAI,CAAG,MAAM,CACbC,MAAM,CAAG,QAAQ,CACjBC,KAAK,CAAG,MAAM,CACdC,KAAK,CAAG,OAAO,CACfC,SAAS,CAAG,EACd,CAAC,CAAAR,IAAA,CACC,KAAM,CAAAS,WAAW,CAAG,CAClBC,EAAE,CAAE,oBAAoB,CACxBC,EAAE,CAAE,sBAAsB,CAC1BC,IAAI,CAAE,sBAAsB,CAC5BC,EAAE,CAAE,oBAAoB,CACxBC,EAAE,CAAE,qBAAqB,CACzB,KAAK,CAAE,sBAAsB,CAC7B,KAAK,CAAE,sBAAsB,CAC7B,KAAK,CAAE,sBAAsB,CAC7B,KAAK,CAAE,sBACT,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,CACpBC,KAAK,CAAE,YAAY,CACnBC,MAAM,CAAE,aAAa,CACrBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,eAAe,CACzBC,IAAI,CAAE,WAAW,CACjBC,SAAS,CAAE,gBACb,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBC,OAAO,CAAE,kBAAkB,CAC3BC,SAAS,CAAE,oBAAoB,CAC/BC,MAAM,CAAE,iBAAiB,CACzBC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,YAAY,CACnBC,KAAK,CAAE,YACT,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBC,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAE,aAAa,CACrBC,KAAK,CAAE,YAAY,CACnBC,OAAO,CAAE,cACX,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG;AACtB,MAAMzB,WAAW,CAACL,IAAI,CAAC;AACvB,MAAMW,aAAa,CAACV,MAAM,CAAC;AAC3B,MAAMiB,YAAY,CAAChB,KAAK,CAAC;AACzB,MAAMuB,YAAY,CAACtB,KAAK,CAAC;AACzB,MAAMC,SAAS;AACf,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAER,mBACErC,IAAA,CAACK,SAAS,EAACK,SAAS,CAAE0B,WAAY,CAAAjC,QAAA,CAC/BA,QAAQ,CACA,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}