{"ast": null, "code": "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n  constructor(url) {\n    let headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let fetch = arguments.length > 2 ? arguments[2] : undefined;\n    super(url, headers, fetch);\n  }\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id) {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch);\n  }\n}", "map": {"version": 3, "names": ["StorageFileApi", "StorageBucketApi", "StorageClient", "constructor", "url", "headers", "arguments", "length", "undefined", "fetch", "from", "id"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\storage-js\\src\\StorageClient.ts"], "sourcesContent": ["import StorageFileApi from './packages/StorageFileApi'\nimport StorageBucketApi from './packages/StorageBucketApi'\nimport { Fetch } from './lib/fetch'\n\nexport class StorageClient extends StorageBucketApi {\n  constructor(url: string, headers: { [key: string]: string } = {}, fetch?: Fetch) {\n    super(url, headers, fetch)\n  }\n\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id: string): StorageFileApi {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch)\n  }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,6BAA6B;AAG1D,OAAM,MAAOC,aAAc,SAAQD,gBAAgB;EACjDE,YAAYC,GAAW,EAAwD;IAAA,IAAtDC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqC,EAAE;IAAA,IAAEG,KAAa,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAC7E,KAAK,CAACJ,GAAG,EAAEC,OAAO,EAAEI,KAAK,CAAC;EAC5B;EAEA;;;;;EAKAC,IAAIA,CAACC,EAAU;IACb,OAAO,IAAIX,cAAc,CAAC,IAAI,CAACI,GAAG,EAAE,IAAI,CAACC,OAAO,EAAEM,EAAE,EAAE,IAAI,CAACF,KAAK,CAAC;EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}