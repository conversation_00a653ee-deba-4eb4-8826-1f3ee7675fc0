import { Student } from '../types';

export const mockStudents: Student[] = [
  {
    id: 'student-001',
    email: '<EMAIL>',
    name: 'أحمد محمد',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    accessCode: '1234567',
    enrolledCourses: ['1', '2'],
    completedCourses: ['1'],
    certificates: ['cert-001'],
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'student-002',
    email: '<EMAIL>',
    name: 'فاطمة أحمد',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    accessCode: '7654321',
    enrolledCourses: ['2'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-01-20')
  },
  {
    id: 'student-003',
    email: '<EMAIL>',
    name: 'محمد علي',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    accessCode: '1111111',
    enrolledCourses: ['1'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-02-01')
  }
];

// بيانات تسجيل دخول الطلاب للاختبار
export const studentCredentials = [
  { email: '<EMAIL>', password: 'Student@123', accessCode: '1234567' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '7654321' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '1111111' }
];
