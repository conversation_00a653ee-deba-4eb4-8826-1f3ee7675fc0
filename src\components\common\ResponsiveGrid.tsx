import React from 'react';
import { motion } from 'framer-motion';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  staggerChildren?: boolean;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  animated = false,
  staggerChildren = false
}) => {
  const gapClasses = {
    sm: 'gap-2 sm:gap-3 md:gap-4',
    md: 'gap-4 sm:gap-6 md:gap-8',
    lg: 'gap-6 sm:gap-8 md:gap-12',
    xl: 'gap-8 sm:gap-12 md:gap-16'
  };

  const getGridCols = () => {
    const { mobile = 1, tablet = 2, desktop = 3 } = columns;
    
    const mobileClass = `grid-cols-${mobile}`;
    const tabletClass = tablet ? `md:grid-cols-${tablet}` : '';
    const desktopClass = desktop ? `lg:grid-cols-${desktop}` : '';
    
    return `${mobileClass} ${tabletClass} ${desktopClass}`.trim();
  };

  const gridClasses = `
    grid
    ${getGridCols()}
    ${gapClasses[gap]}
    ${className}
  `.trim();

  if (animated) {
    const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: staggerChildren ? 0.1 : 0,
          delayChildren: 0.2
        }
      }
    };

    const itemVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6 }
      }
    };

    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className={gridClasses}
      >
        {React.Children.map(children, (child, index) => (
          <motion.div key={index} variants={itemVariants}>
            {child}
          </motion.div>
        ))}
      </motion.div>
    );
  }

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
};

export default ResponsiveGrid;
