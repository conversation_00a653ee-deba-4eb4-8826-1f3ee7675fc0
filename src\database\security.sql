-- منصة ALaa Abd Hamied - سياسات الأمان
-- تم التطوير بواسطة فريق ALaa Abd Elhamied 2025

-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سياسات المدراء
CREATE POLICY "المدراء يمكنهم قراءة جميع بياناتهم" ON admins
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "المدراء يمكنهم تحديث بياناتهم" ON admins
    FOR UPDATE USING (auth.uid()::text = id::text);

-- سياسات الفئات
CREATE POLICY "الجميع يمكنهم قراءة الفئات النشطة" ON categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "المدراء فقط يمكنهم إدارة الفئات" ON categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الكورسات
CREATE POLICY "الجميع يمكنهم قراءة الكورسات النشطة" ON courses
    FOR SELECT USING (is_active = true);

CREATE POLICY "المدراء فقط يمكنهم إدارة الكورسات" ON courses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الطلاب
CREATE POLICY "الطلاب يمكنهم قراءة بياناتهم فقط" ON students
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "الطلاب يمكنهم تحديث بياناتهم" ON students
    FOR UPDATE USING (auth.uid()::text = id::text);

CREATE POLICY "المدراء يمكنهم إدارة الطلاب" ON students
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الفيديوهات
CREATE POLICY "الطلاب المسجلين يمكنهم مشاهدة الفيديوهات" ON videos
    FOR SELECT USING (
        is_free = true OR
        EXISTS (
            SELECT 1 FROM student_enrollments se
            JOIN students s ON s.id = se.student_id
            WHERE s.id::text = auth.uid()::text
            AND se.course_id = videos.course_id
        )
    );

CREATE POLICY "المدراء يمكنهم إدارة الفيديوهات" ON videos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الاختبارات
CREATE POLICY "الطلاب المسجلين يمكنهم رؤية الاختبارات" ON quizzes
    FOR SELECT USING (
        is_active = true AND
        EXISTS (
            SELECT 1 FROM student_enrollments se
            JOIN students s ON s.id = se.student_id
            WHERE s.id::text = auth.uid()::text
            AND se.course_id = quizzes.course_id
        )
    );

CREATE POLICY "المدراء يمكنهم إدارة الاختبارات" ON quizzes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الأسئلة
CREATE POLICY "الطلاب المسجلين يمكنهم رؤية الأسئلة" ON questions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM student_enrollments se
            JOIN students s ON s.id = se.student_id
            JOIN quizzes q ON q.id = questions.quiz_id
            WHERE s.id::text = auth.uid()::text
            AND se.course_id = q.course_id
        )
    );

CREATE POLICY "المدراء يمكنهم إدارة الأسئلة" ON questions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات التسجيل في الكورسات
CREATE POLICY "الطلاب يمكنهم رؤية تسجيلاتهم" ON student_enrollments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM students s
            WHERE s.id = student_enrollments.student_id
            AND s.id::text = auth.uid()::text
        )
    );

CREATE POLICY "الطلاب يمكنهم تحديث تقدمهم" ON student_enrollments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM students s
            WHERE s.id = student_enrollments.student_id
            AND s.id::text = auth.uid()::text
        )
    );

CREATE POLICY "المدراء يمكنهم إدارة التسجيلات" ON student_enrollments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات تقدم الفيديوهات
CREATE POLICY "الطلاب يمكنهم رؤية وتحديث تقدمهم" ON video_progress
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM students s
            WHERE s.id = video_progress.student_id
            AND s.id::text = auth.uid()::text
        )
    );

CREATE POLICY "المدراء يمكنهم رؤية تقدم الطلاب" ON video_progress
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات محاولات الاختبارات
CREATE POLICY "الطلاب يمكنهم رؤية وإضافة محاولاتهم" ON quiz_attempts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM students s
            WHERE s.id = quiz_attempts.student_id
            AND s.id::text = auth.uid()::text
        )
    );

CREATE POLICY "المدراء يمكنهم رؤية جميع المحاولات" ON quiz_attempts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الشهادات
CREATE POLICY "الطلاب يمكنهم رؤية شهاداتهم" ON certificates
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM students s
            WHERE s.id = certificates.student_id
            AND s.id::text = auth.uid()::text
        )
    );

CREATE POLICY "المدراء يمكنهم إدارة الشهادات" ON certificates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );

-- سياسات الإشعارات
CREATE POLICY "المستخدمون يمكنهم رؤية إشعاراتهم" ON notifications
    FOR SELECT USING (
        (recipient_type = 'student' AND 
         EXISTS (SELECT 1 FROM students WHERE id::text = auth.uid()::text AND id::text = notifications.recipient_id::text)) OR
        (recipient_type = 'admin' AND 
         EXISTS (SELECT 1 FROM admins WHERE id::text = auth.uid()::text AND id::text = notifications.recipient_id::text))
    );

CREATE POLICY "المستخدمون يمكنهم تحديث حالة قراءة إشعاراتهم" ON notifications
    FOR UPDATE USING (
        (recipient_type = 'student' AND 
         EXISTS (SELECT 1 FROM students WHERE id::text = auth.uid()::text AND id::text = notifications.recipient_id::text)) OR
        (recipient_type = 'admin' AND 
         EXISTS (SELECT 1 FROM admins WHERE id::text = auth.uid()::text AND id::text = notifications.recipient_id::text))
    );

CREATE POLICY "المدراء يمكنهم إنشاء إشعارات" ON notifications
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE id::text = auth.uid()::text 
            AND is_active = true
        )
    );
