{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nclass PostgrestQueryBuilder {\n  constructor(url, _ref) {\n    let {\n      headers = {},\n      schema,\n      fetch\n    } = _ref;\n    this.url = url;\n    this.headers = headers;\n    this.schema = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select(columns) {\n    let {\n      head = false,\n      count\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const method = head ? 'HEAD' : 'GET';\n    // Remove whitespaces except when quoted\n    let quoted = false;\n    const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*').split('').map(c => {\n      if (/\\s/.test(c) && !quoted) {\n        return '';\n      }\n      if (c === '\"') {\n        quoted = !quoted;\n      }\n      return c;\n    }).join('');\n    this.url.searchParams.set('select', cleanedColumns);\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert(values) {\n    let {\n      count,\n      defaultToNull = true\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const method = 'POST';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert(values) {\n    let {\n      onConflict,\n      ignoreDuplicates = false,\n      count,\n      defaultToNull = true\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const method = 'POST';\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict);\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update(values) {\n    let {\n      count\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const method = 'PATCH';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete() {\n    let {\n      count\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const method = 'DELETE';\n    const prefersHeaders = [];\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer']);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestQueryBuilder;", "map": {"version": 3, "names": ["PostgrestFilterBuilder_1", "__importDefault", "require", "PostgrestQueryBuilder", "constructor", "url", "_ref", "headers", "schema", "fetch", "select", "columns", "head", "count", "arguments", "length", "undefined", "method", "quoted", "cleanedColumns", "split", "map", "c", "test", "join", "searchParams", "set", "default", "allowEmpty", "insert", "values", "defaultToNull", "prefersHeaders", "push", "Array", "isArray", "reduce", "acc", "x", "concat", "Object", "keys", "uniqueColumns", "Set", "column", "body", "upsert", "onConflict", "ignoreDuplicates", "update", "delete", "unshift", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestQueryBuilder.ts"], "sourcesContent": ["import PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport { GetResult } from './select-query-parser/result'\nimport { Fetch, GenericSchema, GenericTable, GenericView } from './types'\n\nexport default class PostgrestQueryBuilder<\n  Schema extends GenericSchema,\n  Relation extends GenericTable | GenericView,\n  RelationName = unknown,\n  Relationships = Relation extends { Relationships: infer R } ? R : unknown\n> {\n  url: URL\n  headers: Record<string, string>\n  schema?: string\n  signal?: AbortSignal\n  fetch?: Fetch\n\n  constructor(\n    url: URL,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: string\n      fetch?: Fetch\n    }\n  ) {\n    this.url = url\n    this.headers = headers\n    this.schema = schema\n    this.fetch = fetch\n  }\n\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select<\n    Query extends string = '*',\n    ResultOne = GetResult<Schema, Relation['Row'], RelationName, Relationships, Query>\n  >(\n    columns?: Query,\n    {\n      head = false,\n      count,\n    }: {\n      head?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], ResultOne[], RelationName, Relationships> {\n    const method = head ? 'HEAD' : 'GET'\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<ResultOne[]>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk inserts.\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      count,\n      defaultToNull = true,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk upserts.\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      onConflict,\n      ignoreDuplicates = false,\n      count,\n      defaultToNull = true,\n    }: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`]\n\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict)\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update<Row extends Relation extends { Update: unknown } ? Relation['Update'] : never>(\n    values: Row,\n    {\n      count,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'PATCH'\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count,\n  }: {\n    count?: 'exact' | 'planned' | 'estimated'\n  } = {}): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'DELETE'\n    const prefersHeaders = []\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer'])\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n}\n"], "mappings": ";;;;;;;;;;AACA,MAAAA,wBAAA,GAAAC,eAAA,CAAAC,OAAA;AAIA,MAAqBC,qBAAqB;EAYxCC,YACEC,GAAQ,EAAAC,IAAA,EASP;IAAA,IARD;MACEC,OAAO,GAAG,EAAE;MACZC,MAAM;MACNC;IAAK,CAKN,GAAAH,IAAA;IAED,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBAC,MAAMA,CAIJC,OAAe,EAOT;IAAA,IANN;MACEC,IAAI,GAAG,KAAK;MACZC;IAAK,IAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAIH,EAAE;IAEN,MAAMG,MAAM,GAAGL,IAAI,GAAG,MAAM,GAAG,KAAK;IACpC;IACA,IAAIM,MAAM,GAAG,KAAK;IAClB,MAAMC,cAAc,GAAG,CAACR,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,GAAG,EACnCS,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAEC,CAAC,IAAI;MACT,IAAI,IAAI,CAACC,IAAI,CAACD,CAAC,CAAC,IAAI,CAACJ,MAAM,EAAE;QAC3B,OAAO,EAAE;;MAEX,IAAII,CAAC,KAAK,GAAG,EAAE;QACbJ,MAAM,GAAG,CAACA,MAAM;;MAElB,OAAOI,CAAC;IACV,CAAC,CAAC,CACDE,IAAI,CAAC,EAAE,CAAC;IACX,IAAI,CAACnB,GAAG,CAACoB,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAEP,cAAc,CAAC;IACnD,IAAIN,KAAK,EAAE;MACT,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,GAAG,SAASM,KAAK,EAAE;;IAG3C,OAAO,IAAIb,wBAAA,CAAA2B,OAAsB,CAAC;MAChCV,MAAM;MACNZ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmB,UAAU,EAAE;KAC+B,CAAC;EAChD;EAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BAC,MAAMA,CACJC,MAAmB,EAOb;IAAA,IANN;MACEjB,KAAK;MACLkB,aAAa,GAAG;IAAI,IAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAIlB,EAAE;IAEN,MAAMG,MAAM,GAAG,MAAM;IAErB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAI,IAAI,CAACzB,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1ByB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTmB,cAAc,CAACC,IAAI,CAAC,SAASpB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACkB,aAAa,EAAE;MAClBC,cAAc,CAACC,IAAI,CAAC,iBAAiB,CAAC;;IAExC,IAAI,CAAC1B,OAAO,CAAC,QAAQ,CAAC,GAAGyB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,IAAIU,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACzB,MAAMnB,OAAO,GAAGmB,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,CAACE,MAAM,CAACC,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC;MACrF,IAAI3B,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM2B,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChC,OAAO,CAAC,CAAC,CAACU,GAAG,CAAEuB,MAAM,IAAK,IAAIA,MAAM,GAAG,CAAC;QAC1E,IAAI,CAACvC,GAAG,CAACoB,YAAY,CAACC,GAAG,CAAC,SAAS,EAAEgB,aAAa,CAAClB,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAIjE,OAAO,IAAIxB,wBAAA,CAAA2B,OAAsB,CAAC;MAChCV,MAAM;MACNZ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBqC,IAAI,EAAEf,MAAM;MACZrB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmB,UAAU,EAAE;KACwB,CAAC;EACzC;EAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCAkB,MAAMA,CACJhB,MAAmB,EAWb;IAAA,IAVN;MACEiB,UAAU;MACVC,gBAAgB,GAAG,KAAK;MACxBnC,KAAK;MACLkB,aAAa,GAAG;IAAI,IAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAMlB,EAAE;IAEN,MAAMG,MAAM,GAAG,MAAM;IAErB,MAAMe,cAAc,GAAG,CAAC,cAAcgB,gBAAgB,GAAG,QAAQ,GAAG,OAAO,aAAa,CAAC;IAEzF,IAAID,UAAU,KAAK/B,SAAS,EAAE,IAAI,CAACX,GAAG,CAACoB,YAAY,CAACC,GAAG,CAAC,aAAa,EAAEqB,UAAU,CAAC;IAClF,IAAI,IAAI,CAACxC,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1ByB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTmB,cAAc,CAACC,IAAI,CAAC,SAASpB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACkB,aAAa,EAAE;MAClBC,cAAc,CAACC,IAAI,CAAC,iBAAiB,CAAC;;IAExC,IAAI,CAAC1B,OAAO,CAAC,QAAQ,CAAC,GAAGyB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,IAAIU,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACzB,MAAMnB,OAAO,GAAGmB,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,CAACE,MAAM,CAACC,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC;MACrF,IAAI3B,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM2B,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChC,OAAO,CAAC,CAAC,CAACU,GAAG,CAAEuB,MAAM,IAAK,IAAIA,MAAM,GAAG,CAAC;QAC1E,IAAI,CAACvC,GAAG,CAACoB,YAAY,CAACC,GAAG,CAAC,SAAS,EAAEgB,aAAa,CAAClB,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAIjE,OAAO,IAAIxB,wBAAA,CAAA2B,OAAsB,CAAC;MAChCV,MAAM;MACNZ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBqC,IAAI,EAAEf,MAAM;MACZrB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmB,UAAU,EAAE;KACwB,CAAC;EACzC;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBAqB,MAAMA,CACJnB,MAAW,EAKL;IAAA,IAJN;MACEjB;IAAK,IAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAGH,EAAE;IAEN,MAAMG,MAAM,GAAG,OAAO;IACtB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAI,IAAI,CAACzB,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1ByB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAE7C,IAAIM,KAAK,EAAE;MACTmB,cAAc,CAACC,IAAI,CAAC,SAASpB,KAAK,EAAE,CAAC;;IAEvC,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,GAAGyB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,OAAO,IAAIxB,wBAAA,CAAA2B,OAAsB,CAAC;MAChCV,MAAM;MACNZ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBqC,IAAI,EAAEf,MAAM;MACZrB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmB,UAAU,EAAE;KACwB,CAAC;EACzC;EAEA;;;;;;;;;;;;;;;;;;;EAmBAsB,MAAMA,CAAA,EAIA;IAAA,IAJC;MACLrC;IAAK,IAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAGH,EAAE;IACJ,MAAMG,MAAM,GAAG,QAAQ;IACvB,MAAMe,cAAc,GAAG,EAAE;IACzB,IAAInB,KAAK,EAAE;MACTmB,cAAc,CAACC,IAAI,CAAC,SAASpB,KAAK,EAAE,CAAC;;IAEvC,IAAI,IAAI,CAACN,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1ByB,cAAc,CAACmB,OAAO,CAAC,IAAI,CAAC5C,OAAO,CAAC,QAAQ,CAAC,CAAC;;IAEhD,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAGyB,cAAc,CAACR,IAAI,CAAC,GAAG,CAAC;IAEjD,OAAO,IAAIxB,wBAAA,CAAA2B,OAAsB,CAAC;MAChCV,MAAM;MACNZ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBmB,UAAU,EAAE;KACwB,CAAC;EACzC;;AAtXFwB,OAAA,CAAAzB,OAAA,GAAAxB,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}