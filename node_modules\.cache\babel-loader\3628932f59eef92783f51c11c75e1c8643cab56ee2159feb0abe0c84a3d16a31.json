{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,UserIcon,AcademicCapIcon,CheckBadgeIcon}from'@heroicons/react/24/outline';import{dataService}from'../../services/dataService';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentsManagement=_ref=>{let{onBack}=_ref;const[students,setStudents]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('all');// Use data from dataService\nconst[loading,setLoading]=useState(true);useEffect(()=>{const loadStudents=async()=>{try{const studentsData=await dataService.getStudents();setStudents(studentsData);setLoading(false);}catch(error){console.error('Error loading students:',error);setLoading(false);}};loadStudents();},[]);const filteredStudents=students.filter(student=>{var _student$name;const matchesSearch=((_student$name=student.name)===null||_student$name===void 0?void 0:_student$name.toLowerCase().includes(searchTerm.toLowerCase()))||student.email.toLowerCase().includes(searchTerm.toLowerCase())||student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());let matchesStatus=true;if(statusFilter==='active'){matchesStatus=student.enrolledCourses.length>0;}else if(statusFilter==='completed'){matchesStatus=student.completedCourses.length>0;}return matchesSearch&&matchesStatus;});const handleAddStudent=()=>{// TODO: Implement add student functionality\nconsole.log('Add student');};const handleEditStudent=studentId=>{// TODO: Implement edit student functionality\nconsole.log('Edit student:',studentId);};const handleDeleteStudent=studentId=>{// TODO: Implement delete student functionality\nconsole.log('Delete student:',studentId);};const handleViewStudent=studentId=>{// TODO: Implement view student functionality\nconsole.log('View student:',studentId);};const getStudentStatus=student=>{if(student.completedCourses.length>0){return{status:'مكتمل',color:'green'};}else if(student.enrolledCourses.length>0){return{status:'نشط',color:'blue'};}else{return{status:'غير نشط',color:'gray'};}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddStudent,className:\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0637\\u0627\\u0644\\u0628 \\u062C\\u062F\\u064A\\u062F\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0627\\u0644\\u0627\\u0633\\u0645\\u060C \\u0627\\u0644\\u0625\\u064A\\u0645\\u064A\\u0644\\u060C \\u0623\\u0648 \\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644...\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0646\\u0634\\u0637\\u0648\\u0646\"}),/*#__PURE__*/_jsx(\"option\",{value:\"completed\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0648\\u0646\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredStudents.map((student,index)=>{const studentStatus=getStudentStatus(student);return/*#__PURE__*/_jsxs(motion.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.05},className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-blue-600\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:student.name||'غير محدد'}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:student.email})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\",children:student.accessCode})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.enrolledCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(CheckBadgeIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.completedCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.certificates.length})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${studentStatus.color==='green'?'bg-green-100 text-green-800':studentStatus.color==='blue'?'bg-blue-100 text-blue-800':'bg-gray-100 text-gray-800'}`,children:studentStatus.status})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(student.createdAt).toLocaleDateString('ar-SA')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewStudent(student.id),className:\"text-blue-600 hover:text-blue-900\",title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditStudent(student.id),className:\"text-green-600 hover:text-green-900\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteStudent(student.id),className:\"text-red-600 hover:text-red-900\",title:\"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]})})]},student.id);})})]})})}),filteredStudents.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u064A\\u0648\\u062C\\u062F \\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0637\\u0644\\u0627\\u0628 \\u064A\\u0637\\u0627\\u0628\\u0642\\u0648\\u0646 \\u0627\\u0644\\u0628\\u062D\\u062B\"})]})]});};export default StudentsManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "UserIcon", "AcademicCapIcon", "CheckBadgeIcon", "dataService", "jsx", "_jsx", "jsxs", "_jsxs", "StudentsManagement", "_ref", "onBack", "students", "setStudents", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "loading", "setLoading", "loadStudents", "studentsData", "getStudents", "error", "console", "filteredStudents", "filter", "student", "_student$name", "matchesSearch", "name", "toLowerCase", "includes", "email", "accessCode", "matchesStatus", "enrolledCourses", "length", "completedCourses", "handleAddStudent", "log", "handleEditStudent", "studentId", "handleDeleteStudent", "handleViewStudent", "getStudentStatus", "status", "color", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "target", "placeholder", "map", "index", "studentStatus", "tr", "initial", "opacity", "y", "animate", "transition", "delay", "certificates", "Date", "createdAt", "toLocaleDateString", "id", "title"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/StudentsManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  UserIcon,\n  AcademicCapIcon,\n  CheckBadgeIcon\n} from '@heroicons/react/24/outline';\nimport { dataService } from '../../services/dataService';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentsManagementProps {\n  onBack?: () => void;\n}\n\nconst StudentsManagement: React.FC<StudentsManagementProps> = ({ onBack }) => {\n  const [students, setStudents] = useState<Student[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  // Use data from dataService\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const loadStudents = async () => {\n      try {\n        const studentsData = await dataService.getStudents();\n        setStudents(studentsData);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error loading students:', error);\n        setLoading(false);\n      }\n    };\n\n    loadStudents();\n  }, []);\n\n\n\n  const filteredStudents = students.filter(student => {\n    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    let matchesStatus = true;\n    if (statusFilter === 'active') {\n      matchesStatus = student.enrolledCourses.length > 0;\n    } else if (statusFilter === 'completed') {\n      matchesStatus = student.completedCourses.length > 0;\n    }\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const handleAddStudent = () => {\n    // TODO: Implement add student functionality\n    console.log('Add student');\n  };\n\n  const handleEditStudent = (studentId: string) => {\n    // TODO: Implement edit student functionality\n    console.log('Edit student:', studentId);\n  };\n\n  const handleDeleteStudent = (studentId: string) => {\n    // TODO: Implement delete student functionality\n    console.log('Delete student:', studentId);\n  };\n\n  const handleViewStudent = (studentId: string) => {\n    // TODO: Implement view student functionality\n    console.log('View student:', studentId);\n  };\n\n  const getStudentStatus = (student: Student) => {\n    if (student.completedCourses.length > 0) {\n      return { status: 'مكتمل', color: 'green' };\n    } else if (student.enrolledCourses.length > 0) {\n      return { status: 'نشط', color: 'blue' };\n    } else {\n      return { status: 'غير نشط', color: 'gray' };\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الطلاب</h1>\n            <p className=\"text-gray-600\">إدارة وتتبع جميع الطلاب المسجلين</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddStudent}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة طالب جديد</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البحث في الطلاب\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"ابحث بالاسم، الإيميل، أو رمز الوصول...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              حالة الطالب\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">جميع الطلاب</option>\n              <option value=\"active\">الطلاب النشطون</option>\n              <option value=\"completed\">الطلاب المكتملون</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Students Table */}\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  رمز الوصول\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المسجلة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المكتملة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الشهادات\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الحالة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ التسجيل\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredStudents.map((student, index) => {\n                const studentStatus = getStudentStatus(student);\n                return (\n                  <motion.tr\n                    key={student.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                    className=\"hover:bg-gray-50\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <UserIcon className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {student.name || 'غير محدد'}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">{student.email}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\">\n                        {student.accessCode}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <AcademicCapIcon className=\"w-4 h-4 text-blue-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.enrolledCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <CheckBadgeIcon className=\"w-4 h-4 text-green-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.completedCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm text-gray-900\">{student.certificates.length}</span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        studentStatus.color === 'green' \n                          ? 'bg-green-100 text-green-800'\n                          : studentStatus.color === 'blue'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {studentStatus.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {new Date(student.createdAt).toLocaleDateString('ar-SA')}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <button\n                          onClick={() => handleViewStudent(student.id)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"عرض الطالب\"\n                        >\n                          <EyeIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleEditStudent(student.id)}\n                          className=\"text-green-600 hover:text-green-900\"\n                          title=\"تعديل الطالب\"\n                        >\n                          <PencilIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteStudent(student.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"حذف الطالب\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </motion.tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {filteredStudents.length === 0 && (\n        <div className=\"text-center py-12\">\n          <UserIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا يوجد طلاب</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي طلاب يطابقون البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StudentsManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,QAAQ,CACRC,eAAe,CACfC,cAAc,KACT,6BAA6B,CACpC,OAASC,WAAW,KAAQ,4BAA4B,CAExD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACvE,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsB,YAAY,CAAEC,eAAe,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAjB,WAAW,CAACkB,WAAW,CAAC,CAAC,CACpDT,WAAW,CAACQ,YAAY,CAAC,CACzBF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDC,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAIN,KAAM,CAAAK,gBAAgB,CAAGb,QAAQ,CAACc,MAAM,CAACC,OAAO,EAAI,KAAAC,aAAA,CAClD,KAAM,CAAAC,aAAa,CAAG,EAAAD,aAAA,CAAAD,OAAO,CAACG,IAAI,UAAAF,aAAA,iBAAZA,aAAA,CAAcG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC,GAC/DJ,OAAO,CAACM,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC,EAC9DJ,OAAO,CAACO,UAAU,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC,CAExF,GAAI,CAAAI,aAAa,CAAG,IAAI,CACxB,GAAInB,YAAY,GAAK,QAAQ,CAAE,CAC7BmB,aAAa,CAAGR,OAAO,CAACS,eAAe,CAACC,MAAM,CAAG,CAAC,CACpD,CAAC,IAAM,IAAIrB,YAAY,GAAK,WAAW,CAAE,CACvCmB,aAAa,CAAGR,OAAO,CAACW,gBAAgB,CAACD,MAAM,CAAG,CAAC,CACrD,CAEA,MAAO,CAAAR,aAAa,EAAIM,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAI,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACAf,OAAO,CAACgB,GAAG,CAAC,aAAa,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,SAAiB,EAAK,CAC/C;AACAlB,OAAO,CAACgB,GAAG,CAAC,eAAe,CAAEE,SAAS,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAID,SAAiB,EAAK,CACjD;AACAlB,OAAO,CAACgB,GAAG,CAAC,iBAAiB,CAAEE,SAAS,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIF,SAAiB,EAAK,CAC/C;AACAlB,OAAO,CAACgB,GAAG,CAAC,eAAe,CAAEE,SAAS,CAAC,CACzC,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAIlB,OAAgB,EAAK,CAC7C,GAAIA,OAAO,CAACW,gBAAgB,CAACD,MAAM,CAAG,CAAC,CAAE,CACvC,MAAO,CAAES,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC5C,CAAC,IAAM,IAAIpB,OAAO,CAACS,eAAe,CAACC,MAAM,CAAG,CAAC,CAAE,CAC7C,MAAO,CAAES,MAAM,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAC,CACzC,CAAC,IAAM,CACL,MAAO,CAAED,MAAM,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC7C,CACF,CAAC,CAED,mBACEvC,KAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBzC,KAAA,QAAKwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzC,KAAA,QAAKwC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDtC,MAAM,eACLL,IAAA,WACE4C,OAAO,CAAEvC,MAAO,CAChBqC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnE3C,IAAA,QAAK0C,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5E3C,IAAA,SAAMgD,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDjD,KAAA,QAAAyC,QAAA,eACE3C,IAAA,OAAI0C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,cAClE3C,IAAA,MAAG0C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8KAAgC,CAAG,CAAC,EAC9D,CAAC,EACH,CAAC,cACNzC,KAAA,WACE0C,OAAO,CAAEX,gBAAiB,CAC1BS,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvI3C,IAAA,CAACT,QAAQ,EAACmD,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC1C,IAAA,SAAA2C,QAAA,CAAM,kFAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,cAGN3C,IAAA,QAAK0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDzC,KAAA,QAAKwC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDzC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kFAEhE,CAAO,CAAC,cACR3C,IAAA,UACEoD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE7C,UAAW,CAClB8C,QAAQ,CAAGC,CAAC,EAAK9C,aAAa,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,8LAAwC,CACpDf,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cACNxC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRzC,KAAA,WACEmD,KAAK,CAAE3C,YAAa,CACpB4C,QAAQ,CAAGC,CAAC,EAAK5C,eAAe,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDX,SAAS,CAAC,wGAAwG,CAAAC,QAAA,eAElH3C,IAAA,WAAQqD,KAAK,CAAC,KAAK,CAAAV,QAAA,CAAC,+DAAW,CAAQ,CAAC,cACxC3C,IAAA,WAAQqD,KAAK,CAAC,QAAQ,CAAAV,QAAA,CAAC,iFAAc,CAAQ,CAAC,cAC9C3C,IAAA,WAAQqD,KAAK,CAAC,WAAW,CAAAV,QAAA,CAAC,6FAAgB,CAAQ,CAAC,EAC7C,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN3C,IAAA,QAAK0C,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5D3C,IAAA,QAAK0C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzC,KAAA,UAAOwC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD3C,IAAA,UAAO0C,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BzC,KAAA,OAAAyC,QAAA,eACE3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,yDAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,6FAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,mGAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,kDAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,2EAEhG,CAAI,CAAC,cACL3C,IAAA,OAAI0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,wDAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR3C,IAAA,UAAO0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDxB,gBAAgB,CAACuC,GAAG,CAAC,CAACrC,OAAO,CAAEsC,KAAK,GAAK,CACxC,KAAM,CAAAC,aAAa,CAAGrB,gBAAgB,CAAClB,OAAO,CAAC,CAC/C,mBACEnB,KAAA,CAACZ,MAAM,CAACuE,EAAE,EAERC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAER,KAAK,CAAG,IAAK,CAAE,CACpCjB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5B3C,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCzC,KAAA,QAAKwC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D3C,IAAA,QAAK0C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3C,IAAA,QAAK0C,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClF3C,IAAA,CAACL,QAAQ,EAAC+C,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,CACH,CAAC,cACNxC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,QAAK0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CtB,OAAO,CAACG,IAAI,EAAI,UAAU,CACxB,CAAC,cACNxB,IAAA,QAAK0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAACM,KAAK,CAAM,CAAC,EACzD,CAAC,EACH,CAAC,CACJ,CAAC,cACL3B,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC3C,IAAA,SAAM0C,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC5EtB,OAAO,CAACO,UAAU,CACf,CAAC,CACL,CAAC,cACL5B,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCzC,KAAA,QAAKwC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D3C,IAAA,CAACJ,eAAe,EAAC8C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACrD1C,IAAA,SAAM0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAACS,eAAe,CAACC,MAAM,CAAO,CAAC,EAC5E,CAAC,CACJ,CAAC,cACL/B,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCzC,KAAA,QAAKwC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D3C,IAAA,CAACH,cAAc,EAAC6C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACrD1C,IAAA,SAAM0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAACW,gBAAgB,CAACD,MAAM,CAAO,CAAC,EAC7E,CAAC,CACJ,CAAC,cACL/B,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC3C,IAAA,SAAM0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAAC+C,YAAY,CAACrC,MAAM,CAAO,CAAC,CAC1E,CAAC,cACL/B,IAAA,OAAI0C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC3C,IAAA,SAAM0C,SAAS,CAAE,4DACfkB,aAAa,CAACnB,KAAK,GAAK,OAAO,CAC3B,6BAA6B,CAC7BmB,aAAa,CAACnB,KAAK,GAAK,MAAM,CAC9B,2BAA2B,CAC3B,2BAA2B,EAC9B,CAAAE,QAAA,CACAiB,aAAa,CAACpB,MAAM,CACjB,CAAC,CACL,CAAC,cACLxC,IAAA,OAAI0C,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAA0B,IAAI,CAAChD,OAAO,CAACiD,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACtD,CAAC,cACLvE,IAAA,OAAI0C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7DzC,KAAA,QAAKwC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D3C,IAAA,WACE4C,OAAO,CAAEA,CAAA,GAAMN,iBAAiB,CAACjB,OAAO,CAACmD,EAAE,CAAE,CAC7C9B,SAAS,CAAC,mCAAmC,CAC7C+B,KAAK,CAAC,yDAAY,CAAA9B,QAAA,cAElB3C,IAAA,CAACN,OAAO,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACT1C,IAAA,WACE4C,OAAO,CAAEA,CAAA,GAAMT,iBAAiB,CAACd,OAAO,CAACmD,EAAE,CAAE,CAC7C9B,SAAS,CAAC,qCAAqC,CAC/C+B,KAAK,CAAC,qEAAc,CAAA9B,QAAA,cAEpB3C,IAAA,CAACR,UAAU,EAACkD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT1C,IAAA,WACE4C,OAAO,CAAEA,CAAA,GAAMP,mBAAmB,CAAChB,OAAO,CAACmD,EAAE,CAAE,CAC/C9B,SAAS,CAAC,iCAAiC,CAC3C+B,KAAK,CAAC,yDAAY,CAAA9B,QAAA,cAElB3C,IAAA,CAACP,SAAS,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GA/EArB,OAAO,CAACmD,EAgFJ,CAAC,CAEhB,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACH,CAAC,CAELrD,gBAAgB,CAACY,MAAM,GAAK,CAAC,eAC5B7B,KAAA,QAAKwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3C,IAAA,CAACL,QAAQ,EAAC+C,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7D1C,IAAA,OAAI0C,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,gEAAY,CAAI,CAAC,cACxE3C,IAAA,MAAG0C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yMAAuC,CAAG,CAAC,EACrE,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}