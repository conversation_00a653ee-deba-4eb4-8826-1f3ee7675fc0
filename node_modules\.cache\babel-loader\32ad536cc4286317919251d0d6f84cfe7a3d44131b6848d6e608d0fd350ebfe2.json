{"ast": null, "code": "export const mockCourses = [{\n  id: '1',\n  title: 'أساسيات البرمجة',\n  description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n  categoryId: 'programming',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-01'),\n  updatedAt: new Date('2024-01-15')\n}, {\n  id: '2',\n  title: 'تطوير المواقع الحديثة',\n  description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n  categoryId: 'web',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-10'),\n  updatedAt: new Date('2024-01-20')\n}, {\n  id: '3',\n  title: 'الذكاء الاصطناعي للمبتدئين',\n  description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n  categoryId: 'ai',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-15'),\n  updatedAt: new Date('2024-01-25')\n}];\nexport const mockVideos = [{\n  id: '1',\n  courseId: '1',\n  title: 'مقدمة في البرمجة',\n  description: 'تعرف على أساسيات البرمجة',\n  videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  duration: 930,\n  // 15:30 in seconds\n  orderIndex: 1,\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}, {\n  id: '2',\n  courseId: '1',\n  title: 'المتغيرات والثوابت',\n  description: 'تعلم كيفية استخدام المتغيرات',\n  videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  duration: 1245,\n  // 20:45 in seconds\n  orderIndex: 2,\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}];\nexport const mockQuizzes = [{\n  id: '1',\n  title: 'اختبار أساسيات البرمجة',\n  description: 'اختبر معرفتك في أساسيات البرمجة',\n  courseId: '1',\n  questions: [{\n    id: '1',\n    question: 'ما هو المتغير في البرمجة؟',\n    type: 'multiple-choice',\n    options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر للطباعة', 'لا شيء مما سبق'],\n    correctAnswer: 0,\n    points: 10,\n    explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n  }],\n  timeLimit: 30,\n  passingScore: 70,\n  attempts: 3,\n  isActive: true,\n  createdAt: new Date('2024-01-01')\n}];\nexport const mockCertificates = [{\n  id: 'cert-001',\n  studentId: 'student-001',\n  courseId: '1',\n  templateUrl: 'https://example.com/template/default.pdf',\n  certificateUrl: 'https://example.com/certificate/cert-001.pdf',\n  issuedAt: new Date('2024-02-01'),\n  verificationCode: 'CERT-001-VERIFY'\n}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "videos", "pdfs", "quizzes", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "courseId", "videoUrl", "duration", "orderIndex", "mockQuizzes", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "points", "explanation", "timeLimit", "passingScore", "attempts", "mockCertificates", "studentId", "templateUrl", "certificateUrl", "issuedAt", "verificationCode"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n    categoryId: 'programming',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع الحديثة',\n    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n    categoryId: 'web',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    id: '3',\n    title: 'الذكاء الاصطناعي للمبتدئين',\n    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n    categoryId: 'ai',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-25')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    courseId: '1',\n    title: 'مقدمة في البرمجة',\n    description: 'تعرف على أساسيات البرمجة',\n    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: 930, // 15:30 in seconds\n    orderIndex: 1,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: '2',\n    courseId: '1',\n    title: 'المتغيرات والثوابت',\n    description: 'تعلم كيفية استخدام المتغيرات',\n    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: 1245, // 20:45 in seconds\n    orderIndex: 2,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبر معرفتك في أساسيات البرمجة',\n    courseId: '1',\n    questions: [\n      {\n        id: '1',\n        question: 'ما هو المتغير في البرمجة؟',\n        type: 'multiple-choice',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر للطباعة',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0,\n        points: 10,\n        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n      }\n    ],\n    timeLimit: 30,\n    passingScore: 70,\n    attempts: 3,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    templateUrl: 'https://example.com/template/default.pdf',\n    certificateUrl: 'https://example.com/certificate/cert-001.pdf',\n    issuedAt: new Date('2024-02-01'),\n    verificationCode: 'CERT-001-VERIFY'\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,WAAqB,GAAG,CACnC;EACEC,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,4CAA4C;EACzDC,UAAU,EAAE,aAAa;EACzBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,2CAA2C;EACxDC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,4BAA4B;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,OAAO,MAAME,UAAmB,GAAG,CACjC;EACEZ,EAAE,EAAE,GAAG;EACPa,QAAQ,EAAE,GAAG;EACbZ,KAAK,EAAE,kBAAkB;EACzBC,WAAW,EAAE,0BAA0B;EACvCY,QAAQ,EAAE,6CAA6C;EACvDC,QAAQ,EAAE,GAAG;EAAE;EACfC,UAAU,EAAE,CAAC;EACbR,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,GAAG;EACPa,QAAQ,EAAE,GAAG;EACbZ,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CY,QAAQ,EAAE,6CAA6C;EACvDC,QAAQ,EAAE,IAAI;EAAE;EAChBC,UAAU,EAAE,CAAC;EACbR,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,OAAO,MAAMO,WAAmB,GAAG,CACjC;EACEjB,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,wBAAwB;EAC/BC,WAAW,EAAE,iCAAiC;EAC9CW,QAAQ,EAAE,GAAG;EACbK,SAAS,EAAE,CACT;IACElB,EAAE,EAAE,GAAG;IACPmB,QAAQ,EAAE,2BAA2B;IACrCC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,CACP,sBAAsB,EACtB,eAAe,EACf,aAAa,EACb,gBAAgB,CACjB;IACDC,aAAa,EAAE,CAAC;IAChBC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAE,CAAC;EACXnB,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,OAAO,MAAMkB,gBAA+B,GAAG,CAC7C;EACE5B,EAAE,EAAE,UAAU;EACd6B,SAAS,EAAE,aAAa;EACxBhB,QAAQ,EAAE,GAAG;EACbiB,WAAW,EAAE,0CAA0C;EACvDC,cAAc,EAAE,8CAA8C;EAC9DC,QAAQ,EAAE,IAAItB,IAAI,CAAC,YAAY,CAAC;EAChCuB,gBAAgB,EAAE;AACpB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}