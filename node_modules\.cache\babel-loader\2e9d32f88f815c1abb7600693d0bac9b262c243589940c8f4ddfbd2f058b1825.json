{"ast": null, "code": "import{signOut,onAuthStateChanged}from'firebase/auth';import{auth}from'../config/firebase';import{supabaseService}from'./supabaseService';class AuthService{// Admin login\nasync loginAdmin(email,password){try{// Get admin from Supabase\nconst admin=await supabaseService.getAdminByEmail(email);if(!admin){throw new Error('البريد الإلكتروني غير صحيح');}// Verify password (simplified for frontend)\n// In production, password verification should be done on the backend\nconst isPasswordValid=password==='Admin@123456';// Temporary for demo\nif(!isPasswordValid){throw new Error('كلمة المرور غير صحيحة');}return{id:admin.id,email:admin.email,role:'admin',name:admin.name,avatar:admin.avatar_url,permissions:admin.permissions||[],createdAt:new Date(admin.created_at)};}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Student login with access code\nasync loginStudent(accessCode){try{var _studentData$student_,_studentData$student_2,_studentData$certific;// Find student by access code in Supabase\nconst studentData=await supabaseService.getStudentByAccessCode(accessCode);if(!studentData){throw new Error('كود الدخول غير صحيح');}if(!studentData.is_active){throw new Error('الحساب غير مفعل');}// Transform Supabase data to match our Student type\nconst enrolledCourses=((_studentData$student_=studentData.student_enrollments)===null||_studentData$student_===void 0?void 0:_studentData$student_.map(enrollment=>enrollment.course_id))||[];const completedCourses=((_studentData$student_2=studentData.student_enrollments)===null||_studentData$student_2===void 0?void 0:_studentData$student_2.filter(enrollment=>enrollment.completed_at).map(enrollment=>enrollment.course_id))||[];const certificates=((_studentData$certific=studentData.certificates)===null||_studentData$certific===void 0?void 0:_studentData$certific.map(cert=>cert.id))||[];return{id:studentData.id,email:studentData.email||'',role:'student',name:studentData.name||'',avatar:studentData.avatar_url||'',accessCode:studentData.access_code,enrolledCourses,completedCourses,certificates,createdAt:new Date(studentData.created_at)};}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Generate access code for student\ngenerateAccessCode(){return Math.floor(1000000+Math.random()*9000000).toString();}// Create student account\nasync createStudent(studentData){try{const accessCode=this.generateAccessCode();// Create student in Supabase\nconst newStudent=await supabaseService.createStudent({accessCode:accessCode,name:studentData.name,email:studentData.email});// Enroll student in courses if provided\nif(studentData.enrolledCourses&&studentData.enrolledCourses.length>0){for(const courseId of studentData.enrolledCourses){await supabaseService.enrollStudent(newStudent.id,courseId);}}return accessCode;}catch(error){// If access code already exists, try again\nif(error.message.includes('duplicate key')){return this.createStudent(studentData);}throw new Error('فشل في إنشاء حساب الطالب');}}// Logout\nasync logout(){try{await signOut(auth);}catch(error){throw new Error('فشل في تسجيل الخروج');}}// Get current user\ngetCurrentUser(){return new Promise(resolve=>{const unsubscribe=onAuthStateChanged(auth,user=>{unsubscribe();resolve(user);});});}// Auth state listener\nonAuthStateChange(callback){return onAuthStateChanged(auth,callback);}getErrorMessage(errorCode){switch(errorCode){case'auth/user-not-found':return'المستخدم غير موجود';case'auth/wrong-password':return'كلمة المرور غير صحيحة';case'auth/invalid-email':return'البريد الإلكتروني غير صحيح';case'auth/user-disabled':return'الحساب معطل';case'auth/too-many-requests':return'محاولات كثيرة، حاول مرة أخرى لاحقاً';default:return'حدث خطأ في تسجيل الدخول';}}}export const authService=new AuthService();", "map": {"version": 3, "names": ["signOut", "onAuthStateChanged", "auth", "supabaseService", "AuthService", "loginAdmin", "email", "password", "admin", "getAdminByEmail", "Error", "isPasswordValid", "id", "role", "name", "avatar", "avatar_url", "permissions", "createdAt", "Date", "created_at", "error", "message", "loginStudent", "accessCode", "_studentData$student_", "_studentData$student_2", "_studentData$certific", "studentData", "getStudentByAccessCode", "is_active", "enrolledCourses", "student_enrollments", "map", "enrollment", "course_id", "completedCourses", "filter", "completed_at", "certificates", "cert", "access_code", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "newStudent", "length", "courseId", "enrollStudent", "includes", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "user", "onAuthStateChange", "callback", "getErrorMessage", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import {\n  signInWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { auth } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\nimport { supabaseService } from './supabaseService';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      // Get admin from Supabase\n      const admin = await supabaseService.getAdminByEmail(email);\n\n      if (!admin) {\n        throw new Error('البريد الإلكتروني غير صحيح');\n      }\n\n      // Verify password (simplified for frontend)\n      // In production, password verification should be done on the backend\n      const isPasswordValid = password === 'Admin@123456'; // Temporary for demo\n      if (!isPasswordValid) {\n        throw new Error('كلمة المرور غير صحيحة');\n      }\n\n      return {\n        id: admin.id,\n        email: admin.email,\n        role: 'admin',\n        name: admin.name,\n        avatar: admin.avatar_url,\n        permissions: admin.permissions || [],\n        createdAt: new Date(admin.created_at)\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Find student by access code in Supabase\n      const studentData = await supabaseService.getStudentByAccessCode(accessCode);\n\n      if (!studentData) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n\n      if (!studentData.is_active) {\n        throw new Error('الحساب غير مفعل');\n      }\n\n      // Transform Supabase data to match our Student type\n      const enrolledCourses = studentData.student_enrollments?.map((enrollment: any) => enrollment.course_id) || [];\n      const completedCourses = studentData.student_enrollments?.filter((enrollment: any) => enrollment.completed_at).map((enrollment: any) => enrollment.course_id) || [];\n      const certificates = studentData.certificates?.map((cert: any) => cert.id) || [];\n\n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name || '',\n        avatar: studentData.avatar_url || '',\n        accessCode: studentData.access_code,\n        enrolledCourses,\n        completedCourses,\n        certificates,\n        createdAt: new Date(studentData.created_at)\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n\n      // Create student in Supabase\n      const newStudent = await supabaseService.createStudent({\n        accessCode: accessCode,\n        name: studentData.name,\n        email: studentData.email\n      });\n\n      // Enroll student in courses if provided\n      if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {\n        for (const courseId of studentData.enrolledCourses) {\n          await supabaseService.enrollStudent(newStudent.id, courseId);\n        }\n      }\n\n      return accessCode;\n    } catch (error: any) {\n      // If access code already exists, try again\n      if (error.message.includes('duplicate key')) {\n        return this.createStudent(studentData);\n      }\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,OAEEA,OAAO,CACPC,kBAAkB,KAEb,eAAe,CACtB,OAASC,IAAI,KAAQ,oBAAoB,CAEzC,OAASC,eAAe,KAAQ,mBAAmB,CAEnD,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAa,CAAEC,QAAgB,CAAkB,CAChE,GAAI,CACF;AACA,KAAM,CAAAC,KAAK,CAAG,KAAM,CAAAL,eAAe,CAACM,eAAe,CAACH,KAAK,CAAC,CAE1D,GAAI,CAACE,KAAK,CAAE,CACV,KAAM,IAAI,CAAAE,KAAK,CAAC,4BAA4B,CAAC,CAC/C,CAEA;AACA;AACA,KAAM,CAAAC,eAAe,CAAGJ,QAAQ,GAAK,cAAc,CAAE;AACrD,GAAI,CAACI,eAAe,CAAE,CACpB,KAAM,IAAI,CAAAD,KAAK,CAAC,uBAAuB,CAAC,CAC1C,CAEA,MAAO,CACLE,EAAE,CAAEJ,KAAK,CAACI,EAAE,CACZN,KAAK,CAAEE,KAAK,CAACF,KAAK,CAClBO,IAAI,CAAE,OAAO,CACbC,IAAI,CAAEN,KAAK,CAACM,IAAI,CAChBC,MAAM,CAAEP,KAAK,CAACQ,UAAU,CACxBC,WAAW,CAAET,KAAK,CAACS,WAAW,EAAI,EAAE,CACpCC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACX,KAAK,CAACY,UAAU,CACtC,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAACC,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACA,KAAM,CAAAC,YAAYA,CAACC,UAAkB,CAAoB,CACvD,GAAI,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAzB,eAAe,CAAC0B,sBAAsB,CAACL,UAAU,CAAC,CAE5E,GAAI,CAACI,WAAW,CAAE,CAChB,KAAM,IAAI,CAAAlB,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAEA,GAAI,CAACkB,WAAW,CAACE,SAAS,CAAE,CAC1B,KAAM,IAAI,CAAApB,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAEA;AACA,KAAM,CAAAqB,eAAe,CAAG,EAAAN,qBAAA,CAAAG,WAAW,CAACI,mBAAmB,UAAAP,qBAAA,iBAA/BA,qBAAA,CAAiCQ,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CAC7G,KAAM,CAAAC,gBAAgB,CAAG,EAAAV,sBAAA,CAAAE,WAAW,CAACI,mBAAmB,UAAAN,sBAAA,iBAA/BA,sBAAA,CAAiCW,MAAM,CAAEH,UAAe,EAAKA,UAAU,CAACI,YAAY,CAAC,CAACL,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CACnK,KAAM,CAAAI,YAAY,CAAG,EAAAZ,qBAAA,CAAAC,WAAW,CAACW,YAAY,UAAAZ,qBAAA,iBAAxBA,qBAAA,CAA0BM,GAAG,CAAEO,IAAS,EAAKA,IAAI,CAAC5B,EAAE,CAAC,GAAI,EAAE,CAEhF,MAAO,CACLA,EAAE,CAAEgB,WAAW,CAAChB,EAAE,CAClBN,KAAK,CAAEsB,WAAW,CAACtB,KAAK,EAAI,EAAE,CAC9BO,IAAI,CAAE,SAAS,CACfC,IAAI,CAAEc,WAAW,CAACd,IAAI,EAAI,EAAE,CAC5BC,MAAM,CAAEa,WAAW,CAACZ,UAAU,EAAI,EAAE,CACpCQ,UAAU,CAAEI,WAAW,CAACa,WAAW,CACnCV,eAAe,CACfK,gBAAgB,CAChBG,YAAY,CACZrB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACS,WAAW,CAACR,UAAU,CAC5C,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAACC,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACAoB,kBAAkBA,CAAA,CAAW,CAC3B,MAAO,CAAAC,IAAI,CAACC,KAAK,CAAC,OAAO,CAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACnB,WAInB,CAAmB,CAClB,GAAI,CACF,KAAM,CAAAJ,UAAU,CAAG,IAAI,CAACkB,kBAAkB,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAM,UAAU,CAAG,KAAM,CAAA7C,eAAe,CAAC4C,aAAa,CAAC,CACrDvB,UAAU,CAAEA,UAAU,CACtBV,IAAI,CAAEc,WAAW,CAACd,IAAI,CACtBR,KAAK,CAAEsB,WAAW,CAACtB,KACrB,CAAC,CAAC,CAEF;AACA,GAAIsB,WAAW,CAACG,eAAe,EAAIH,WAAW,CAACG,eAAe,CAACkB,MAAM,CAAG,CAAC,CAAE,CACzE,IAAK,KAAM,CAAAC,QAAQ,GAAI,CAAAtB,WAAW,CAACG,eAAe,CAAE,CAClD,KAAM,CAAA5B,eAAe,CAACgD,aAAa,CAACH,UAAU,CAACpC,EAAE,CAAEsC,QAAQ,CAAC,CAC9D,CACF,CAEA,MAAO,CAAA1B,UAAU,CACnB,CAAE,MAAOH,KAAU,CAAE,CACnB;AACA,GAAIA,KAAK,CAACC,OAAO,CAAC8B,QAAQ,CAAC,eAAe,CAAC,CAAE,CAC3C,MAAO,KAAI,CAACL,aAAa,CAACnB,WAAW,CAAC,CACxC,CACA,KAAM,IAAI,CAAAlB,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAEA;AACA,KAAM,CAAA2C,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAArD,OAAO,CAACE,IAAI,CAAC,CACrB,CAAE,MAAOmB,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACA4C,cAAcA,CAAA,CAAiC,CAC7C,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,WAAW,CAAGxD,kBAAkB,CAACC,IAAI,CAAGwD,IAAI,EAAK,CACrDD,WAAW,CAAC,CAAC,CACbD,OAAO,CAACE,IAAI,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACAC,iBAAiBA,CAACC,QAA6C,CAAE,CAC/D,MAAO,CAAA3D,kBAAkB,CAACC,IAAI,CAAE0D,QAAQ,CAAC,CAC3C,CAEQC,eAAeA,CAACC,SAAiB,CAAU,CACjD,OAAQA,SAAS,EACf,IAAK,qBAAqB,CACxB,MAAO,oBAAoB,CAC7B,IAAK,qBAAqB,CACxB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,4BAA4B,CACrC,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,wBAAwB,CAC3B,MAAO,qCAAqC,CAC9C,QACE,MAAO,yBAAyB,CACpC,CACF,CACF,CAEA,MAAO,MAAM,CAAAC,WAAW,CAAG,GAAI,CAAA3D,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}