{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import{motion}from'framer-motion';import{PlayIcon,DocumentIcon,ClipboardDocumentListIcon,CheckCircleIcon,ArrowLeftIcon,ArrowRightIcon}from'@heroicons/react/24/outline';import{toast}from'react-hot-toast';// Components\n// Services\nimport{supabaseService}from'../../services/supabaseService';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CourseViewer=_ref=>{let{user}=_ref;const{courseId}=useParams();const navigate=useNavigate();const[activeTab,setActiveTab]=useState('videos');const[selectedVideo,setSelectedVideo]=useState(null);const[course,setCourse]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[videoProgress,setVideoProgress]=useState({});useEffect(()=>{if(courseId){loadCourseData();}},[courseId]);const loadCourseData=async()=>{try{setLoading(true);setError(null);// Load course details with videos, quizzes, etc.\nconst courseData=await supabaseService.getCourseById(courseId);setCourse(courseData);// Set first video as selected if available\nif(courseData.videos&&courseData.videos.length>0){setSelectedVideo(courseData.videos[0].id);}}catch(error){console.error('Error loading course:',error);setError('حدث خطأ في تحميل الكورس');toast.error('فشل في تحميل الكورس');}finally{setLoading(false);}};const currentVideo=course.videos.find(v=>v.id===selectedVideo);const handleNextVideo=()=>{const currentIndex=course.videos.findIndex(v=>v.id===selectedVideo);if(currentIndex<course.videos.length-1){setSelectedVideo(course.videos[currentIndex+1].id);}};const handlePrevVideo=()=>{const currentIndex=course.videos.findIndex(v=>v.id===selectedVideo);if(currentIndex>0){setSelectedVideo(course.videos[currentIndex-1].id);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:course.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:course.description})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2 space-y-4\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-video bg-gray-900 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"w-16 h-16 mx-auto mb-4 opacity-50\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:currentVideo===null||currentVideo===void 0?void 0:currentVideo.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-300\",children:[\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648: \",currentVideo===null||currentVideo===void 0?void 0:currentVideo.duration]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handlePrevVideo,disabled:course.videos.findIndex(v=>v.id===selectedVideo)===0,className:\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:[/*#__PURE__*/_jsx(ArrowRightIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleNextVideo,disabled:course.videos.findIndex(v=>v.id===selectedVideo)===course.videos.length-1,className:\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\"}),/*#__PURE__*/_jsx(ArrowLeftIcon,{className:\"w-4 h-4\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",children:\"\\u062A\\u0645 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('videos'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='videos'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('pdfs'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='pdfs'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('quizzes'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='quizzes'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm p-4\",children:[activeTab==='videos'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),course.videos.map((video,index)=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>setSelectedVideo(video.id),className:`p-3 rounded-lg cursor-pointer transition-colors ${selectedVideo===video.id?'bg-blue-50 border border-blue-200':'bg-gray-50 hover:bg-gray-100'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:video.completed?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"}):/*#__PURE__*/_jsx(PlayIcon,{className:\"w-5 h-5 text-gray-400\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:[index+1,\". \",video.title]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:video.duration})]})]})},video.id))]}),activeTab==='pdfs'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0631\\u0627\\u062C\\u0639\"}),course.pdfs.map(pdf=>/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(DocumentIcon,{className:\"w-5 h-5 text-red-600\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:pdf.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:pdf.size})]})]})},pdf.id))]}),activeTab==='quizzes'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"}),course.quizzes.map(quiz=>/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-5 h-5 text-purple-600\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:quiz.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[quiz.questions,\" \\u0633\\u0624\\u0627\\u0644\",quiz.completed&&quiz.score&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600 mr-2\",children:[\"\\u2022 \\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629: \",quiz.score,\"%\"]})]})]}),quiz.completed&&/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"})]})},quiz.id))]})]},activeTab)]})]})]});};export default CourseViewer;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "PlayIcon", "DocumentIcon", "ClipboardDocumentListIcon", "CheckCircleIcon", "ArrowLeftIcon", "ArrowRightIcon", "toast", "supabaseService", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>iewer", "_ref", "user", "courseId", "navigate", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "course", "setCourse", "loading", "setLoading", "error", "setError", "videoProgress", "setVideoProgress", "loadCourseData", "courseData", "getCourseById", "videos", "length", "id", "console", "currentVideo", "find", "v", "handleNextVideo", "currentIndex", "findIndex", "handlePrevVideo", "className", "children", "onBack", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "description", "div", "initial", "opacity", "y", "animate", "duration", "disabled", "map", "video", "index", "completed", "pdfs", "pdf", "size", "quizzes", "quiz", "questions", "score"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/CourseViewer.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon,\n  CheckCircleIcon,\n  ArrowLeftIcon,\n  ArrowRightIcon,\n  BookOpenIcon,\n  AcademicCapIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\nimport { toast } from 'react-hot-toast';\n\n// Components\nimport ResponsiveContainer from '../common/ResponsiveContainer';\nimport ResponsiveCard from '../common/ResponsiveCard';\nimport ResponsiveButton from '../common/ResponsiveButton';\nimport ResponsiveText from '../common/ResponsiveText';\nimport LoadingSpinner from '../common/LoadingSpinner';\n\n// Services\nimport { supabaseService } from '../../services/supabaseService';\n\n// Types\nimport { Student } from '../../types';\n\ninterface CourseViewerProps {\n  user: Student;\n}\n\nconst CourseViewer: React.FC<CourseViewerProps> = ({ user }) => {\n  const { courseId } = useParams<{ courseId: string }>();\n  const navigate = useNavigate();\n\n  const [activeTab, setActiveTab] = useState('videos');\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n  const [course, setCourse] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [videoProgress, setVideoProgress] = useState<{ [key: string]: number }>({});\n\n  useEffect(() => {\n    if (courseId) {\n      loadCourseData();\n    }\n  }, [courseId]);\n\n  const loadCourseData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Load course details with videos, quizzes, etc.\n      const courseData = await supabaseService.getCourseById(courseId!);\n      setCourse(courseData);\n\n      // Set first video as selected if available\n      if (courseData.videos && courseData.videos.length > 0) {\n        setSelectedVideo(courseData.videos[0].id);\n      }\n\n    } catch (error: any) {\n      console.error('Error loading course:', error);\n      setError('حدث خطأ في تحميل الكورس');\n      toast.error('فشل في تحميل الكورس');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const currentVideo = course.videos.find(v => v.id === selectedVideo);\n\n  const handleNextVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex < course.videos.length - 1) {\n      setSelectedVideo(course.videos[currentIndex + 1].id);\n    }\n  };\n\n  const handlePrevVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex > 0) {\n      setSelectedVideo(course.videos[currentIndex - 1].id);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{course.title}</h1>\n          <p className=\"text-gray-600\">{course.description}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Video Player */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm overflow-hidden\"\n          >\n            {/* Video Area */}\n            <div className=\"aspect-video bg-gray-900 flex items-center justify-center\">\n              <div className=\"text-center text-white\">\n                <PlayIcon className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                <h3 className=\"text-xl font-semibold mb-2\">{currentVideo?.title}</h3>\n                <p className=\"text-gray-300\">مدة الفيديو: {currentVideo?.duration}</p>\n              </div>\n            </div>\n\n            {/* Video Controls */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <button\n                    onClick={handlePrevVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === 0}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <ArrowRightIcon className=\"w-4 h-4\" />\n                    <span>السابق</span>\n                  </button>\n                  <button\n                    onClick={handleNextVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === course.videos.length - 1}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <span>التالي</span>\n                    <ArrowLeftIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                  تم المشاهدة\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Course Content Sidebar */}\n        <div className=\"space-y-4\">\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-sm p-4\">\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('videos')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'videos'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الفيديوهات\n              </button>\n              <button\n                onClick={() => setActiveTab('pdfs')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'pdfs'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الملفات\n              </button>\n              <button\n                onClick={() => setActiveTab('quizzes')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'quizzes'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الاختبارات\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm p-4\"\n          >\n            {activeTab === 'videos' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">قائمة الفيديوهات</h3>\n                {course.videos.map((video, index) => (\n                  <div\n                    key={video.id}\n                    onClick={() => setSelectedVideo(video.id)}\n                    className={`p-3 rounded-lg cursor-pointer transition-colors ${\n                      selectedVideo === video.id\n                        ? 'bg-blue-50 border border-blue-200'\n                        : 'bg-gray-50 hover:bg-gray-100'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <div className=\"flex-shrink-0\">\n                        {video.completed ? (\n                          <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                        ) : (\n                          <PlayIcon className=\"w-5 h-5 text-gray-400\" />\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {index + 1}. {video.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{video.duration}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'pdfs' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الملفات والمراجع</h3>\n                {course.pdfs.map((pdf) => (\n                  <div\n                    key={pdf.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <DocumentIcon className=\"w-5 h-5 text-red-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {pdf.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{pdf.size}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'quizzes' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الاختبارات</h3>\n                {course.quizzes.map((quiz) => (\n                  <div\n                    key={quiz.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <ClipboardDocumentListIcon className=\"w-5 h-5 text-purple-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {quiz.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {quiz.questions} سؤال\n                          {quiz.completed && quiz.score && (\n                            <span className=\"text-green-600 mr-2\">• النتيجة: {quiz.score}%</span>\n                          )}\n                        </p>\n                      </div>\n                      {quiz.completed && (\n                        <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CourseViewer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,YAAY,CACZC,yBAAyB,CACzBC,eAAe,CACfC,aAAa,CACbC,cAAc,KAIT,6BAA6B,CACpC,OAASC,KAAK,KAAQ,iBAAiB,CAEvC;AAOA;AACA,OAASC,eAAe,KAAQ,gCAAgC,CAEhE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGlB,SAAS,CAAuB,CAAC,CACtD,KAAM,CAAAmB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,QAAQ,CAAC,CACpD,KAAM,CAACwB,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAAC0B,MAAM,CAAEC,SAAS,CAAC,CAAG3B,QAAQ,CAAM,IAAI,CAAC,CAC/C,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAA4B,CAAC,CAAC,CAAC,CAEjFC,SAAS,CAAC,IAAM,CACd,GAAImB,QAAQ,CAAE,CACZc,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACd,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAc,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACFL,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAI,UAAU,CAAG,KAAM,CAAAvB,eAAe,CAACwB,aAAa,CAAChB,QAAS,CAAC,CACjEO,SAAS,CAACQ,UAAU,CAAC,CAErB;AACA,GAAIA,UAAU,CAACE,MAAM,EAAIF,UAAU,CAACE,MAAM,CAACC,MAAM,CAAG,CAAC,CAAE,CACrDb,gBAAgB,CAACU,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC,CAC3C,CAEF,CAAE,MAAOT,KAAU,CAAE,CACnBU,OAAO,CAACV,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CC,QAAQ,CAAC,yBAAyB,CAAC,CACnCpB,KAAK,CAACmB,KAAK,CAAC,qBAAqB,CAAC,CACpC,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGf,MAAM,CAACW,MAAM,CAACK,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACJ,EAAE,GAAKf,aAAa,CAAC,CAEpE,KAAM,CAAAoB,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,YAAY,CAAGnB,MAAM,CAACW,MAAM,CAACS,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACJ,EAAE,GAAKf,aAAa,CAAC,CACzE,GAAIqB,YAAY,CAAGnB,MAAM,CAACW,MAAM,CAACC,MAAM,CAAG,CAAC,CAAE,CAC3Cb,gBAAgB,CAACC,MAAM,CAACW,MAAM,CAACQ,YAAY,CAAG,CAAC,CAAC,CAACN,EAAE,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAQ,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAF,YAAY,CAAGnB,MAAM,CAACW,MAAM,CAACS,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACJ,EAAE,GAAKf,aAAa,CAAC,CACzE,GAAIqB,YAAY,CAAG,CAAC,CAAE,CACpBpB,gBAAgB,CAACC,MAAM,CAACW,MAAM,CAACQ,YAAY,CAAG,CAAC,CAAC,CAACN,EAAE,CAAC,CACtD,CACF,CAAC,CAED,mBACEvB,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBjC,KAAA,QAAKgC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDC,MAAM,eACLpC,IAAA,WACEqC,OAAO,CAAED,MAAO,CAChBF,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEnC,IAAA,QAAKkC,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5EnC,IAAA,SAAMyC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACD1C,KAAA,QAAAiC,QAAA,eACEnC,IAAA,OAAIkC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEvB,MAAM,CAACiC,KAAK,CAAK,CAAC,cACpE7C,IAAA,MAAGkC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEvB,MAAM,CAACkC,WAAW,CAAI,CAAC,EAClD,CAAC,EACH,CAAC,cAEN5C,KAAA,QAAKgC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDnC,IAAA,QAAKkC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCjC,KAAA,CAACZ,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BhB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAGzDnC,IAAA,QAAKkC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxEjC,KAAA,QAAKgC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCnC,IAAA,CAACT,QAAQ,EAAC2C,SAAS,CAAC,mCAAmC,CAAE,CAAC,cAC1DlC,IAAA,OAAIkC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAER,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEkB,KAAK,CAAK,CAAC,cACrE3C,KAAA,MAAGgC,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,iEAAa,CAACR,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEyB,QAAQ,EAAI,CAAC,EACnE,CAAC,CACH,CAAC,cAGNpD,IAAA,QAAKkC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CjC,KAAA,QAAKgC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjC,KAAA,QAAKgC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjC,KAAA,WACEmC,OAAO,CAAEJ,eAAgB,CACzBoB,QAAQ,CAAEzC,MAAM,CAACW,MAAM,CAACS,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACJ,EAAE,GAAKf,aAAa,CAAC,GAAK,CAAE,CACrEwB,SAAS,CAAC,0KAA0K,CAAAC,QAAA,eAEpLnC,IAAA,CAACJ,cAAc,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,cACtClC,IAAA,SAAAmC,QAAA,CAAM,sCAAM,CAAM,CAAC,EACb,CAAC,cACTjC,KAAA,WACEmC,OAAO,CAAEP,eAAgB,CACzBuB,QAAQ,CAAEzC,MAAM,CAACW,MAAM,CAACS,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACJ,EAAE,GAAKf,aAAa,CAAC,GAAKE,MAAM,CAACW,MAAM,CAACC,MAAM,CAAG,CAAE,CAC5FU,SAAS,CAAC,qLAAqL,CAAAC,QAAA,eAE/LnC,IAAA,SAAAmC,QAAA,CAAM,sCAAM,CAAM,CAAC,cACnBnC,IAAA,CAACL,aAAa,EAACuC,SAAS,CAAC,SAAS,CAAE,CAAC,EAC/B,CAAC,EACN,CAAC,cACNlC,IAAA,WAAQkC,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAC,+DAEtG,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,EACI,CAAC,CACV,CAAC,cAGNjC,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBnC,IAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDjC,KAAA,QAAKgC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CnC,IAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,QAAQ,CAAE,CACtCyB,SAAS,CAAE,yDACT1B,SAAS,GAAK,QAAQ,CAClB,wBAAwB,CACxB,6CAA6C,EAChD,CAAA2B,QAAA,CACJ,8DAED,CAAQ,CAAC,cACTnC,IAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,MAAM,CAAE,CACpCyB,SAAS,CAAE,yDACT1B,SAAS,GAAK,MAAM,CAChB,wBAAwB,CACxB,6CAA6C,EAChD,CAAA2B,QAAA,CACJ,4CAED,CAAQ,CAAC,cACTnC,IAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,SAAS,CAAE,CACvCyB,SAAS,CAAE,yDACT1B,SAAS,GAAK,SAAS,CACnB,wBAAwB,CACxB,6CAA6C,EAChD,CAAA2B,QAAA,CACJ,8DAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGNjC,KAAA,CAACZ,MAAM,CAACyD,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BhB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAE5C3B,SAAS,GAAK,QAAQ,eACrBN,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnC,IAAA,OAAIkC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,CACrEvB,MAAM,CAACW,MAAM,CAAC+B,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBAC9BxD,IAAA,QAEEqC,OAAO,CAAEA,CAAA,GAAM1B,gBAAgB,CAAC4C,KAAK,CAAC9B,EAAE,CAAE,CAC1CS,SAAS,CAAE,mDACTxB,aAAa,GAAK6C,KAAK,CAAC9B,EAAE,CACtB,mCAAmC,CACnC,8BAA8B,EACjC,CAAAU,QAAA,cAEHjC,KAAA,QAAKgC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnC,IAAA,QAAKkC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BoB,KAAK,CAACE,SAAS,cACdzD,IAAA,CAACN,eAAe,EAACwC,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAEtDlC,IAAA,CAACT,QAAQ,EAAC2C,SAAS,CAAC,uBAAuB,CAAE,CAC9C,CACE,CAAC,cACNhC,KAAA,QAAKgC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjC,KAAA,MAAGgC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,EACtDqB,KAAK,CAAG,CAAC,CAAC,IAAE,CAACD,KAAK,CAACV,KAAK,EACxB,CAAC,cACJ7C,IAAA,MAAGkC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEoB,KAAK,CAACH,QAAQ,CAAI,CAAC,EACtD,CAAC,EACH,CAAC,EAtBDG,KAAK,CAAC9B,EAuBR,CACN,CAAC,EACC,CACN,CAEAjB,SAAS,GAAK,MAAM,eACnBN,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnC,IAAA,OAAIkC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,CACrEvB,MAAM,CAAC8C,IAAI,CAACJ,GAAG,CAAEK,GAAG,eACnB3D,IAAA,QAEEkC,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAExFjC,KAAA,QAAKgC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnC,IAAA,CAACR,YAAY,EAAC0C,SAAS,CAAC,sBAAsB,CAAE,CAAC,cACjDhC,KAAA,QAAKgC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnC,IAAA,MAAGkC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtDwB,GAAG,CAACd,KAAK,CACT,CAAC,cACJ7C,IAAA,MAAGkC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEwB,GAAG,CAACC,IAAI,CAAI,CAAC,EAChD,CAAC,EACH,CAAC,EAXDD,GAAG,CAAClC,EAYN,CACN,CAAC,EACC,CACN,CAEAjB,SAAS,GAAK,SAAS,eACtBN,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnC,IAAA,OAAIkC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,8DAAU,CAAI,CAAC,CAC/DvB,MAAM,CAACiD,OAAO,CAACP,GAAG,CAAEQ,IAAI,eACvB9D,IAAA,QAEEkC,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAExFjC,KAAA,QAAKgC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnC,IAAA,CAACP,yBAAyB,EAACyC,SAAS,CAAC,yBAAyB,CAAE,CAAC,cACjEhC,KAAA,QAAKgC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnC,IAAA,MAAGkC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtD2B,IAAI,CAACjB,KAAK,CACV,CAAC,cACJ3C,KAAA,MAAGgC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACjC2B,IAAI,CAACC,SAAS,CAAC,2BAChB,CAACD,IAAI,CAACL,SAAS,EAAIK,IAAI,CAACE,KAAK,eAC3B9D,KAAA,SAAMgC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAC,qDAAW,CAAC2B,IAAI,CAACE,KAAK,CAAC,GAAC,EAAM,CACrE,EACA,CAAC,EACD,CAAC,CACLF,IAAI,CAACL,SAAS,eACbzD,IAAA,CAACN,eAAe,EAACwC,SAAS,CAAC,wBAAwB,CAAE,CACtD,EACE,CAAC,EAnBD4B,IAAI,CAACrC,EAoBP,CACN,CAAC,EACC,CACN,GAxFIjB,SAyFK,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}