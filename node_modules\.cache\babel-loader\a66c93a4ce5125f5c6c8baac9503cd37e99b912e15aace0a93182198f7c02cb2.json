{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{CogIcon,BellIcon,ShieldCheckIcon,GlobeAltIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SettingsPage=_ref=>{let{onBack}=_ref;const[settings,setSettings]=useState({siteName:'منصة ALaa Abd Hamied',siteDescription:'منصة تعليمية متقدمة - تطوير فريق ALaa Abd Elhamied 2025',allowRegistration:true,requireEmailVerification:true,enableNotifications:true,defaultLanguage:'ar'});const handleSettingChange=(key,value)=>{setSettings(prev=>({...prev,[key]:value}));};const handleSaveSettings=()=>{// TODO: Implement save settings functionality\nconsole.log('Save settings:',settings);};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629 \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse mb-6\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"w-6 h-6 text-gray-600\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:settings.siteName,onChange:e=>handleSettingChange('siteName',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\"}),/*#__PURE__*/_jsx(\"textarea\",{value:settings.siteDescription,onChange:e=>handleSettingChange('siteDescription',e.target.value),rows:3,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0627\\u0641\\u062A\\u0631\\u0627\\u0636\\u064A\\u0629\"}),/*#__PURE__*/_jsxs(\"select\",{value:settings.defaultLanguage,onChange:e=>handleSettingChange('defaultLanguage',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"ar\",children:\"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"option\",{value:\"en\",children:\"English\"})]})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse mb-6\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-6 h-6 text-gray-600\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u0627\\u0644\\u0633\\u0645\\u0627\\u062D \\u0628\\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0633\\u0645\\u0627\\u062D \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646 \\u0627\\u0644\\u062C\\u062F\\u062F \\u0628\\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"relative inline-flex items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.allowRegistration,onChange:e=>handleSettingChange('allowRegistration',e.target.checked),className:\"sr-only peer\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0637\\u0644\\u0628 \\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \\u0639\\u0646\\u062F \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"relative inline-flex items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.requireEmailVerification,onChange:e=>handleSettingChange('requireEmailVerification',e.target.checked),className:\"sr-only peer\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"})]})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse mb-6\",children:[/*#__PURE__*/_jsx(BellIcon,{className:\"w-6 h-6 text-gray-600\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u0631\\u0633\\u0627\\u0644 \\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"relative inline-flex items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.enableNotifications,onChange:e=>handleSettingChange('enableNotifications',e.target.checked),className:\"sr-only peer\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"})]})]})})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.4},className:\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(GlobeAltIcon,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u062D\\u0648\\u0644 \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4 text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-2\",children:\"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629: \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied \\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631: 2025.1.0\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0637\\u0644\\u0627\\u0642: \\u064A\\u0646\\u0627\\u064A\\u0631 2025\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-2\",children:\"\\u0641\\u0631\\u064A\\u0642 \\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u0627\\u0644\\u0645\\u0637\\u0648\\u0631: \\u0641\\u0631\\u064A\\u0642 ALaa Abd Elhamied 2025\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u0627\\u0644\\u062A\\u062E\\u0635\\u0635: \\u0627\\u0644\\u062D\\u0644\\u0648\\u0644 \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"\\u0627\\u0644\\u062A\\u0642\\u0646\\u064A\\u0627\\u062A: React, TypeScript, Supabase\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"border-t pt-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-center text-gray-500\",children:\"\\xA9 2025 \\u0641\\u0631\\u064A\\u0642 ALaa Abd Elhamied. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end\",children:/*#__PURE__*/_jsx(\"button\",{onClick:handleSaveSettings,className:\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:\"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"})})]})]});};export default SettingsPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "CogIcon", "BellIcon", "ShieldCheckIcon", "GlobeAltIcon", "jsx", "_jsx", "jsxs", "_jsxs", "SettingsPage", "_ref", "onBack", "settings", "setSettings", "siteName", "siteDescription", "allowRegistration", "requireEmailVerification", "enableNotifications", "defaultLanguage", "handleSettingChange", "key", "value", "prev", "handleSaveSettings", "console", "log", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "type", "onChange", "e", "target", "rows", "transition", "delay", "checked"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/SettingsPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  CogIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon\n} from '@heroicons/react/24/outline';\n\ninterface SettingsPageProps {\n  onBack?: () => void;\n}\n\nconst SettingsPage: React.FC<SettingsPageProps> = ({ onBack }) => {\n  const [settings, setSettings] = useState({\n    siteName: 'منصة ALaa Abd Hamied',\n    siteDescription: 'منصة تعليمية متقدمة - تطوير فريق ALaa Abd Elhamied 2025',\n    allowRegistration: true,\n    requireEmailVerification: true,\n    enableNotifications: true,\n    defaultLanguage: 'ar'\n  });\n\n  const handleSettingChange = (key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleSaveSettings = () => {\n    // TODO: Implement save settings functionality\n    console.log('Save settings:', settings);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إعدادات النظام</h1>\n          <p className=\"text-gray-600\">إدارة إعدادات المنصة العامة</p>\n        </div>\n      </div>\n\n      {/* Settings Sections */}\n      <div className=\"space-y-6\">\n        {/* General Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <CogIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">الإعدادات العامة</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                اسم الموقع\n              </label>\n              <input\n                type=\"text\"\n                value={settings.siteName}\n                onChange={(e) => handleSettingChange('siteName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                وصف الموقع\n              </label>\n              <textarea\n                value={settings.siteDescription}\n                onChange={(e) => handleSettingChange('siteDescription', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                اللغة الافتراضية\n              </label>\n              <select\n                value={settings.defaultLanguage}\n                onChange={(e) => handleSettingChange('defaultLanguage', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"ar\">العربية</option>\n                <option value=\"en\">English</option>\n              </select>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* User Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <ShieldCheckIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">إعدادات المستخدمين</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">السماح بالتسجيل</h3>\n                <p className=\"text-sm text-gray-600\">السماح للمستخدمين الجدد بالتسجيل</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.allowRegistration}\n                  onChange={(e) => handleSettingChange('allowRegistration', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">تأكيد البريد الإلكتروني</h3>\n                <p className=\"text-sm text-gray-600\">طلب تأكيد البريد الإلكتروني عند التسجيل</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.requireEmailVerification}\n                  onChange={(e) => handleSettingChange('requireEmailVerification', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Notification Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <BellIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">إعدادات الإشعارات</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">تفعيل الإشعارات</h3>\n                <p className=\"text-sm text-gray-600\">إرسال إشعارات للمستخدمين</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.enableNotifications}\n                  onChange={(e) => handleSettingChange('enableNotifications', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* About Platform */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-4\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <GlobeAltIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">حول المنصة</h3>\n          </div>\n\n          <div className=\"space-y-4 text-gray-600\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">معلومات المنصة</h4>\n                <p className=\"text-sm\">اسم المنصة: منصة ALaa Abd Hamied للكورسات</p>\n                <p className=\"text-sm\">الإصدار: 2025.1.0</p>\n                <p className=\"text-sm\">تاريخ الإطلاق: يناير 2025</p>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">فريق التطوير</h4>\n                <p className=\"text-sm\">المطور: فريق ALaa Abd Elhamied 2025</p>\n                <p className=\"text-sm\">التخصص: الحلول التعليمية الرقمية</p>\n                <p className=\"text-sm\">التقنيات: React, TypeScript, Supabase</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-4\">\n              <p className=\"text-sm text-center text-gray-500\">\n                © 2025 فريق ALaa Abd Elhamied. جميع الحقوق محفوظة.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Save Button */}\n        <div className=\"flex justify-end\">\n          <button\n            onClick={handleSaveSettings}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            حفظ الإعدادات\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,OAAO,CACPC,QAAQ,CACRC,eAAe,CACfC,YAAY,KACP,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMrC,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CAC3D,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,CACvCe,QAAQ,CAAE,sBAAsB,CAChCC,eAAe,CAAE,yDAAyD,CAC1EC,iBAAiB,CAAE,IAAI,CACvBC,wBAAwB,CAAE,IAAI,CAC9BC,mBAAmB,CAAE,IAAI,CACzBC,eAAe,CAAE,IACnB,CAAC,CAAC,CAEF,KAAM,CAAAC,mBAAmB,CAAGA,CAACC,GAAW,CAAEC,KAAU,GAAK,CACvDT,WAAW,CAACU,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACF,GAAG,EAAGC,KACT,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEd,QAAQ,CAAC,CACzC,CAAC,CAED,mBACEJ,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBpB,KAAA,QAAKmB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDjB,MAAM,eACLL,IAAA,WACEuB,OAAO,CAAElB,MAAO,CAChBgB,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEtB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EtB,IAAA,SAAM2B,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACD5B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cACpEtB,IAAA,MAAGqB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,qJAA2B,CAAG,CAAC,EACzD,CAAC,EACH,CAAC,cAGNpB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBpB,KAAA,CAACR,MAAM,CAACqC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEpB,KAAA,QAAKmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DtB,IAAA,CAACL,OAAO,EAAC0B,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC7CrB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,EACtE,CAAC,cAENpB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yDAEhE,CAAO,CAAC,cACRtB,IAAA,UACEoC,IAAI,CAAC,MAAM,CACXpB,KAAK,CAAEV,QAAQ,CAACE,QAAS,CACzB6B,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,UAAU,CAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE,CACjEK,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cAENnB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yDAEhE,CAAO,CAAC,cACRtB,IAAA,aACEgB,KAAK,CAAEV,QAAQ,CAACG,eAAgB,CAChC4B,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,iBAAiB,CAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE,CACxEwB,IAAI,CAAE,CAAE,CACRnB,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cAENnB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6FAEhE,CAAO,CAAC,cACRpB,KAAA,WACEc,KAAK,CAAEV,QAAQ,CAACO,eAAgB,CAChCwB,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,iBAAiB,CAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE,CACxEK,SAAS,CAAC,wGAAwG,CAAAC,QAAA,eAElHtB,IAAA,WAAQgB,KAAK,CAAC,IAAI,CAAAM,QAAA,CAAC,4CAAO,CAAQ,CAAC,cACnCtB,IAAA,WAAQgB,KAAK,CAAC,IAAI,CAAAM,QAAA,CAAC,SAAO,CAAQ,CAAC,EAC7B,CAAC,EACN,CAAC,EACH,CAAC,EACI,CAAC,cAGbpB,KAAA,CAACR,MAAM,CAACqC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BO,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BrB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEpB,KAAA,QAAKmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DtB,IAAA,CAACH,eAAe,EAACwB,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACrDrB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,yGAAkB,CAAI,CAAC,EACxE,CAAC,cAENpB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,KAAA,QAAKmB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uFAAe,CAAI,CAAC,cACtEtB,IAAA,MAAGqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mLAAgC,CAAG,CAAC,EACtE,CAAC,cACNpB,KAAA,UAAOmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eACjEtB,IAAA,UACEoC,IAAI,CAAC,UAAU,CACfO,OAAO,CAAErC,QAAQ,CAACI,iBAAkB,CACpC2B,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,mBAAmB,CAAEwB,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE,CAC5EtB,SAAS,CAAC,cAAc,CACzB,CAAC,cACFrB,IAAA,QAAKqB,SAAS,CAAC,yXAAyX,CAAM,CAAC,EAC1Y,CAAC,EACL,CAAC,cAENnB,KAAA,QAAKmB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,kIAAuB,CAAI,CAAC,cAC9EtB,IAAA,MAAGqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mNAAuC,CAAG,CAAC,EAC7E,CAAC,cACNpB,KAAA,UAAOmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eACjEtB,IAAA,UACEoC,IAAI,CAAC,UAAU,CACfO,OAAO,CAAErC,QAAQ,CAACK,wBAAyB,CAC3C0B,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,0BAA0B,CAAEwB,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE,CACnFtB,SAAS,CAAC,cAAc,CACzB,CAAC,cACFrB,IAAA,QAAKqB,SAAS,CAAC,yXAAyX,CAAM,CAAC,EAC1Y,CAAC,EACL,CAAC,EACH,CAAC,EACI,CAAC,cAGbnB,KAAA,CAACR,MAAM,CAACqC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BO,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BrB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEpB,KAAA,QAAKmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DtB,IAAA,CAACJ,QAAQ,EAACyB,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC9CrB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,mGAAiB,CAAI,CAAC,EACvE,CAAC,cAENtB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpB,KAAA,QAAKmB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uFAAe,CAAI,CAAC,cACtEtB,IAAA,MAAGqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,wIAAwB,CAAG,CAAC,EAC9D,CAAC,cACNpB,KAAA,UAAOmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eACjEtB,IAAA,UACEoC,IAAI,CAAC,UAAU,CACfO,OAAO,CAAErC,QAAQ,CAACM,mBAAoB,CACtCyB,QAAQ,CAAGC,CAAC,EAAKxB,mBAAmB,CAAC,qBAAqB,CAAEwB,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE,CAC9EtB,SAAS,CAAC,cAAc,CACzB,CAAC,cACFrB,IAAA,QAAKqB,SAAS,CAAC,yXAAyX,CAAM,CAAC,EAC1Y,CAAC,EACL,CAAC,CACH,CAAC,EACI,CAAC,cAGbnB,KAAA,CAACR,MAAM,CAACqC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BO,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BrB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEpB,KAAA,QAAKmB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DtB,IAAA,QAAKqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCtB,IAAA,CAACF,YAAY,EAACuB,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC/C,CAAC,cACNrB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,yDAAU,CAAI,CAAC,EAChE,CAAC,cAENpB,KAAA,QAAKmB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCpB,KAAA,QAAKmB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cAClEtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,oJAAyC,CAAG,CAAC,cACpEtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,sDAAiB,CAAG,CAAC,cAC5CtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,gHAAyB,CAAG,CAAC,EACjD,CAAC,cACNpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,cAChEtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,uFAAmC,CAAG,CAAC,cAC9DtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,8KAAgC,CAAG,CAAC,cAC3DtB,IAAA,MAAGqB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,+EAAqC,CAAG,CAAC,EAC7D,CAAC,EACH,CAAC,cAENtB,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtB,IAAA,MAAGqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,2JAEjD,CAAG,CAAC,CACD,CAAC,EACH,CAAC,EACI,CAAC,cAGbtB,IAAA,QAAKqB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BtB,IAAA,WACEuB,OAAO,CAAEL,kBAAmB,CAC5BG,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,2EAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}