{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return import('@supabase/node-fetch').then(_ref => {\n        let {\n          default: fetch\n        } = _ref;\n        return fetch(...args);\n      });\n    };\n  } else {\n    _fetch = fetch;\n  }\n  return function () {\n    return _fetch(...arguments);\n  };\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (yield import('@supabase/node-fetch')).Response;\n  }\n  return Response;\n});\nexport const recursiveToCamel = item => {\n  if (Array.isArray(item)) {\n    return item.map(el => recursiveToCamel(el));\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item;\n  }\n  const result = {};\n  Object.entries(item).forEach(_ref2 => {\n    let [key, value] = _ref2;\n    const newKey = key.replace(/([-_][a-z])/gi, c => c.toUpperCase().replace(/[-_]/g, ''));\n    result[newKey] = recursiveToCamel(value);\n  });\n  return result;\n};", "map": {"version": 3, "names": ["resolveFetch", "customFetch", "_fetch", "fetch", "_len", "arguments", "length", "args", "Array", "_key", "then", "_ref", "default", "resolveResponse", "__awaiter", "Response", "recursiveToCamel", "item", "isArray", "map", "el", "Object", "result", "entries", "for<PERSON>ach", "_ref2", "key", "value", "new<PERSON>ey", "replace", "c", "toUpperCase"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\storage-js\\src\\lib\\helpers.ts"], "sourcesContent": ["type Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const resolveResponse = async (): Promise<typeof Response> => {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (await import('@supabase/node-fetch' as any)).Response\n  }\n\n  return Response\n}\n\nexport const recursiveToCamel = (item: Record<string, any>): unknown => {\n  if (Array.isArray(item)) {\n    return item.map((el) => recursiveToCamel(el))\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item\n  }\n\n  const result: Record<string, any> = {}\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''))\n    result[newKey] = recursiveToCamel(value)\n  })\n\n  return result\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,OAAO,MAAMA,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAG,SAAAA,CAAA;MAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAA,OACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAACC,IAAA;QAAA,IAAC;UAAEC,OAAO,EAAET;QAAK,CAAE,GAAAQ,IAAA;QAAA,OAAKR,KAAK,CAAC,GAAGI,IAAI,CAAC;MAAA,EAAC;IAAA;GACrF,MAAM;IACLL,MAAM,GAAGC,KAAK;;EAEhB,OAAO;IAAA,OAAaD,MAAM,CAAC,GAAAG,SAAO,CAAC;EAAA;AACrC,CAAC;AAED,OAAO,MAAMQ,eAAe,GAAGA,CAAA,KAAqCC,SAAA;EAClE,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC;IACA,OAAO,CAAC,MAAM,MAAM,CAAC,sBAA6B,CAAC,EAAEA,QAAQ;;EAG/D,OAAOA,QAAQ;AACjB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIC,IAAyB,IAAa;EACrE,IAAIT,KAAK,CAACU,OAAO,CAACD,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACE,GAAG,CAAEC,EAAE,IAAKJ,gBAAgB,CAACI,EAAE,CAAC,CAAC;GAC9C,MAAM,IAAI,OAAOH,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAKI,MAAM,CAACJ,IAAI,CAAC,EAAE;IAC9D,OAAOA,IAAI;;EAGb,MAAMK,MAAM,GAAwB,EAAE;EACtCD,MAAM,CAACE,OAAO,CAACN,IAAI,CAAC,CAACO,OAAO,CAACC,KAAA,IAAiB;IAAA,IAAhB,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAAF,KAAA;IACxC,MAAMG,MAAM,GAAGF,GAAG,CAACG,OAAO,CAAC,eAAe,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,EAAE,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACxFP,MAAM,CAACM,MAAM,CAAC,GAAGZ,gBAAgB,CAACW,KAAK,CAAC;EAC1C,CAAC,CAAC;EAEF,OAAOL,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}