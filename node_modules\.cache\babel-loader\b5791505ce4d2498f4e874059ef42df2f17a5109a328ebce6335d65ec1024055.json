{"ast": null, "code": "import{db}from'../config/firebase';import{collection,doc,addDoc,updateDoc,deleteDoc}from'firebase/firestore';import{mockCourses,mockQuizzes,mockCertificates}from'../data/mockCourses';import{mockStudents}from'../data/mockStudents';// Mock categories data\nconst mockCategories=[{id:'programming',name:'البرمجة',description:'كورسات البرمجة وتطوير البرمجيات',color:'blue',isActive:true,createdAt:new Date('2024-01-01')},{id:'web',name:'تطوير الويب',description:'كورسات تطوير المواقع والتطبيقات',color:'green',isActive:true,createdAt:new Date('2024-01-01')},{id:'ai',name:'الذكاء الاصطناعي',description:'كورسات الذكاء الاصطناعي وتعلم الآلة',color:'purple',isActive:true,createdAt:new Date('2024-01-01')}];class DataService{// Courses\nasync getCourses(){try{// Return mock data for now\nreturn mockCourses;}catch(error){console.error('Error fetching courses:',error);return mockCourses;// Fallback to mock data\n}}async getCourse(id){try{const course=mockCourses.find(c=>c.id===id);return course||null;}catch(error){console.error('Error fetching course:',error);return null;}}async addCourse(course){try{const docRef=await addDoc(collection(db,'courses'),course);return docRef.id;}catch(error){console.error('Error adding course:',error);throw error;}}async updateCourse(id,course){try{await updateDoc(doc(db,'courses',id),course);}catch(error){console.error('Error updating course:',error);throw error;}}async deleteCourse(id){try{await deleteDoc(doc(db,'courses',id));}catch(error){console.error('Error deleting course:',error);throw error;}}// Students\nasync getStudents(){try{return mockStudents;}catch(error){console.error('Error fetching students:',error);return mockStudents;}}async getStudent(id){try{const student=mockStudents.find(s=>s.id===id);return student||null;}catch(error){console.error('Error fetching student:',error);return null;}}// Quizzes\nasync getQuizzes(){try{return mockQuizzes;}catch(error){console.error('Error fetching quizzes:',error);return mockQuizzes;}}async getQuiz(id){try{const quiz=mockQuizzes.find(q=>q.id===id);return quiz||null;}catch(error){console.error('Error fetching quiz:',error);return null;}}// Certificates\nasync getCertificates(){try{return mockCertificates;}catch(error){console.error('Error fetching certificates:',error);return mockCertificates;}}async getStudentCertificates(studentId){try{return mockCertificates.filter(cert=>cert.studentId===studentId);}catch(error){console.error('Error fetching student certificates:',error);return[];}}// Categories\nasync getCategories(){try{return mockCategories;}catch(error){console.error('Error fetching categories:',error);return mockCategories;}}async getCategory(id){try{const category=mockCategories.find(c=>c.id===id);return category||null;}catch(error){console.error('Error fetching category:',error);return null;}}async createCategory(category){try{const docRef=await addDoc(collection(db,'categories'),category);return docRef.id;}catch(error){console.error('Error adding category:',error);throw error;}}async updateCategory(id,category){try{await updateDoc(doc(db,'categories',id),category);}catch(error){console.error('Error updating category:',error);throw error;}}async deleteCategory(id){try{await deleteDoc(doc(db,'categories',id));}catch(error){console.error('Error deleting category:',error);throw error;}}// Analytics\nasync getAnalytics(){return{totalStudents:mockStudents.length,totalCourses:mockCourses.length,totalQuizzes:mockQuizzes.length,totalCertificates:mockCertificates.length,revenue:mockCourses.length*299,// Mock revenue calculation\nenrollments:mockStudents.reduce((sum,student)=>sum+student.enrolledCourses.length,0)};}}export const dataService=new DataService();", "map": {"version": 3, "names": ["db", "collection", "doc", "addDoc", "updateDoc", "deleteDoc", "mockCourses", "mockQuizzes", "mockCertificates", "mockStudents", "mockCategories", "id", "name", "description", "color", "isActive", "createdAt", "Date", "DataService", "getCourses", "error", "console", "getCourse", "course", "find", "c", "addCourse", "doc<PERSON>ef", "updateCourse", "deleteCourse", "getStudents", "getStudent", "student", "s", "getQuizzes", "getQuiz", "quiz", "q", "getCertificates", "getStudentCertificates", "studentId", "filter", "cert", "getCategories", "getCategory", "category", "createCategory", "updateCategory", "deleteCategory", "getAnalytics", "totalStudents", "length", "totalCourses", "totalQuizzes", "totalCertificates", "revenue", "enrollments", "reduce", "sum", "enrolledCourses", "dataService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/dataService.ts"], "sourcesContent": ["import { db } from '../config/firebase';\nimport { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { Course, Student, Quiz, Certificate, Category } from '../types';\nimport { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\n\n// Mock categories data\nconst mockCategories: Category[] = [\n  {\n    id: 'programming',\n    name: 'البرمجة',\n    description: 'كورسات البرمجة وتطوير البرمجيات',\n    color: 'blue',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: 'web',\n    name: 'تطوير الويب',\n    description: 'كورسات تطوير المواقع والتطبيقات',\n    color: 'green',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: 'ai',\n    name: 'الذكاء الاصطناعي',\n    description: 'كورسات الذكاء الاصطناعي وتعلم الآلة',\n    color: 'purple',\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nclass DataService {\n  // Courses\n  async getCourses(): Promise<Course[]> {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n\n  async getCourse(id: string): Promise<Course | null> {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n\n  async addCourse(course: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n\n  async updateCourse(id: string, course: Partial<Course>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n\n  async deleteCourse(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents(): Promise<Student[]> {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n\n  async getStudent(id: string): Promise<Student | null> {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes(): Promise<Quiz[]> {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n\n  async getQuiz(id: string): Promise<Quiz | null> {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates(): Promise<Certificate[]> {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n\n  async getStudentCertificates(studentId: string): Promise<Certificate[]> {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Categories\n  async getCategories(): Promise<Category[]> {\n    try {\n      return mockCategories;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      return mockCategories;\n    }\n  }\n\n  async getCategory(id: string): Promise<Category | null> {\n    try {\n      const category = mockCategories.find(c => c.id === id);\n      return category || null;\n    } catch (error) {\n      console.error('Error fetching category:', error);\n      return null;\n    }\n  }\n\n  async createCategory(category: Omit<Category, 'id' | 'createdAt'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'categories'), category);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding category:', error);\n      throw error;\n    }\n  }\n\n  async updateCategory(id: string, category: Partial<Category>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'categories', id), category);\n    } catch (error) {\n      console.error('Error updating category:', error);\n      throw error;\n    }\n  }\n\n  async deleteCategory(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'categories', id));\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      throw error;\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.length * 299, // Mock revenue calculation\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\n\nexport const dataService = new DataService();\n"], "mappings": "AAAA,OAASA,EAAE,KAAQ,oBAAoB,CACvC,OAASC,UAAU,CAAWC,GAAG,CAAUC,MAAM,CAAEC,SAAS,CAAEC,SAAS,KAAQ,oBAAoB,CAEnG,OAASC,WAAW,CAAcC,WAAW,CAAEC,gBAAgB,KAAQ,qBAAqB,CAC5F,OAASC,YAAY,KAAQ,sBAAsB,CAEnD;AACA,KAAM,CAAAC,cAA0B,CAAG,CACjC,CACEC,EAAE,CAAE,aAAa,CACjBC,IAAI,CAAE,SAAS,CACfC,WAAW,CAAE,iCAAiC,CAC9CC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEN,EAAE,CAAE,KAAK,CACTC,IAAI,CAAE,aAAa,CACnBC,WAAW,CAAE,iCAAiC,CAC9CC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEN,EAAE,CAAE,IAAI,CACRC,IAAI,CAAE,kBAAkB,CACxBC,WAAW,CAAE,qCAAqC,CAClDC,KAAK,CAAE,QAAQ,CACfC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAAA,CAAsB,CACpC,GAAI,CACF;AACA,MAAO,CAAAb,WAAW,CACpB,CAAE,MAAOc,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAAd,WAAW,CAAE;AACtB,CACF,CAEA,KAAM,CAAAgB,SAASA,CAACX,EAAU,CAA0B,CAClD,GAAI,CACF,KAAM,CAAAY,MAAM,CAAGjB,WAAW,CAACkB,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACd,EAAE,GAAKA,EAAE,CAAC,CACjD,MAAO,CAAAY,MAAM,EAAI,IAAI,CACvB,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,KAAI,CACb,CACF,CAEA,KAAM,CAAAM,SAASA,CAACH,MAA0B,CAAmB,CAC3D,GAAI,CACF,KAAM,CAAAI,MAAM,CAAG,KAAM,CAAAxB,MAAM,CAACF,UAAU,CAACD,EAAE,CAAE,SAAS,CAAC,CAAEuB,MAAM,CAAC,CAC9D,MAAO,CAAAI,MAAM,CAAChB,EAAE,CAClB,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAQ,YAAYA,CAACjB,EAAU,CAAEY,MAAuB,CAAiB,CACrE,GAAI,CACF,KAAM,CAAAnB,SAAS,CAACF,GAAG,CAACF,EAAE,CAAE,SAAS,CAAEW,EAAE,CAAC,CAAEY,MAAM,CAAC,CACjD,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAS,YAAYA,CAAClB,EAAU,CAAiB,CAC5C,GAAI,CACF,KAAM,CAAAN,SAAS,CAACH,GAAG,CAACF,EAAE,CAAE,SAAS,CAAEW,EAAE,CAAC,CAAC,CACzC,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAU,WAAWA,CAAA,CAAuB,CACtC,GAAI,CACF,MAAO,CAAArB,YAAY,CACrB,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,CAAAX,YAAY,CACrB,CACF,CAEA,KAAM,CAAAsB,UAAUA,CAACpB,EAAU,CAA2B,CACpD,GAAI,CACF,KAAM,CAAAqB,OAAO,CAAGvB,YAAY,CAACe,IAAI,CAACS,CAAC,EAAIA,CAAC,CAACtB,EAAE,GAAKA,EAAE,CAAC,CACnD,MAAO,CAAAqB,OAAO,EAAI,IAAI,CACxB,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAc,UAAUA,CAAA,CAAoB,CAClC,GAAI,CACF,MAAO,CAAA3B,WAAW,CACpB,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAAb,WAAW,CACpB,CACF,CAEA,KAAM,CAAA4B,OAAOA,CAACxB,EAAU,CAAwB,CAC9C,GAAI,CACF,KAAM,CAAAyB,IAAI,CAAG7B,WAAW,CAACiB,IAAI,CAACa,CAAC,EAAIA,CAAC,CAAC1B,EAAE,GAAKA,EAAE,CAAC,CAC/C,MAAO,CAAAyB,IAAI,EAAI,IAAI,CACrB,CAAE,MAAOhB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAkB,eAAeA,CAAA,CAA2B,CAC9C,GAAI,CACF,MAAO,CAAA9B,gBAAgB,CACzB,CAAE,MAAOY,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,CAAAZ,gBAAgB,CACzB,CACF,CAEA,KAAM,CAAA+B,sBAAsBA,CAACC,SAAiB,CAA0B,CACtE,GAAI,CACF,MAAO,CAAAhC,gBAAgB,CAACiC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACF,SAAS,GAAKA,SAAS,CAAC,CACtE,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,MAAO,EAAE,CACX,CACF,CAEA;AACA,KAAM,CAAAuB,aAAaA,CAAA,CAAwB,CACzC,GAAI,CACF,MAAO,CAAAjC,cAAc,CACvB,CAAE,MAAOU,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CAAAV,cAAc,CACvB,CACF,CAEA,KAAM,CAAAkC,WAAWA,CAACjC,EAAU,CAA4B,CACtD,GAAI,CACF,KAAM,CAAAkC,QAAQ,CAAGnC,cAAc,CAACc,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACd,EAAE,GAAKA,EAAE,CAAC,CACtD,MAAO,CAAAkC,QAAQ,EAAI,IAAI,CACzB,CAAE,MAAOzB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,KAAI,CACb,CACF,CAEA,KAAM,CAAA0B,cAAcA,CAACD,QAA4C,CAAmB,CAClF,GAAI,CACF,KAAM,CAAAlB,MAAM,CAAG,KAAM,CAAAxB,MAAM,CAACF,UAAU,CAACD,EAAE,CAAE,YAAY,CAAC,CAAE6C,QAAQ,CAAC,CACnE,MAAO,CAAAlB,MAAM,CAAChB,EAAE,CAClB,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAA2B,cAAcA,CAACpC,EAAU,CAAEkC,QAA2B,CAAiB,CAC3E,GAAI,CACF,KAAM,CAAAzC,SAAS,CAACF,GAAG,CAACF,EAAE,CAAE,YAAY,CAAEW,EAAE,CAAC,CAAEkC,QAAQ,CAAC,CACtD,CAAE,MAAOzB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAA4B,cAAcA,CAACrC,EAAU,CAAiB,CAC9C,GAAI,CACF,KAAM,CAAAN,SAAS,CAACH,GAAG,CAACF,EAAE,CAAE,YAAY,CAAEW,EAAE,CAAC,CAAC,CAC5C,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAA6B,YAAYA,CAAA,CAAG,CACnB,MAAO,CACLC,aAAa,CAAEzC,YAAY,CAAC0C,MAAM,CAClCC,YAAY,CAAE9C,WAAW,CAAC6C,MAAM,CAChCE,YAAY,CAAE9C,WAAW,CAAC4C,MAAM,CAChCG,iBAAiB,CAAE9C,gBAAgB,CAAC2C,MAAM,CAC1CI,OAAO,CAAEjD,WAAW,CAAC6C,MAAM,CAAG,GAAG,CAAE;AACnCK,WAAW,CAAE/C,YAAY,CAACgD,MAAM,CAAC,CAACC,GAAG,CAAE1B,OAAO,GAAK0B,GAAG,CAAG1B,OAAO,CAAC2B,eAAe,CAACR,MAAM,CAAE,CAAC,CAC5F,CAAC,CACH,CACF,CAEA,MAAO,MAAM,CAAAS,WAAW,CAAG,GAAI,CAAA1C,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}