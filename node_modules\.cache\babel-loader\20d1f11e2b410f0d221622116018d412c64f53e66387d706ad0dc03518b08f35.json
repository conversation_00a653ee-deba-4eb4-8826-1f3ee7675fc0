{"ast": null, "code": "export class StorageError extends Error {\n  constructor(message) {\n    super(message);\n    this.__isStorageError = true;\n    this.name = 'StorageError';\n  }\n}\nexport function isStorageError(error) {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n  constructor(message, status) {\n    super(message);\n    this.name = 'StorageApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class StorageUnknownError extends StorageError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'StorageUnknownError';\n    this.originalError = originalError;\n  }\n}", "map": {"version": 3, "names": ["StorageError", "Error", "constructor", "message", "__isStorageError", "name", "isStorageError", "error", "StorageApiError", "status", "toJSON", "StorageUnknownError", "originalError"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\storage-js\\src\\lib\\errors.ts"], "sourcesContent": ["export class StorageError extends Error {\n  protected __isStorageError = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'StorageError'\n  }\n}\n\nexport function isStorageError(error: unknown): error is StorageError {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error\n}\n\nexport class StorageApiError extends StorageError {\n  status: number\n\n  constructor(message: string, status: number) {\n    super(message)\n    this.name = 'StorageApiError'\n    this.status = status\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n    }\n  }\n}\n\nexport class StorageUnknownError extends StorageError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'StorageUnknownError'\n    this.originalError = originalError\n  }\n}\n"], "mappings": "AAAA,OAAM,MAAOA,YAAa,SAAQC,KAAK;EAGrCC,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,CAAC;IAHN,KAAAC,gBAAgB,GAAG,IAAI;IAI/B,IAAI,CAACC,IAAI,GAAG,cAAc;EAC5B;;AAGF,OAAM,SAAUC,cAAcA,CAACC,KAAc;EAC3C,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,kBAAkB,IAAIA,KAAK;AACnF;AAEA,OAAM,MAAOC,eAAgB,SAAQR,YAAY;EAG/CE,YAAYC,OAAe,EAAEM,MAAc;IACzC,KAAK,CAACN,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACI,MAAM,GAAGA,MAAM;EACtB;EAEAC,MAAMA,CAAA;IACJ,OAAO;MACLL,IAAI,EAAE,IAAI,CAACA,IAAI;MACfF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBM,MAAM,EAAE,IAAI,CAACA;KACd;EACH;;AAGF,OAAM,MAAOE,mBAAoB,SAAQX,YAAY;EAGnDE,YAAYC,OAAe,EAAES,aAAsB;IACjD,KAAK,CAACT,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACO,aAAa,GAAGA,aAAa;EACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}