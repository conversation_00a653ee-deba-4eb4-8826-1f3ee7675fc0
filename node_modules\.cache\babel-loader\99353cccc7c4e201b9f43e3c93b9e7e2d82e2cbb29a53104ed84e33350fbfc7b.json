{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{toast}from'react-hot-toast';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,AcademicCapIcon,PlayIcon,DocumentIcon,ClipboardDocumentListIcon}from'@heroicons/react/24/outline';import{dataService}from'../../services/dataService';import AddCourseModal from'./modals/AddCourseModal';import EditCourseModal from'./modals/EditCourseModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CoursesManagement=_ref=>{let{onBack}=_ref;const[courses,setCourses]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[selectedCategory,setSelectedCategory]=useState('all');const[loading,setLoading]=useState(true);const[showAddModal,setShowAddModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[selectedCourse,setSelectedCourse]=useState(null);useEffect(()=>{loadCourses();},[]);const loadCourses=async()=>{try{setLoading(true);const coursesData=await dataService.getCourses();setCourses(coursesData);}catch(error){console.error('Error loading courses:',error);toast.error('حدث خطأ في تحميل الكورسات');}finally{setLoading(false);}};const filteredCourses=courses.filter(course=>{const matchesSearch=course.title.toLowerCase().includes(searchTerm.toLowerCase())||course.description.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=selectedCategory==='all'||course.categoryId===selectedCategory;return matchesSearch&&matchesCategory;});const handleAddCourse=()=>{setShowAddModal(true);};const handleEditCourse=courseId=>{const course=courses.find(c=>c.id===courseId);if(course){setSelectedCourse(course);setShowEditModal(true);}};const handleDeleteCourse=async courseId=>{if(window.confirm('هل أنت متأكد من حذف هذا الكورس؟')){try{await dataService.deleteCourse(courseId);toast.success('تم حذف الكورس بنجاح');loadCourses();}catch(error){console.error('Error deleting course:',error);toast.error('حدث خطأ في حذف الكورس');}}};const handleViewCourse=courseId=>{// TODO: Implement view course functionality\nconsole.log('View course:',courseId);};const handleCourseAdded=()=>{setShowAddModal(false);loadCourses();toast.success('تم إضافة الكورس بنجاح');};const handleCourseUpdated=()=>{setShowEditModal(false);setSelectedCourse(null);loadCourses();toast.success('تم تحديث الكورس بنجاح');};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u0646\\u0638\\u064A\\u0645 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddCourse,className:\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0643\\u0648\\u0631\\u0633 \\u062C\\u062F\\u064A\\u062F\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0643\\u0648\\u0631\\u0633...\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u062A\\u0635\\u0646\\u064A\\u0641\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062A\\u0635\\u0646\\u064A\\u0641\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"option\",{value:\"programming\",children:\"\\u0627\\u0644\\u0628\\u0631\\u0645\\u062C\\u0629\"}),/*#__PURE__*/_jsx(\"option\",{value:\"web\",children:\"\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0627\\u0642\\u0639\"}),/*#__PURE__*/_jsx(\"option\",{value:\"mobile\",children:\"\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\\u0627\\u062A\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:filteredCourses.map((course,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.1},className:\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:course.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:course.description})]})]}),/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded-full ${course.isActive?'bg-green-100 text-green-800':'bg-red-100 text-red-800'}`,children:course.isActive?'نشط':'غير نشط'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[course.videos.length,\" \\u0641\\u064A\\u062F\\u064A\\u0648\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(DocumentIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[course.pdfs.length,\" \\u0645\\u0644\\u0641\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[course.quizzes.length,\" \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between pt-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewCourse(course.id),className:\"p-2 text-gray-600 hover:text-blue-600 transition-colors\",title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditCourse(course.id),className:\"p-2 text-gray-600 hover:text-green-600 transition-colors\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteCourse(course.id),className:\"p-2 text-gray-600 hover:text-red-600 transition-colors\",title:\"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:new Date(course.createdAt).toLocaleDateString('ar-SA')})]})]})},course.id))}),filteredCourses.length===0&&!loading&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B\"})]}),loading&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A...\"})]}),showAddModal&&/*#__PURE__*/_jsx(AddCourseModal,{onClose:()=>setShowAddModal(false),onSuccess:handleCourseAdded}),showEditModal&&selectedCourse&&/*#__PURE__*/_jsx(EditCourseModal,{course:selectedCourse,onClose:()=>{setShowEditModal(false);setSelectedCourse(null);},onSuccess:handleCourseUpdated})]});};export default CoursesManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "toast", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "AcademicCapIcon", "PlayIcon", "DocumentIcon", "ClipboardDocumentListIcon", "dataService", "AddCourseModal", "EditCourseModal", "jsx", "_jsx", "jsxs", "_jsxs", "CoursesManagement", "_ref", "onBack", "courses", "setCourses", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "loading", "setLoading", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "selectedCourse", "setSelectedCourse", "loadCourses", "coursesData", "getCourses", "error", "console", "filteredCourses", "filter", "course", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesCategory", "categoryId", "handleAddCourse", "handleEditCourse", "courseId", "find", "c", "id", "handleDeleteCourse", "window", "confirm", "deleteCourse", "success", "handleViewCourse", "log", "handleCourseAdded", "handleCourseUpdated", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "target", "placeholder", "map", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "isActive", "videos", "length", "pdfs", "quizzes", "Date", "createdAt", "toLocaleDateString", "onClose", "onSuccess"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/CoursesManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  AcademicCapIcon,\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline';\nimport { dataService } from '../../services/dataService';\nimport { Course } from '../../types';\nimport AddCourseModal from './modals/AddCourseModal';\nimport EditCourseModal from './modals/EditCourseModal';\n\n\n\ninterface CoursesManagementProps {\n  onBack?: () => void;\n}\n\nconst CoursesManagement: React.FC<CoursesManagementProps> = ({ onBack }) => {\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [loading, setLoading] = useState(true);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);\n\n  useEffect(() => {\n    loadCourses();\n  }, []);\n\n  const loadCourses = async () => {\n    try {\n      setLoading(true);\n      const coursesData = await dataService.getCourses();\n      setCourses(coursesData);\n    } catch (error) {\n      console.error('Error loading courses:', error);\n      toast.error('حدث خطأ في تحميل الكورسات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n\n\n  const filteredCourses = courses.filter(course => {\n    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         course.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || course.categoryId === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleAddCourse = () => {\n    setShowAddModal(true);\n  };\n\n  const handleEditCourse = (courseId: string) => {\n    const course = courses.find(c => c.id === courseId);\n    if (course) {\n      setSelectedCourse(course);\n      setShowEditModal(true);\n    }\n  };\n\n  const handleDeleteCourse = async (courseId: string) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الكورس؟')) {\n      try {\n        await dataService.deleteCourse(courseId);\n        toast.success('تم حذف الكورس بنجاح');\n        loadCourses();\n      } catch (error) {\n        console.error('Error deleting course:', error);\n        toast.error('حدث خطأ في حذف الكورس');\n      }\n    }\n  };\n\n  const handleViewCourse = (courseId: string) => {\n    // TODO: Implement view course functionality\n    console.log('View course:', courseId);\n  };\n\n  const handleCourseAdded = () => {\n    setShowAddModal(false);\n    loadCourses();\n    toast.success('تم إضافة الكورس بنجاح');\n  };\n\n  const handleCourseUpdated = () => {\n    setShowEditModal(false);\n    setSelectedCourse(null);\n    loadCourses();\n    toast.success('تم تحديث الكورس بنجاح');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الكورسات</h1>\n            <p className=\"text-gray-600\">إدارة وتنظيم جميع الكورسات التعليمية</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddCourse}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة كورس جديد</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البحث في الكورسات\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"ابحث عن كورس...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              التصنيف\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">جميع التصنيفات</option>\n              <option value=\"programming\">البرمجة</option>\n              <option value=\"web\">تطوير المواقع</option>\n              <option value=\"mobile\">تطوير التطبيقات</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Courses Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredCourses.map((course, index) => (\n          <motion.div\n            key={course.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <div className=\"p-2 bg-blue-100 rounded-lg\">\n                    <AcademicCapIcon className=\"w-6 h-6 text-blue-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{course.title}</h3>\n                    <p className=\"text-sm text-gray-600\">{course.description}</p>\n                  </div>\n                </div>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  course.isActive \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {course.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n\n              <div className=\"flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-4\">\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <PlayIcon className=\"w-4 h-4\" />\n                  <span>{course.videos.length} فيديو</span>\n                </div>\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <DocumentIcon className=\"w-4 h-4\" />\n                  <span>{course.pdfs.length} ملف</span>\n                </div>\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <ClipboardDocumentListIcon className=\"w-4 h-4\" />\n                  <span>{course.quizzes.length} اختبار</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleViewCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                    title=\"عرض الكورس\"\n                  >\n                    <EyeIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleEditCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-green-600 transition-colors\"\n                    title=\"تعديل الكورس\"\n                  >\n                    <PencilIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-red-600 transition-colors\"\n                    title=\"حذف الكورس\"\n                  >\n                    <TrashIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <span className=\"text-xs text-gray-500\">\n                  {new Date(course.createdAt).toLocaleDateString('ar-SA')}\n                </span>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {filteredCourses.length === 0 && !loading && (\n        <div className=\"text-center py-12\">\n          <AcademicCapIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد كورسات</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي كورسات تطابق البحث</p>\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري تحميل الكورسات...</p>\n        </div>\n      )}\n\n      {/* Add Course Modal */}\n      {showAddModal && (\n        <AddCourseModal\n          onClose={() => setShowAddModal(false)}\n          onSuccess={handleCourseAdded}\n        />\n      )}\n\n      {/* Edit Course Modal */}\n      {showEditModal && selectedCourse && (\n        <EditCourseModal\n          course={selectedCourse}\n          onClose={() => {\n            setShowEditModal(false);\n            setSelectedCourse(null);\n          }}\n          onSuccess={handleCourseUpdated}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default CoursesManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,yBAAyB,KACpB,6BAA6B,CACpC,OAASC,WAAW,KAAQ,4BAA4B,CAExD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQvD,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACrE,KAAM,CAACE,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC0B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAgB,IAAI,CAAC,CAEzEC,SAAS,CAAC,IAAM,CACdmC,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAQ,WAAW,CAAG,KAAM,CAAAzB,WAAW,CAAC0B,UAAU,CAAC,CAAC,CAClDf,UAAU,CAACc,WAAW,CAAC,CACzB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CpC,KAAK,CAACoC,KAAK,CAAC,2BAA2B,CAAC,CAC1C,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAMD,KAAM,CAAAY,eAAe,CAAGnB,OAAO,CAACoB,MAAM,CAACC,MAAM,EAAI,CAC/C,KAAM,CAAAC,aAAa,CAAGD,MAAM,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,EAC9DH,MAAM,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,CACxF,KAAM,CAAAG,eAAe,CAAGvB,gBAAgB,GAAK,KAAK,EAAIiB,MAAM,CAACO,UAAU,GAAKxB,gBAAgB,CAC5F,MAAO,CAAAkB,aAAa,EAAIK,eAAe,CACzC,CAAC,CAAC,CAEF,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5BpB,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAqB,gBAAgB,CAAIC,QAAgB,EAAK,CAC7C,KAAM,CAAAV,MAAM,CAAGrB,OAAO,CAACgC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKH,QAAQ,CAAC,CACnD,GAAIV,MAAM,CAAE,CACVR,iBAAiB,CAACQ,MAAM,CAAC,CACzBV,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAwB,kBAAkB,CAAG,KAAO,CAAAJ,QAAgB,EAAK,CACrD,GAAIK,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAE,CACrD,GAAI,CACF,KAAM,CAAA/C,WAAW,CAACgD,YAAY,CAACP,QAAQ,CAAC,CACxClD,KAAK,CAAC0D,OAAO,CAAC,qBAAqB,CAAC,CACpCzB,WAAW,CAAC,CAAC,CACf,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CpC,KAAK,CAACoC,KAAK,CAAC,uBAAuB,CAAC,CACtC,CACF,CACF,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAIT,QAAgB,EAAK,CAC7C;AACAb,OAAO,CAACuB,GAAG,CAAC,cAAc,CAAEV,QAAQ,CAAC,CACvC,CAAC,CAED,KAAM,CAAAW,iBAAiB,CAAGA,CAAA,GAAM,CAC9BjC,eAAe,CAAC,KAAK,CAAC,CACtBK,WAAW,CAAC,CAAC,CACbjC,KAAK,CAAC0D,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAC,CAED,KAAM,CAAAI,mBAAmB,CAAGA,CAAA,GAAM,CAChChC,gBAAgB,CAAC,KAAK,CAAC,CACvBE,iBAAiB,CAAC,IAAI,CAAC,CACvBC,WAAW,CAAC,CAAC,CACbjC,KAAK,CAAC0D,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAC,CAED,mBACE3C,KAAA,QAAKgD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBjD,KAAA,QAAKgD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjD,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzD9C,MAAM,eACLL,IAAA,WACEoD,OAAO,CAAE/C,MAAO,CAChB6C,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEnD,IAAA,QAAKkD,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EnD,IAAA,SAAMwD,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDzD,KAAA,QAAAiD,QAAA,eACEnD,IAAA,OAAIkD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cACpEnD,IAAA,MAAGkD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sMAAoC,CAAG,CAAC,EAClE,CAAC,EACH,CAAC,cACNjD,KAAA,WACEkD,OAAO,CAAEjB,eAAgB,CACzBe,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvInD,IAAA,CAACZ,QAAQ,EAAC8D,SAAS,CAAC,SAAS,CAAE,CAAC,cAChClD,IAAA,SAAAmD,QAAA,CAAM,kFAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,cAGNnD,IAAA,QAAKkD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDjD,KAAA,QAAKgD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjD,KAAA,QAAAiD,QAAA,eACEnD,IAAA,UAAOkD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,8FAEhE,CAAO,CAAC,cACRnD,IAAA,UACE4D,IAAI,CAAC,MAAM,CACXC,KAAK,CAAErD,UAAW,CAClBsD,QAAQ,CAAGC,CAAC,EAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,mEAAiB,CAC7Bf,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cACNhD,KAAA,QAAAiD,QAAA,eACEnD,IAAA,UAAOkD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,4CAEhE,CAAO,CAAC,cACRjD,KAAA,WACE2D,KAAK,CAAEnD,gBAAiB,CACxBoD,QAAQ,CAAGC,CAAC,EAAKpD,mBAAmB,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDX,SAAS,CAAC,wGAAwG,CAAAC,QAAA,eAElHnD,IAAA,WAAQ6D,KAAK,CAAC,KAAK,CAAAV,QAAA,CAAC,iFAAc,CAAQ,CAAC,cAC3CnD,IAAA,WAAQ6D,KAAK,CAAC,aAAa,CAAAV,QAAA,CAAC,4CAAO,CAAQ,CAAC,cAC5CnD,IAAA,WAAQ6D,KAAK,CAAC,KAAK,CAAAV,QAAA,CAAC,2EAAa,CAAQ,CAAC,cAC1CnD,IAAA,WAAQ6D,KAAK,CAAC,QAAQ,CAAAV,QAAA,CAAC,uFAAe,CAAQ,CAAC,EACzC,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGNnD,IAAA,QAAKkD,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClE1B,eAAe,CAACyC,GAAG,CAAC,CAACvC,MAAM,CAAEwC,KAAK,gBACjCnE,IAAA,CAACd,MAAM,CAACkF,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAEP,KAAK,CAAG,GAAI,CAAE,CACnCjB,SAAS,CAAC,wGAAwG,CAAAC,QAAA,cAElHjD,KAAA,QAAKgD,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjD,KAAA,QAAKgD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjD,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnD,IAAA,QAAKkD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCnD,IAAA,CAACR,eAAe,EAAC0D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAClD,CAAC,cACNhD,KAAA,QAAAiD,QAAA,eACEnD,IAAA,OAAIkD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAExB,MAAM,CAACE,KAAK,CAAK,CAAC,cAC/D7B,IAAA,MAAGkD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAExB,MAAM,CAACK,WAAW,CAAI,CAAC,EAC1D,CAAC,EACH,CAAC,cACNhC,IAAA,SAAMkD,SAAS,CAAE,kCACfvB,MAAM,CAACgD,QAAQ,CACX,6BAA6B,CAC7B,yBAAyB,EAC5B,CAAAxB,QAAA,CACAxB,MAAM,CAACgD,QAAQ,CAAG,KAAK,CAAG,SAAS,CAChC,CAAC,EACJ,CAAC,cAENzE,KAAA,QAAKgD,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eACrFjD,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnD,IAAA,CAACP,QAAQ,EAACyD,SAAS,CAAC,SAAS,CAAE,CAAC,cAChChD,KAAA,SAAAiD,QAAA,EAAOxB,MAAM,CAACiD,MAAM,CAACC,MAAM,CAAC,iCAAM,EAAM,CAAC,EACtC,CAAC,cACN3E,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnD,IAAA,CAACN,YAAY,EAACwD,SAAS,CAAC,SAAS,CAAE,CAAC,cACpChD,KAAA,SAAAiD,QAAA,EAAOxB,MAAM,CAACmD,IAAI,CAACD,MAAM,CAAC,qBAAI,EAAM,CAAC,EAClC,CAAC,cACN3E,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnD,IAAA,CAACL,yBAAyB,EAACuD,SAAS,CAAC,SAAS,CAAE,CAAC,cACjDhD,KAAA,SAAAiD,QAAA,EAAOxB,MAAM,CAACoD,OAAO,CAACF,MAAM,CAAC,uCAAO,EAAM,CAAC,EACxC,CAAC,EACH,CAAC,cAEN3E,KAAA,QAAKgD,SAAS,CAAC,iEAAiE,CAAAC,QAAA,eAC9EjD,KAAA,QAAKgD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DnD,IAAA,WACEoD,OAAO,CAAEA,CAAA,GAAMN,gBAAgB,CAACnB,MAAM,CAACa,EAAE,CAAE,CAC3CU,SAAS,CAAC,yDAAyD,CACnErB,KAAK,CAAC,yDAAY,CAAAsB,QAAA,cAElBnD,IAAA,CAACT,OAAO,EAAC2D,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACTlD,IAAA,WACEoD,OAAO,CAAEA,CAAA,GAAMhB,gBAAgB,CAACT,MAAM,CAACa,EAAE,CAAE,CAC3CU,SAAS,CAAC,0DAA0D,CACpErB,KAAK,CAAC,qEAAc,CAAAsB,QAAA,cAEpBnD,IAAA,CAACX,UAAU,EAAC6D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTlD,IAAA,WACEoD,OAAO,CAAEA,CAAA,GAAMX,kBAAkB,CAACd,MAAM,CAACa,EAAE,CAAE,CAC7CU,SAAS,CAAC,wDAAwD,CAClErB,KAAK,CAAC,yDAAY,CAAAsB,QAAA,cAElBnD,IAAA,CAACV,SAAS,EAAC4D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cACNlD,IAAA,SAAMkD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpC,GAAI,CAAA6B,IAAI,CAACrD,MAAM,CAACsD,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACnD,CAAC,EACJ,CAAC,EACH,CAAC,EArEDvD,MAAM,CAACa,EAsEF,CACb,CAAC,CACC,CAAC,CAELf,eAAe,CAACoD,MAAM,GAAK,CAAC,EAAI,CAACjE,OAAO,eACvCV,KAAA,QAAKgD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnD,IAAA,CAACR,eAAe,EAAC0D,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACpElD,IAAA,OAAIkD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4EAAc,CAAI,CAAC,cAC1EnD,IAAA,MAAGkD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yMAAuC,CAAG,CAAC,EACrE,CACN,CAEAvC,OAAO,eACNV,KAAA,QAAKgD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnD,IAAA,QAAKkD,SAAS,CAAC,8FAA8F,CAAM,CAAC,cACpHlD,IAAA,MAAGkD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6GAAsB,CAAG,CAAC,EACpD,CACN,CAGArC,YAAY,eACXd,IAAA,CAACH,cAAc,EACbsF,OAAO,CAAEA,CAAA,GAAMpE,eAAe,CAAC,KAAK,CAAE,CACtCqE,SAAS,CAAEpC,iBAAkB,CAC9B,CACF,CAGAhC,aAAa,EAAIE,cAAc,eAC9BlB,IAAA,CAACF,eAAe,EACd6B,MAAM,CAAET,cAAe,CACvBiE,OAAO,CAAEA,CAAA,GAAM,CACblE,gBAAgB,CAAC,KAAK,CAAC,CACvBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACFiE,SAAS,CAAEnC,mBAAoB,CAChC,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}