{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{AcademicCapIcon,PlayIcon,ClipboardDocumentListIcon,CheckCircleIcon,ClockIcon,ArrowRightIcon,FunnelIcon}from'@heroicons/react/24/outline';import{toast}from'react-hot-toast';// Components\nimport ResponsiveGrid from'../common/ResponsiveGrid';import ResponsiveCard from'../common/ResponsiveCard';import ResponsiveButton from'../common/ResponsiveButton';import ResponsiveText from'../common/ResponsiveText';import LoadingSpinner from'../common/LoadingSpinner';// Services\nimport{supabaseService}from'../../services/supabaseService';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MyCourses=_ref=>{let{user}=_ref;const navigate=useNavigate();const[filter,setFilter]=useState('all');const[courses,setCourses]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Load user's enrolled courses\nuseEffect(()=>{loadEnrolledCourses();},[user.id]);const loadEnrolledCourses=async()=>{try{setLoading(true);setError(null);// Get student enrollments with course details\nconst enrollments=await supabaseService.getStudentEnrollments(user.id);setCourses(enrollments||[]);}catch(error){console.error('Error loading courses:',error);setError('حدث خطأ في تحميل الكورسات');toast.error('فشل في تحميل الكورسات');}finally{setLoading(false);}};const filteredCourses=courses.filter(enrollment=>{if(filter==='completed'){return enrollment.progress===100;}else if(filter==='in-progress'){return enrollment.progress>0&&enrollment.progress<100;}return true;});const handleCourseClick=courseId=>{navigate(`/student/course/${courseId}`);};const getProgressColor=progress=>{if(progress===100)return'bg-green-500';if(progress>=50)return'bg-blue-500';return'bg-yellow-500';};const getStatusIcon=progress=>{if(progress===100){return/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"});}return/*#__PURE__*/_jsx(ClockIcon,{className:\"w-5 h-5 text-blue-600\"});};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center py-12\",children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-red-600 mb-4\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-12 h-12 mx-auto mb-2\"}),/*#__PURE__*/_jsx(\"p\",{children:error})]}),/*#__PURE__*/_jsx(ResponsiveButton,{onClick:loadEnrolledCourses,variant:\"primary\",children:\"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"container-responsive space-y-6 sm:space-y-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(ResponsiveText,{as:\"h1\",size:\"2xl\",weight:\"bold\",color:\"gray\",children:\"\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"sm\",color:\"gray\",className:\"mt-1\",children:\"\\u062A\\u0627\\u0628\\u0639 \\u062A\\u0642\\u062F\\u0645\\u0643 \\u0641\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644 \\u0628\\u0647\\u0627\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(ResponsiveText,{size:\"lg\",weight:\"bold\",color:\"primary\",children:courses.length}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"xs\",color:\"gray\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(ResponsiveText,{size:\"lg\",weight:\"bold\",color:\"accent\",children:courses.filter(c=>c.progress===100).length}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"xs\",color:\"gray\",children:\"\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"})]})]})]}),/*#__PURE__*/_jsxs(ResponsiveCard,{padding:\"md\",className:\"bg-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(FunnelIcon,{className:\"w-5 h-5 text-gray-500\"}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"sm\",weight:\"medium\",color:\"gray\",children:\"\\u062A\\u0635\\u0641\\u064A\\u0629 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap gap-2 sm:gap-4\",children:[/*#__PURE__*/_jsxs(ResponsiveButton,{onClick:()=>setFilter('all'),variant:filter==='all'?'primary':'ghost',size:\"sm\",className:\"flex-1 sm:flex-none\",children:[\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A (\",courses.length,\")\"]}),/*#__PURE__*/_jsxs(ResponsiveButton,{onClick:()=>setFilter('in-progress'),variant:filter==='in-progress'?'primary':'ghost',size:\"sm\",className:\"flex-1 sm:flex-none\",children:[\"\\u0642\\u064A\\u062F \\u0627\\u0644\\u062A\\u0642\\u062F\\u0645 (\",courses.filter(c=>c.progress>0&&c.progress<100).length,\")\"]}),/*#__PURE__*/_jsxs(ResponsiveButton,{onClick:()=>setFilter('completed'),variant:filter==='completed'?'primary':'ghost',size:\"sm\",className:\"flex-1 sm:flex-none\",children:[\"\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629 (\",courses.filter(c=>c.progress===100).length,\")\"]})]})]}),/*#__PURE__*/_jsx(ResponsiveGrid,{columns:{mobile:1,tablet:2,desktop:3},gap:\"md\",animated:true,staggerChildren:true,children:filteredCourses.map((enrollment,index)=>{var _course$videos,_course$quizzes;const course=enrollment.courses;const progress=enrollment.progress||0;const totalVideos=(course===null||course===void 0?void 0:(_course$videos=course.videos)===null||_course$videos===void 0?void 0:_course$videos.length)||0;const completedVideos=Math.floor(progress/100*totalVideos);return/*#__PURE__*/_jsx(ResponsiveCard,{hover:true,animated:true,animationDelay:index*0.1,onClick:()=>handleCourseClick(course.id),className:\"cursor-pointer\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-primary-100 rounded-lg flex-shrink-0\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-5 h-5 sm:w-6 sm:h-6 text-primary-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(ResponsiveText,{as:\"h3\",size:\"base\",weight:\"semibold\",color:\"gray\",className:\"truncate\",children:course.title}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"xs\",color:\"gray\",className:\"line-clamp-2 mt-1\",children:course.description})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:getStatusIcon(progress)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(ResponsiveText,{size:\"xs\",color:\"gray\",children:\"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"}),/*#__PURE__*/_jsxs(ResponsiveText,{size:\"xs\",weight:\"medium\",color:\"gray\",children:[progress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress)}`,style:{width:`${progress}%`}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-xs sm:text-sm text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"w-3 h-3 sm:w-4 sm:h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[completedVideos,\"/\",totalVideos,\" \\u0641\\u064A\\u062F\\u064A\\u0648\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-3 h-3 sm:w-4 sm:h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[(course===null||course===void 0?void 0:(_course$quizzes=course.quizzes)===null||_course$quizzes===void 0?void 0:_course$quizzes.length)||0,\" \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"]})]})]}),/*#__PURE__*/_jsx(ResponsiveButton,{onClick:e=>{e===null||e===void 0?void 0:e.stopPropagation();handleCourseClick(course.id);},variant:\"primary\",size:\"sm\",fullWidth:true,icon:/*#__PURE__*/_jsx(ArrowRightIcon,{className:\"w-4 h-4\"}),iconPosition:\"left\",children:progress===100?'مراجعة الكورس':'متابعة التعلم'})]})},enrollment.id);})}),filteredCourses.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(ResponsiveText,{as:\"h3\",size:\"lg\",weight:\"medium\",color:\"gray\",className:\"mb-2\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(ResponsiveText,{size:\"sm\",color:\"gray\",children:filter==='completed'?'لم تكمل أي كورسات بعد':filter==='in-progress'?'لا توجد كورسات قيد التقدم':'لم تسجل في أي كورسات بعد'})]})]});};export default MyCourses;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "AcademicCapIcon", "PlayIcon", "ClipboardDocumentListIcon", "CheckCircleIcon", "ClockIcon", "ArrowRightIcon", "FunnelIcon", "toast", "ResponsiveGrid", "ResponsiveCard", "ResponsiveButton", "ResponsiveText", "LoadingSpinner", "supabaseService", "jsx", "_jsx", "jsxs", "_jsxs", "MyCourses", "_ref", "user", "navigate", "filter", "setFilter", "courses", "setCourses", "loading", "setLoading", "error", "setError", "loadEnrolledCourses", "id", "enrollments", "getStudentEnrollments", "console", "filteredCourses", "enrollment", "progress", "handleCourseClick", "courseId", "getProgressColor", "getStatusIcon", "className", "children", "onClick", "variant", "as", "size", "weight", "color", "length", "c", "padding", "columns", "mobile", "tablet", "desktop", "gap", "animated", "stagger<PERSON><PERSON><PERSON><PERSON>", "map", "index", "_course$videos", "_course$quizzes", "course", "totalVideos", "videos", "completedVideos", "Math", "floor", "hover", "animationDelay", "title", "description", "style", "width", "quizzes", "e", "stopPropagation", "fullWidth", "icon", "iconPosition"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/MyCourses.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AcademicCapIcon,\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon,\n  CheckCircleIcon,\n  ClockIcon,\n  ArrowRightIcon,\n  FunnelIcon\n} from '@heroicons/react/24/outline';\nimport { toast } from 'react-hot-toast';\n\n// Components\nimport ResponsiveGrid from '../common/ResponsiveGrid';\nimport ResponsiveCard from '../common/ResponsiveCard';\nimport ResponsiveButton from '../common/ResponsiveButton';\nimport ResponsiveText from '../common/ResponsiveText';\nimport LoadingSpinner from '../common/LoadingSpinner';\n\n// Services\nimport { supabaseService } from '../../services/supabaseService';\n\n// Types\nimport { Course, Student } from '../../types';\n\ninterface MyCoursesProps {\n  user: Student;\n}\n\nconst MyCourses: React.FC<MyCoursesProps> = ({ user }) => {\n  const navigate = useNavigate();\n  const [filter, setFilter] = useState('all');\n  const [courses, setCourses] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load user's enrolled courses\n  useEffect(() => {\n    loadEnrolledCourses();\n  }, [user.id]);\n\n  const loadEnrolledCourses = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Get student enrollments with course details\n      const enrollments = await supabaseService.getStudentEnrollments(user.id);\n      setCourses(enrollments || []);\n    } catch (error: any) {\n      console.error('Error loading courses:', error);\n      setError('حدث خطأ في تحميل الكورسات');\n      toast.error('فشل في تحميل الكورسات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredCourses = courses.filter(enrollment => {\n    if (filter === 'completed') {\n      return enrollment.progress === 100;\n    } else if (filter === 'in-progress') {\n      return enrollment.progress > 0 && enrollment.progress < 100;\n    }\n    return true;\n  });\n\n  const handleCourseClick = (courseId: string) => {\n    navigate(`/student/course/${courseId}`);\n  };\n\n  const getProgressColor = (progress: number) => {\n    if (progress === 100) return 'bg-green-500';\n    if (progress >= 50) return 'bg-blue-500';\n    return 'bg-yellow-500';\n  };\n\n  const getStatusIcon = (progress: number) => {\n    if (progress === 100) {\n      return <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />;\n    }\n    return <ClockIcon className=\"w-5 h-5 text-blue-600\" />;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <LoadingSpinner />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-red-600 mb-4\">\n          <AcademicCapIcon className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>{error}</p>\n        </div>\n        <ResponsiveButton onClick={loadEnrolledCourses} variant=\"primary\">\n          إعادة المحاولة\n        </ResponsiveButton>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container-responsive space-y-6 sm:space-y-8\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n        <div>\n          <ResponsiveText as=\"h1\" size=\"2xl\" weight=\"bold\" color=\"gray\">\n            كورساتي\n          </ResponsiveText>\n          <ResponsiveText size=\"sm\" color=\"gray\" className=\"mt-1\">\n            تابع تقدمك في الكورسات المسجل بها\n          </ResponsiveText>\n        </div>\n\n        {/* Stats Summary */}\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          <div className=\"text-center\">\n            <ResponsiveText size=\"lg\" weight=\"bold\" color=\"primary\">\n              {courses.length}\n            </ResponsiveText>\n            <ResponsiveText size=\"xs\" color=\"gray\">\n              إجمالي الكورسات\n            </ResponsiveText>\n          </div>\n          <div className=\"text-center\">\n            <ResponsiveText size=\"lg\" weight=\"bold\" color=\"accent\">\n              {courses.filter(c => c.progress === 100).length}\n            </ResponsiveText>\n            <ResponsiveText size=\"xs\" color=\"gray\">\n              مكتملة\n            </ResponsiveText>\n          </div>\n        </div>\n      </div>\n\n      {/* Filter Tabs */}\n      <ResponsiveCard padding=\"md\" className=\"bg-white\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2 space-x-reverse\">\n            <FunnelIcon className=\"w-5 h-5 text-gray-500\" />\n            <ResponsiveText size=\"sm\" weight=\"medium\" color=\"gray\">\n              تصفية الكورسات\n            </ResponsiveText>\n          </div>\n        </div>\n\n        <div className=\"flex flex-wrap gap-2 sm:gap-4\">\n          <ResponsiveButton\n            onClick={() => setFilter('all')}\n            variant={filter === 'all' ? 'primary' : 'ghost'}\n            size=\"sm\"\n            className=\"flex-1 sm:flex-none\"\n          >\n            جميع الكورسات ({courses.length})\n          </ResponsiveButton>\n          <ResponsiveButton\n            onClick={() => setFilter('in-progress')}\n            variant={filter === 'in-progress' ? 'primary' : 'ghost'}\n            size=\"sm\"\n            className=\"flex-1 sm:flex-none\"\n          >\n            قيد التقدم ({courses.filter(c => c.progress > 0 && c.progress < 100).length})\n          </ResponsiveButton>\n          <ResponsiveButton\n            onClick={() => setFilter('completed')}\n            variant={filter === 'completed' ? 'primary' : 'ghost'}\n            size=\"sm\"\n            className=\"flex-1 sm:flex-none\"\n          >\n            مكتملة ({courses.filter(c => c.progress === 100).length})\n          </ResponsiveButton>\n        </div>\n      </ResponsiveCard>\n\n      {/* Courses Grid */}\n      <ResponsiveGrid\n        columns={{ mobile: 1, tablet: 2, desktop: 3 }}\n        gap=\"md\"\n        animated={true}\n        staggerChildren={true}\n      >\n        {filteredCourses.map((enrollment, index) => {\n          const course = enrollment.courses;\n          const progress = enrollment.progress || 0;\n          const totalVideos = course?.videos?.length || 0;\n          const completedVideos = Math.floor((progress / 100) * totalVideos);\n\n          return (\n            <ResponsiveCard\n              key={enrollment.id}\n              hover={true}\n              animated={true}\n              animationDelay={index * 0.1}\n              onClick={() => handleCourseClick(course.id)}\n              className=\"cursor-pointer\"\n            >\n              <div className=\"space-y-4\">\n                {/* Course Header */}\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center space-x-3 space-x-reverse flex-1\">\n                    <div className=\"p-2 bg-primary-100 rounded-lg flex-shrink-0\">\n                      <AcademicCapIcon className=\"w-5 h-5 sm:w-6 sm:h-6 text-primary-600\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <ResponsiveText\n                        as=\"h3\"\n                        size=\"base\"\n                        weight=\"semibold\"\n                        color=\"gray\"\n                        className=\"truncate\"\n                      >\n                        {course.title}\n                      </ResponsiveText>\n                      <ResponsiveText\n                        size=\"xs\"\n                        color=\"gray\"\n                        className=\"line-clamp-2 mt-1\"\n                      >\n                        {course.description}\n                      </ResponsiveText>\n                    </div>\n                  </div>\n                  <div className=\"flex-shrink-0\">\n                    {getStatusIcon(progress)}\n                  </div>\n                </div>\n\n                {/* Progress Bar */}\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <ResponsiveText size=\"xs\" color=\"gray\">\n                      التقدم\n                    </ResponsiveText>\n                    <ResponsiveText size=\"xs\" weight=\"medium\" color=\"gray\">\n                      {progress}%\n                    </ResponsiveText>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress)}`}\n                      style={{ width: `${progress}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                {/* Course Stats */}\n                <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600\">\n                  <div className=\"flex items-center space-x-1 space-x-reverse\">\n                    <PlayIcon className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                    <span>{completedVideos}/{totalVideos} فيديو</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1 space-x-reverse\">\n                    <ClipboardDocumentListIcon className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                    <span>{course?.quizzes?.length || 0} اختبار</span>\n                  </div>\n                </div>\n\n                {/* Continue Button */}\n                <ResponsiveButton\n                  onClick={(e) => {\n                    e?.stopPropagation();\n                    handleCourseClick(course.id);\n                  }}\n                  variant=\"primary\"\n                  size=\"sm\"\n                  fullWidth={true}\n                  icon={<ArrowRightIcon className=\"w-4 h-4\" />}\n                  iconPosition=\"left\"\n                >\n                  {progress === 100 ? 'مراجعة الكورس' : 'متابعة التعلم'}\n                </ResponsiveButton>\n              </div>\n            </ResponsiveCard>\n          );\n        })}\n      </ResponsiveGrid>\n\n      {/* Empty State */}\n      {filteredCourses.length === 0 && (\n        <div className=\"text-center py-12\">\n          <AcademicCapIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <ResponsiveText as=\"h3\" size=\"lg\" weight=\"medium\" color=\"gray\" className=\"mb-2\">\n            لا توجد كورسات\n          </ResponsiveText>\n          <ResponsiveText size=\"sm\" color=\"gray\">\n            {filter === 'completed'\n              ? 'لم تكمل أي كورسات بعد'\n              : filter === 'in-progress'\n              ? 'لا توجد كورسات قيد التقدم'\n              : 'لم تسجل في أي كورسات بعد'\n            }\n          </ResponsiveText>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MyCourses;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,eAAe,CACfC,QAAQ,CAERC,yBAAyB,CACzBC,eAAe,CACfC,SAAS,CACTC,cAAc,CACdC,UAAU,KACL,6BAA6B,CACpC,OAASC,KAAK,KAAQ,iBAAiB,CAEvC;AACA,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,gBAAgB,KAAM,4BAA4B,CACzD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CAErD;AACA,OAASC,eAAe,KAAQ,gCAAgC,CAEhE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACnD,KAAM,CAAAE,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACuB,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAQ,EAAE,CAAC,CACjD,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC+B,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACAC,SAAS,CAAC,IAAM,CACdgC,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACV,IAAI,CAACW,EAAE,CAAC,CAAC,CAEb,KAAM,CAAAD,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAG,WAAW,CAAG,KAAM,CAAAnB,eAAe,CAACoB,qBAAqB,CAACb,IAAI,CAACW,EAAE,CAAC,CACxEN,UAAU,CAACO,WAAW,EAAI,EAAE,CAAC,CAC/B,CAAE,MAAOJ,KAAU,CAAE,CACnBM,OAAO,CAACN,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CC,QAAQ,CAAC,2BAA2B,CAAC,CACrCtB,KAAK,CAACqB,KAAK,CAAC,uBAAuB,CAAC,CACtC,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAQ,eAAe,CAAGX,OAAO,CAACF,MAAM,CAACc,UAAU,EAAI,CACnD,GAAId,MAAM,GAAK,WAAW,CAAE,CAC1B,MAAO,CAAAc,UAAU,CAACC,QAAQ,GAAK,GAAG,CACpC,CAAC,IAAM,IAAIf,MAAM,GAAK,aAAa,CAAE,CACnC,MAAO,CAAAc,UAAU,CAACC,QAAQ,CAAG,CAAC,EAAID,UAAU,CAACC,QAAQ,CAAG,GAAG,CAC7D,CACA,MAAO,KAAI,CACb,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAIC,QAAgB,EAAK,CAC9ClB,QAAQ,CAAC,mBAAmBkB,QAAQ,EAAE,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIH,QAAgB,EAAK,CAC7C,GAAIA,QAAQ,GAAK,GAAG,CAAE,MAAO,cAAc,CAC3C,GAAIA,QAAQ,EAAI,EAAE,CAAE,MAAO,aAAa,CACxC,MAAO,eAAe,CACxB,CAAC,CAED,KAAM,CAAAI,aAAa,CAAIJ,QAAgB,EAAK,CAC1C,GAAIA,QAAQ,GAAK,GAAG,CAAE,CACpB,mBAAOtB,IAAA,CAACZ,eAAe,EAACuC,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC/D,CACA,mBAAO3B,IAAA,CAACX,SAAS,EAACsC,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACxD,CAAC,CAED,GAAIhB,OAAO,CAAE,CACX,mBACEX,IAAA,QAAK2B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrD5B,IAAA,CAACH,cAAc,GAAE,CAAC,CACf,CAAC,CAEV,CAEA,GAAIgB,KAAK,CAAE,CACT,mBACEX,KAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,KAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5B,IAAA,CAACf,eAAe,EAAC0C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtD3B,IAAA,MAAA4B,QAAA,CAAIf,KAAK,CAAI,CAAC,EACX,CAAC,cACNb,IAAA,CAACL,gBAAgB,EAACkC,OAAO,CAAEd,mBAAoB,CAACe,OAAO,CAAC,SAAS,CAAAF,QAAA,CAAC,iFAElE,CAAkB,CAAC,EAChB,CAAC,CAEV,CAEA,mBACE1B,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1D1B,KAAA,QAAKyB,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAClG1B,KAAA,QAAA0B,QAAA,eACE5B,IAAA,CAACJ,cAAc,EAACmC,EAAE,CAAC,IAAI,CAACC,IAAI,CAAC,KAAK,CAACC,MAAM,CAAC,MAAM,CAACC,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,4CAE9D,CAAgB,CAAC,cACjB5B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC,MAAM,CAACP,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,+KAExD,CAAgB,CAAC,EACd,CAAC,cAGN1B,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D1B,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,MAAM,CAACC,KAAK,CAAC,SAAS,CAAAN,QAAA,CACpDnB,OAAO,CAAC0B,MAAM,CACD,CAAC,cACjBnC,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,uFAEvC,CAAgB,CAAC,EACd,CAAC,cACN1B,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,MAAM,CAACC,KAAK,CAAC,QAAQ,CAAAN,QAAA,CACnDnB,OAAO,CAACF,MAAM,CAAC6B,CAAC,EAAIA,CAAC,CAACd,QAAQ,GAAK,GAAG,CAAC,CAACa,MAAM,CACjC,CAAC,cACjBnC,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,sCAEvC,CAAgB,CAAC,EACd,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACR,cAAc,EAAC2C,OAAO,CAAC,IAAI,CAACV,SAAS,CAAC,UAAU,CAAAC,QAAA,eAC/C5B,IAAA,QAAK2B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrD1B,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5B,IAAA,CAACT,UAAU,EAACoC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAChD3B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,QAAQ,CAACC,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,iFAEvD,CAAgB,CAAC,EACd,CAAC,CACH,CAAC,cAEN1B,KAAA,QAAKyB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C1B,KAAA,CAACP,gBAAgB,EACfkC,OAAO,CAAEA,CAAA,GAAMrB,SAAS,CAAC,KAAK,CAAE,CAChCsB,OAAO,CAAEvB,MAAM,GAAK,KAAK,CAAG,SAAS,CAAG,OAAQ,CAChDyB,IAAI,CAAC,IAAI,CACTL,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAChC,6EACgB,CAACnB,OAAO,CAAC0B,MAAM,CAAC,GACjC,EAAkB,CAAC,cACnBjC,KAAA,CAACP,gBAAgB,EACfkC,OAAO,CAAEA,CAAA,GAAMrB,SAAS,CAAC,aAAa,CAAE,CACxCsB,OAAO,CAAEvB,MAAM,GAAK,aAAa,CAAG,SAAS,CAAG,OAAQ,CACxDyB,IAAI,CAAC,IAAI,CACTL,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAChC,2DACa,CAACnB,OAAO,CAACF,MAAM,CAAC6B,CAAC,EAAIA,CAAC,CAACd,QAAQ,CAAG,CAAC,EAAIc,CAAC,CAACd,QAAQ,CAAG,GAAG,CAAC,CAACa,MAAM,CAAC,GAC9E,EAAkB,CAAC,cACnBjC,KAAA,CAACP,gBAAgB,EACfkC,OAAO,CAAEA,CAAA,GAAMrB,SAAS,CAAC,WAAW,CAAE,CACtCsB,OAAO,CAAEvB,MAAM,GAAK,WAAW,CAAG,SAAS,CAAG,OAAQ,CACtDyB,IAAI,CAAC,IAAI,CACTL,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAChC,wCACS,CAACnB,OAAO,CAACF,MAAM,CAAC6B,CAAC,EAAIA,CAAC,CAACd,QAAQ,GAAK,GAAG,CAAC,CAACa,MAAM,CAAC,GAC1D,EAAkB,CAAC,EAChB,CAAC,EACQ,CAAC,cAGjBnC,IAAA,CAACP,cAAc,EACb6C,OAAO,CAAE,CAAEC,MAAM,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAE,CAAE,CAC9CC,GAAG,CAAC,IAAI,CACRC,QAAQ,CAAE,IAAK,CACfC,eAAe,CAAE,IAAK,CAAAhB,QAAA,CAErBR,eAAe,CAACyB,GAAG,CAAC,CAACxB,UAAU,CAAEyB,KAAK,GAAK,KAAAC,cAAA,CAAAC,eAAA,CAC1C,KAAM,CAAAC,MAAM,CAAG5B,UAAU,CAACZ,OAAO,CACjC,KAAM,CAAAa,QAAQ,CAAGD,UAAU,CAACC,QAAQ,EAAI,CAAC,CACzC,KAAM,CAAA4B,WAAW,CAAG,CAAAD,MAAM,SAANA,MAAM,kBAAAF,cAAA,CAANE,MAAM,CAAEE,MAAM,UAAAJ,cAAA,iBAAdA,cAAA,CAAgBZ,MAAM,GAAI,CAAC,CAC/C,KAAM,CAAAiB,eAAe,CAAGC,IAAI,CAACC,KAAK,CAAEhC,QAAQ,CAAG,GAAG,CAAI4B,WAAW,CAAC,CAElE,mBACElD,IAAA,CAACN,cAAc,EAEb6D,KAAK,CAAE,IAAK,CACZZ,QAAQ,CAAE,IAAK,CACfa,cAAc,CAAEV,KAAK,CAAG,GAAI,CAC5BjB,OAAO,CAAEA,CAAA,GAAMN,iBAAiB,CAAC0B,MAAM,CAACjC,EAAE,CAAE,CAC5CW,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAE1B1B,KAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1B,KAAA,QAAKyB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C1B,KAAA,QAAKyB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjE5B,IAAA,QAAK2B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D5B,IAAA,CAACf,eAAe,EAAC0C,SAAS,CAAC,wCAAwC,CAAE,CAAC,CACnE,CAAC,cACNzB,KAAA,QAAKyB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5B,IAAA,CAACJ,cAAc,EACbmC,EAAE,CAAC,IAAI,CACPC,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,UAAU,CACjBC,KAAK,CAAC,MAAM,CACZP,SAAS,CAAC,UAAU,CAAAC,QAAA,CAEnBqB,MAAM,CAACQ,KAAK,CACC,CAAC,cACjBzD,IAAA,CAACJ,cAAc,EACboC,IAAI,CAAC,IAAI,CACTE,KAAK,CAAC,MAAM,CACZP,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAE5BqB,MAAM,CAACS,WAAW,CACL,CAAC,EACd,CAAC,EACH,CAAC,cACN1D,IAAA,QAAK2B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BF,aAAa,CAACJ,QAAQ,CAAC,CACrB,CAAC,EACH,CAAC,cAGNpB,KAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1B,KAAA,QAAKyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,sCAEvC,CAAgB,CAAC,cACjB1B,KAAA,CAACN,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,QAAQ,CAACC,KAAK,CAAC,MAAM,CAAAN,QAAA,EACnDN,QAAQ,CAAC,GACZ,EAAgB,CAAC,EACd,CAAC,cACNtB,IAAA,QAAK2B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD5B,IAAA,QACE2B,SAAS,CAAE,gDAAgDF,gBAAgB,CAACH,QAAQ,CAAC,EAAG,CACxFqC,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGtC,QAAQ,GAAI,CAAE,CAC7B,CAAC,CACJ,CAAC,EACH,CAAC,cAGNpB,KAAA,QAAKyB,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjF1B,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5B,IAAA,CAACd,QAAQ,EAACyC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC9CzB,KAAA,SAAA0B,QAAA,EAAOwB,eAAe,CAAC,GAAC,CAACF,WAAW,CAAC,iCAAM,EAAM,CAAC,EAC/C,CAAC,cACNhD,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5B,IAAA,CAACb,yBAAyB,EAACwC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/DzB,KAAA,SAAA0B,QAAA,EAAO,CAAAqB,MAAM,SAANA,MAAM,kBAAAD,eAAA,CAANC,MAAM,CAAEY,OAAO,UAAAb,eAAA,iBAAfA,eAAA,CAAiBb,MAAM,GAAI,CAAC,CAAC,uCAAO,EAAM,CAAC,EAC/C,CAAC,EACH,CAAC,cAGNnC,IAAA,CAACL,gBAAgB,EACfkC,OAAO,CAAGiC,CAAC,EAAK,CACdA,CAAC,SAADA,CAAC,iBAADA,CAAC,CAAEC,eAAe,CAAC,CAAC,CACpBxC,iBAAiB,CAAC0B,MAAM,CAACjC,EAAE,CAAC,CAC9B,CAAE,CACFc,OAAO,CAAC,SAAS,CACjBE,IAAI,CAAC,IAAI,CACTgC,SAAS,CAAE,IAAK,CAChBC,IAAI,cAAEjE,IAAA,CAACV,cAAc,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAE,CAC7CuC,YAAY,CAAC,MAAM,CAAAtC,QAAA,CAElBN,QAAQ,GAAK,GAAG,CAAG,eAAe,CAAG,eAAe,CACrC,CAAC,EAChB,CAAC,EAlFDD,UAAU,CAACL,EAmFF,CAAC,CAErB,CAAC,CAAC,CACY,CAAC,CAGhBI,eAAe,CAACe,MAAM,GAAK,CAAC,eAC3BjC,KAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5B,IAAA,CAACf,eAAe,EAAC0C,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACpE3B,IAAA,CAACJ,cAAc,EAACmC,EAAE,CAAC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,QAAQ,CAACC,KAAK,CAAC,MAAM,CAACP,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,4EAEhF,CAAgB,CAAC,cACjB5B,IAAA,CAACJ,cAAc,EAACoC,IAAI,CAAC,IAAI,CAACE,KAAK,CAAC,MAAM,CAAAN,QAAA,CACnCrB,MAAM,GAAK,WAAW,CACnB,uBAAuB,CACvBA,MAAM,GAAK,aAAa,CACxB,2BAA2B,CAC3B,0BAA0B,CAEhB,CAAC,EACd,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}