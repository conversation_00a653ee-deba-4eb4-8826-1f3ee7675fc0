import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { User, Student, Admin } from '../types';
import { defaultAdmin, adminCredentials } from '../data/defaultAdmin';
import { mockStudents, studentCredentials } from '../data/mockStudents';
import { supabaseService } from './supabaseService';

class AuthService {
  // Admin login
  async loginAdmin(email: string, password: string): Promise<Admin> {
    try {
      // Check for default admin credentials
      if (email === adminCredentials.email && password === adminCredentials.password) {
        return defaultAdmin as Admin;
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Get admin data from Firestore
      const adminDoc = await getDoc(doc(db, 'admins', user.uid));
      if (!adminDoc.exists()) {
        throw new Error('المستخدم غير مخول كمدير');
      }
      
      const adminData = adminDoc.data();
      return {
        id: user.uid,
        email: user.email!,
        role: 'admin',
        name: adminData.name,
        avatar: adminData.avatar,
        permissions: adminData.permissions || [],
        createdAt: adminData.createdAt?.toDate() || new Date()
      };
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Student login with access code
  async loginStudent(accessCode: string): Promise<Student> {
    try {
      // Check for mock students first (for backward compatibility)
      const mockStudent = mockStudents.find(student => student.accessCode === accessCode);
      if (mockStudent) {
        return mockStudent;
      }

      // Find student by access code in Supabase
      const studentData = await supabaseService.getStudentByAccessCode(accessCode);

      if (!studentData) {
        throw new Error('كود الدخول غير صحيح');
      }

      if (!studentData.is_active) {
        throw new Error('الحساب غير مفعل');
      }

      // Transform Supabase data to match our Student type
      const enrolledCourses = studentData.student_enrollments?.map(enrollment => enrollment.course_id) || [];
      const completedCourses = studentData.student_enrollments?.filter(enrollment => enrollment.completed_at).map(enrollment => enrollment.course_id) || [];
      const certificates = studentData.certificates?.map(cert => cert.id) || [];

      return {
        id: studentData.id,
        email: studentData.email || '',
        role: 'student',
        name: studentData.name || '',
        avatar: studentData.avatar_url || '',
        accessCode: studentData.access_code,
        enrolledCourses,
        completedCourses,
        certificates,
        createdAt: new Date(studentData.created_at)
      };
    } catch (error: any) {
      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');
    }
  }

  // Generate access code for student
  generateAccessCode(): string {
    return Math.floor(1000000 + Math.random() * 9000000).toString();
  }

  // Create student account
  async createStudent(studentData: {
    name: string;
    email?: string;
    enrolledCourses?: string[];
  }): Promise<string> {
    try {
      const accessCode = this.generateAccessCode();

      // Create student in Supabase
      const newStudent = await supabaseService.createStudent({
        accessCode: accessCode,
        name: studentData.name,
        email: studentData.email
      });

      // Enroll student in courses if provided
      if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {
        for (const courseId of studentData.enrolledCourses) {
          await supabaseService.enrollStudent(newStudent.id, courseId);
        }
      }

      return accessCode;
    } catch (error: any) {
      // If access code already exists, try again
      if (error.message.includes('duplicate key')) {
        return this.createStudent(studentData);
      }
      throw new Error('فشل في إنشاء حساب الطالب');
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error: any) {
      throw new Error('فشل في تسجيل الخروج');
    }
  }

  // Get current user
  getCurrentUser(): Promise<FirebaseUser | null> {
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe();
        resolve(user);
      });
    });
  }

  // Auth state listener
  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'المستخدم غير موجود';
      case 'auth/wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'auth/invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'auth/user-disabled':
        return 'الحساب معطل';
      case 'auth/too-many-requests':
        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';
      default:
        return 'حدث خطأ في تسجيل الدخول';
    }
  }
}

export const authService = new AuthService();
