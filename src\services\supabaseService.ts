import { supabase } from '../config/supabase';
import { Student, Course, Category, Quiz, Certificate, Admin } from '../types';

export class SupabaseService {
  // Admin Services
  async createAdmin(adminData: {
    email: string;
    name: string;
    passwordHash: string;
    permissions?: string[];
    avatarUrl?: string;
  }) {
    const { data, error } = await supabase
      .from('admins')
      .insert([{
        email: adminData.email,
        name: adminData.name,
        password_hash: adminData.passwordHash,
        permissions: adminData.permissions || ['all'],
        avatar_url: adminData.avatarUrl
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAdminByEmail(email: string) {
    const { data, error } = await supabase
      .from('admins')
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching admin:', error);
      return null;
    }
    return data;
  }

  // Student Services
  async createStudent(studentData: {
    accessCode: string;
    name?: string;
    email?: string;
    avatarUrl?: string;
  }) {
    const { data, error } = await supabase
      .from('students')
      .insert([{
        access_code: studentData.accessCode,
        name: studentData.name,
        email: studentData.email,
        avatar_url: studentData.avatarUrl
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getStudentByAccessCode(accessCode: string) {
    try {
      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          student_enrollments (
            course_id,
            enrolled_at,
            completed_at,
            progress
          ),
          certificates (
            id,
            course_id,
            certificate_url,
            grade,
            issued_at
          )
        `)
        .eq('access_code', accessCode)
        .eq('is_active', true)
        .single();

      if (error) {
        console.warn('Supabase error, falling back to mock data:', error);
        return null; // Will trigger fallback in authService
      }
      return data;
    } catch (error) {
      console.warn('Supabase connection failed, falling back to mock data:', error);
      return null; // Will trigger fallback in authService
    }
  }

  async getAllStudents() {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        student_enrollments (
          course_id,
          enrolled_at,
          completed_at,
          progress
        ),
        certificates (
          id,
          course_id,
          certificate_url,
          grade,
          issued_at
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async updateStudent(id: string, updates: Partial<{
    name: string;
    email: string;
    avatarUrl: string;
    isActive: boolean;
  }>) {
    const { data, error } = await supabase
      .from('students')
      .update({
        name: updates.name,
        email: updates.email,
        avatar_url: updates.avatarUrl,
        is_active: updates.isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteStudent(id: string) {
    const { error } = await supabase
      .from('students')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Category Services
  async createCategory(categoryData: {
    name: string;
    description?: string;
  }) {
    const { data, error } = await supabase
      .from('categories')
      .insert([{
        name: categoryData.name,
        description: categoryData.description
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAllCategories() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data;
  }

  async updateCategory(id: string, updates: Partial<{
    name: string;
    description: string;
    isActive: boolean;
  }>) {
    const { data, error } = await supabase
      .from('categories')
      .update({
        name: updates.name,
        description: updates.description,
        is_active: updates.isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteCategory(id: string) {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Course Services
  async createCourse(courseData: {
    title: string;
    description: string;
    categoryId: string;
    instructorId: string;
    thumbnailUrl?: string;
    price?: number;
    durationHours?: number;
    level?: string;
  }) {
    const { data, error } = await supabase
      .from('courses')
      .insert([{
        title: courseData.title,
        description: courseData.description,
        category_id: courseData.categoryId,
        instructor_id: courseData.instructorId,
        thumbnail_url: courseData.thumbnailUrl,
        price: courseData.price || 0,
        duration_hours: courseData.durationHours || 0,
        level: courseData.level || 'beginner'
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAllCourses() {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        categories (
          id,
          name
        ),
        admins (
          id,
          name
        ),
        videos (
          id,
          title,
          duration,
          order_index
        ),
        quizzes (
          id,
          title
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async getCourseById(id: string) {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        categories (
          id,
          name
        ),
        admins (
          id,
          name
        ),
        videos (
          id,
          title,
          description,
          video_url,
          duration,
          order_index
        ),
        quizzes (
          id,
          title,
          description,
          passing_score,
          time_limit,
          questions (
            id,
            question,
            options,
            correct_answer,
            points,
            order_index
          )
        )
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) throw error;
    return data;
  }

  async updateCourse(id: string, updates: Partial<{
    title: string;
    description: string;
    categoryId: string;
    thumbnailUrl: string;
    price: number;
    durationHours: number;
    level: string;
    isActive: boolean;
  }>) {
    const { data, error } = await supabase
      .from('courses')
      .update({
        title: updates.title,
        description: updates.description,
        category_id: updates.categoryId,
        thumbnail_url: updates.thumbnailUrl,
        price: updates.price,
        duration_hours: updates.durationHours,
        level: updates.level,
        is_active: updates.isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteCourse(id: string) {
    const { error } = await supabase
      .from('courses')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Video Services
  async createVideo(videoData: {
    courseId: string;
    title: string;
    description?: string;
    videoUrl: string;
    duration?: number;
    orderIndex?: number;
  }) {
    const { data, error } = await supabase
      .from('videos')
      .insert([{
        course_id: videoData.courseId,
        title: videoData.title,
        description: videoData.description,
        video_url: videoData.videoUrl,
        duration: videoData.duration || 0,
        order_index: videoData.orderIndex || 0
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getVideosByCourseId(courseId: string) {
    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('course_id', courseId)
      .order('order_index');

    if (error) throw error;
    return data;
  }

  async updateVideo(id: string, updates: Partial<{
    title: string;
    description: string;
    videoUrl: string;
    duration: number;
    orderIndex: number;
  }>) {
    const { data, error } = await supabase
      .from('videos')
      .update({
        title: updates.title,
        description: updates.description,
        video_url: updates.videoUrl,
        duration: updates.duration,
        order_index: updates.orderIndex,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteVideo(id: string) {
    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Quiz Services
  async createQuiz(quizData: {
    courseId: string;
    title: string;
    description?: string;
    passingScore?: number;
    timeLimit?: number;
  }) {
    const { data, error } = await supabase
      .from('quizzes')
      .insert([{
        course_id: quizData.courseId,
        title: quizData.title,
        description: quizData.description,
        passing_score: quizData.passingScore || 70,
        time_limit: quizData.timeLimit || 30
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getQuizzesByCourseId(courseId: string) {
    const { data, error } = await supabase
      .from('quizzes')
      .select(`
        *,
        questions (
          id,
          question,
          options,
          correct_answer,
          points,
          order_index
        )
      `)
      .eq('course_id', courseId)
      .eq('is_active', true)
      .order('created_at');

    if (error) throw error;
    return data;
  }

  async getAllQuizzes() {
    const { data, error } = await supabase
      .from('quizzes')
      .select(`
        *,
        courses (
          id,
          title
        ),
        questions (
          id,
          question,
          options,
          correct_answer,
          points,
          order_index
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async updateQuiz(id: string, updates: Partial<{
    title: string;
    description: string;
    passingScore: number;
    timeLimit: number;
    isActive: boolean;
  }>) {
    const { data, error } = await supabase
      .from('quizzes')
      .update({
        title: updates.title,
        description: updates.description,
        passing_score: updates.passingScore,
        time_limit: updates.timeLimit,
        is_active: updates.isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteQuiz(id: string) {
    const { error } = await supabase
      .from('quizzes')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Question Services
  async createQuestion(questionData: {
    quizId: string;
    question: string;
    options: string[];
    correctAnswer: number;
    points?: number;
    orderIndex?: number;
  }) {
    const { data, error } = await supabase
      .from('questions')
      .insert([{
        quiz_id: questionData.quizId,
        question: questionData.question,
        options: questionData.options,
        correct_answer: questionData.correctAnswer,
        points: questionData.points || 1,
        order_index: questionData.orderIndex || 0
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getQuestionsByQuizId(quizId: string) {
    const { data, error } = await supabase
      .from('questions')
      .select('*')
      .eq('quiz_id', quizId)
      .order('order_index');

    if (error) throw error;
    return data;
  }

  async updateQuestion(id: string, updates: Partial<{
    question: string;
    options: string[];
    correctAnswer: number;
    points: number;
    orderIndex: number;
  }>) {
    const { data, error } = await supabase
      .from('questions')
      .update({
        question: updates.question,
        options: updates.options,
        correct_answer: updates.correctAnswer,
        points: updates.points,
        order_index: updates.orderIndex,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteQuestion(id: string) {
    const { error } = await supabase
      .from('questions')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Certificate Services
  async createCertificate(certificateData: {
    studentId: string;
    courseId: string;
    certificateUrl?: string;
    grade?: number;
  }) {
    const { data, error } = await supabase
      .from('certificates')
      .insert([{
        student_id: certificateData.studentId,
        course_id: certificateData.courseId,
        certificate_url: certificateData.certificateUrl,
        grade: certificateData.grade
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getCertificatesByStudentId(studentId: string) {
    const { data, error } = await supabase
      .from('certificates')
      .select(`
        *,
        courses (
          id,
          title,
          thumbnail_url
        )
      `)
      .eq('student_id', studentId)
      .order('issued_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async getAllCertificates() {
    const { data, error } = await supabase
      .from('certificates')
      .select(`
        *,
        students (
          id,
          name,
          access_code
        ),
        courses (
          id,
          title,
          thumbnail_url
        )
      `)
      .order('issued_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async deleteCertificate(id: string) {
    const { error } = await supabase
      .from('certificates')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Student Enrollment Services
  async enrollStudent(studentId: string, courseId: string) {
    const { data, error } = await supabase
      .from('student_enrollments')
      .insert([{
        student_id: studentId,
        course_id: courseId
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateStudentProgress(studentId: string, courseId: string, progress: number) {
    const { data, error } = await supabase
      .from('student_enrollments')
      .update({
        progress: progress,
        completed_at: progress >= 100 ? new Date().toISOString() : null
      })
      .eq('student_id', studentId)
      .eq('course_id', courseId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getStudentEnrollments(studentId: string) {
    const { data, error } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        courses (
          id,
          title,
          description,
          thumbnail_url,
          videos (
            id,
            title,
            duration
          )
        )
      `)
      .eq('student_id', studentId)
      .order('enrolled_at', { ascending: false });

    if (error) throw error;
    return data;
  }
}

export const supabaseService = new SupabaseService();
