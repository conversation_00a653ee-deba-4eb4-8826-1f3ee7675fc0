{"ast": null, "code": "/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter() {\n  let store = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}", "map": {"version": 3, "names": ["memoryLocalStorageAdapter", "store", "arguments", "length", "undefined", "getItem", "key", "setItem", "value", "removeItem"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@supabase\\auth-js\\src\\lib\\local-storage.ts"], "sourcesContent": ["import { SupportedStorage } from './types'\n\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store: { [key: string]: string } = {}): SupportedStorage {\n  return {\n    getItem: (key) => {\n      return store[key] || null\n    },\n\n    setItem: (key, value) => {\n      store[key] = value\n    },\n\n    removeItem: (key) => {\n      delete store[key]\n    },\n  }\n}\n"], "mappings": "AAEA;;;;AAIA,OAAM,SAAUA,yBAAyBA,CAAA,EAAsC;EAAA,IAArCC,KAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAmC,EAAE;EAC7E,OAAO;IACLG,OAAO,EAAGC,GAAG,IAAI;MACf,OAAOL,KAAK,CAACK,GAAG,CAAC,IAAI,IAAI;IAC3B,CAAC;IAEDC,OAAO,EAAEA,CAACD,GAAG,EAAEE,KAAK,KAAI;MACtBP,KAAK,CAACK,GAAG,CAAC,GAAGE,KAAK;IACpB,CAAC;IAEDC,UAAU,EAAGH,GAAG,IAAI;MAClB,OAAOL,KAAK,CAACK,GAAG,CAAC;IACnB;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}