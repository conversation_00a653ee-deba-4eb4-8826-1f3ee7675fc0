{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ResponsiveButton=_ref=>{let{children,onClick,type='button',variant='primary',size='md',fullWidth=false,disabled=false,loading=false,icon,iconPosition='right',className='',animated=true}=_ref;const baseClasses='inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';const variantClasses={primary:'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 shadow-sm hover:shadow-md',secondary:'bg-secondary-200 hover:bg-secondary-300 text-secondary-800 focus:ring-secondary-500 shadow-sm hover:shadow-md',outline:'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500',ghost:'text-primary-600 hover:bg-primary-50 focus:ring-primary-500',danger:'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 shadow-sm hover:shadow-md'};const sizeClasses={xs:'px-2 py-1 text-xs sm:px-3 sm:py-1.5 sm:text-sm',sm:'px-3 py-1.5 text-sm sm:px-4 sm:py-2 sm:text-base',md:'px-4 py-2 text-base sm:px-6 sm:py-3 sm:text-lg',lg:'px-6 py-3 text-lg sm:px-8 sm:py-4 sm:text-xl',xl:'px-8 py-4 text-xl sm:px-10 sm:py-5 sm:text-2xl'};const widthClass=fullWidth?'w-full':'';const disabledClass=disabled||loading?'opacity-50 cursor-not-allowed':'';const buttonClasses=`\n    ${baseClasses}\n    ${variantClasses[variant]}\n    ${sizeClasses[size]}\n    ${widthClass}\n    ${disabledClass}\n    ${className}\n  `.trim();const buttonContent=/*#__PURE__*/_jsxs(_Fragment,{children:[loading&&/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-current border-t-transparent rounded-full animate-spin ml-2\"}),icon&&iconPosition==='right'&&!loading&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:icon}),/*#__PURE__*/_jsx(\"span\",{className:loading?'opacity-75':'',children:children}),icon&&iconPosition==='left'&&!loading&&/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:icon})]});if(animated&&!disabled&&!loading){return/*#__PURE__*/_jsx(motion.button,{type:type,onClick:onClick,disabled:disabled||loading,className:buttonClasses,whileHover:{scale:1.02},whileTap:{scale:0.98},transition:{duration:0.1},children:buttonContent});}return/*#__PURE__*/_jsx(\"button\",{type:type,onClick:onClick,disabled:disabled||loading,className:buttonClasses,children:buttonContent});};export default ResponsiveButton;", "map": {"version": 3, "names": ["React", "motion", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "ResponsiveButton", "_ref", "children", "onClick", "type", "variant", "size", "fullWidth", "disabled", "loading", "icon", "iconPosition", "className", "animated", "baseClasses", "variantClasses", "primary", "secondary", "outline", "ghost", "danger", "sizeClasses", "xs", "sm", "md", "lg", "xl", "widthClass", "disabledClass", "buttonClasses", "trim", "buttonContent", "button", "whileHover", "scale", "whileTap", "transition", "duration"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/ResponsiveButton.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ResponsiveButtonProps {\n  children: React.ReactNode;\n  onClick?: (e?: React.MouseEvent<HTMLButtonElement>) => void;\n  type?: 'button' | 'submit' | 'reset';\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n  fullWidth?: boolean;\n  disabled?: boolean;\n  loading?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: 'left' | 'right';\n  className?: string;\n  animated?: boolean;\n}\n\nconst ResponsiveButton: React.FC<ResponsiveButtonProps> = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'md',\n  fullWidth = false,\n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'right',\n  className = '',\n  animated = true\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n\n  const variantClasses = {\n    primary: 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 shadow-sm hover:shadow-md',\n    secondary: 'bg-secondary-200 hover:bg-secondary-300 text-secondary-800 focus:ring-secondary-500 shadow-sm hover:shadow-md',\n    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500',\n    ghost: 'text-primary-600 hover:bg-primary-50 focus:ring-primary-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 shadow-sm hover:shadow-md'\n  };\n\n  const sizeClasses = {\n    xs: 'px-2 py-1 text-xs sm:px-3 sm:py-1.5 sm:text-sm',\n    sm: 'px-3 py-1.5 text-sm sm:px-4 sm:py-2 sm:text-base',\n    md: 'px-4 py-2 text-base sm:px-6 sm:py-3 sm:text-lg',\n    lg: 'px-6 py-3 text-lg sm:px-8 sm:py-4 sm:text-xl',\n    xl: 'px-8 py-4 text-xl sm:px-10 sm:py-5 sm:text-2xl'\n  };\n\n  const widthClass = fullWidth ? 'w-full' : '';\n  const disabledClass = disabled || loading ? 'opacity-50 cursor-not-allowed' : '';\n\n  const buttonClasses = `\n    ${baseClasses}\n    ${variantClasses[variant]}\n    ${sizeClasses[size]}\n    ${widthClass}\n    ${disabledClass}\n    ${className}\n  `.trim();\n\n  const buttonContent = (\n    <>\n      {loading && (\n        <div className=\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-current border-t-transparent rounded-full animate-spin ml-2\" />\n      )}\n      {icon && iconPosition === 'right' && !loading && (\n        <span className=\"ml-2\">{icon}</span>\n      )}\n      <span className={loading ? 'opacity-75' : ''}>{children}</span>\n      {icon && iconPosition === 'left' && !loading && (\n        <span className=\"mr-2\">{icon}</span>\n      )}\n    </>\n  );\n\n  if (animated && !disabled && !loading) {\n    return (\n      <motion.button\n        type={type}\n        onClick={onClick}\n        disabled={disabled || loading}\n        className={buttonClasses}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        transition={{ duration: 0.1 }}\n      >\n        {buttonContent}\n      </motion.button>\n    );\n  }\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={buttonClasses}\n    >\n      {buttonContent}\n    </button>\n  );\n};\n\nexport default ResponsiveButton;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiBvC,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAapD,IAbqD,CACzDC,QAAQ,CACRC,OAAO,CACPC,IAAI,CAAG,QAAQ,CACfC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,IAAI,CACXC,SAAS,CAAG,KAAK,CACjBC,QAAQ,CAAG,KAAK,CAChBC,OAAO,CAAG,KAAK,CACfC,IAAI,CACJC,YAAY,CAAG,OAAO,CACtBC,SAAS,CAAG,EAAE,CACdC,QAAQ,CAAG,IACb,CAAC,CAAAZ,IAAA,CACC,KAAM,CAAAa,WAAW,CAAG,gJAAgJ,CAEpK,KAAM,CAAAC,cAAc,CAAG,CACrBC,OAAO,CAAE,iGAAiG,CAC1GC,SAAS,CAAE,+GAA+G,CAC1HC,OAAO,CAAE,2GAA2G,CACpHC,KAAK,CAAE,6DAA6D,CACpEC,MAAM,CAAE,qFACV,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,EAAE,CAAE,gDAAgD,CACpDC,EAAE,CAAE,kDAAkD,CACtDC,EAAE,CAAE,gDAAgD,CACpDC,EAAE,CAAE,8CAA8C,CAClDC,EAAE,CAAE,gDACN,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGpB,SAAS,CAAG,QAAQ,CAAG,EAAE,CAC5C,KAAM,CAAAqB,aAAa,CAAGpB,QAAQ,EAAIC,OAAO,CAAG,+BAA+B,CAAG,EAAE,CAEhF,KAAM,CAAAoB,aAAa,CAAG;AACxB,MAAMf,WAAW;AACjB,MAAMC,cAAc,CAACV,OAAO,CAAC;AAC7B,MAAMgB,WAAW,CAACf,IAAI,CAAC;AACvB,MAAMqB,UAAU;AAChB,MAAMC,aAAa;AACnB,MAAMhB,SAAS;AACf,GAAG,CAACkB,IAAI,CAAC,CAAC,CAER,KAAM,CAAAC,aAAa,cACjBhC,KAAA,CAAAF,SAAA,EAAAK,QAAA,EACGO,OAAO,eACNd,IAAA,QAAKiB,SAAS,CAAC,mGAAmG,CAAE,CACrH,CACAF,IAAI,EAAIC,YAAY,GAAK,OAAO,EAAI,CAACF,OAAO,eAC3Cd,IAAA,SAAMiB,SAAS,CAAC,MAAM,CAAAV,QAAA,CAAEQ,IAAI,CAAO,CACpC,cACDf,IAAA,SAAMiB,SAAS,CAAEH,OAAO,CAAG,YAAY,CAAG,EAAG,CAAAP,QAAA,CAAEA,QAAQ,CAAO,CAAC,CAC9DQ,IAAI,EAAIC,YAAY,GAAK,MAAM,EAAI,CAACF,OAAO,eAC1Cd,IAAA,SAAMiB,SAAS,CAAC,MAAM,CAAAV,QAAA,CAAEQ,IAAI,CAAO,CACpC,EACD,CACH,CAED,GAAIG,QAAQ,EAAI,CAACL,QAAQ,EAAI,CAACC,OAAO,CAAE,CACrC,mBACEd,IAAA,CAACF,MAAM,CAACuC,MAAM,EACZ5B,IAAI,CAAEA,IAAK,CACXD,OAAO,CAAEA,OAAQ,CACjBK,QAAQ,CAAEA,QAAQ,EAAIC,OAAQ,CAC9BG,SAAS,CAAEiB,aAAc,CACzBI,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAnC,QAAA,CAE7B6B,aAAa,CACD,CAAC,CAEpB,CAEA,mBACEpC,IAAA,WACES,IAAI,CAAEA,IAAK,CACXD,OAAO,CAAEA,OAAQ,CACjBK,QAAQ,CAAEA,QAAQ,EAAIC,OAAQ,CAC9BG,SAAS,CAAEiB,aAAc,CAAA3B,QAAA,CAExB6B,aAAa,CACR,CAAC,CAEb,CAAC,CAED,cAAe,CAAA/B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}