import { supabaseService } from '../services/supabaseService';

// Sample data for seeding the database
const seedData = {
  // Admin data
  admin: {
    email: '<EMAIL>',
    name: 'مدير النظام - <PERSON><PERSON>',
    passwordHash: '$2b$10$placeholder_hash_for_Admin@123456', // This should be properly hashed
    permissions: ['all'],
    avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },

  // Categories data
  categories: [
    {
      name: 'البرمجة والتطوير',
      description: 'كورسات البرمجة وتطوير البرمجيات'
    },
    {
      name: 'تطوير المواقع',
      description: 'كورسات تطوير المواقع الإلكترونية'
    },
    {
      name: 'تطوير التطبيقات',
      description: 'كورسات تطوير تطبيقات الهاتف المحمول'
    },
    {
      name: 'الذكاء الاصطناعي',
      description: 'كورسات الذكاء الاصطناعي وتعلم الآلة'
    },
    {
      name: 'أمن المعلومات',
      description: 'كورسات أمن المعلومات والحماية السيبرانية'
    }
  ],

  // Students data
  students: [
    {
      accessCode: '1234567',
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '9876543',
      name: 'فاطمة أحمد حسن',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '5555555',
      name: 'محمد علي سالم',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '1111111',
      name: 'سارة خالد محمود',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '7777777',
      name: 'عبدالله يوسف أحمد',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '3333333',
      name: 'نور الهدى إبراهيم',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '8888888',
      name: 'حسام الدين محمد',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      accessCode: '2222222',
      name: 'ريم عبدالرحمن',
      email: '<EMAIL>',
      avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    }
  ],

  // Courses data
  courses: [
    {
      title: 'أساسيات البرمجة بـ JavaScript',
      description: 'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف. يغطي هذا الكورس المتغيرات، الدوال، الكائنات، والمفاهيم المتقدمة.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',
      price: 299,
      durationHours: 25,
      level: 'beginner'
    },
    {
      title: 'تطوير المواقع بـ React',
      description: 'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React. يشمل الكورس JSX، Components، State Management، وأفضل الممارسات.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
      price: 499,
      durationHours: 40,
      level: 'intermediate'
    },
    {
      title: 'تطوير تطبيقات الهاتف بـ React Native',
      description: 'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android باستخدام React Native. من الأساسيات إلى النشر في المتاجر.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
      price: 699,
      durationHours: 50,
      level: 'intermediate'
    },
    {
      title: 'الذكاء الاصطناعي وتعلم الآلة',
      description: 'مقدمة شاملة للذكاء الاصطناعي وتعلم الآلة باستخدام Python. يغطي الخوارزميات الأساسية والتطبيقات العملية.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop',
      price: 899,
      durationHours: 60,
      level: 'advanced'
    },
    {
      title: 'أمن المعلومات والحماية السيبرانية',
      description: 'تعلم أساسيات أمن المعلومات والحماية من التهديدات السيبرانية. يشمل التشفير، اختبار الاختراق، وأفضل الممارسات الأمنية.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=300&fit=crop',
      price: 799,
      durationHours: 45,
      level: 'advanced'
    }
  ]
};

// Function to seed the database
export async function seedDatabase() {
  try {
    console.log('🌱 بدء عملية إدخال البيانات...');

    // 1. Create admin
    console.log('📝 إنشاء حساب المدير...');
    const admin = await supabaseService.createAdmin(seedData.admin);
    console.log('✅ تم إنشاء حساب المدير:', admin.email);

    // 2. Create categories
    console.log('📂 إنشاء الأقسام...');
    const categories = [];
    for (const categoryData of seedData.categories) {
      const category = await supabaseService.createCategory(categoryData);
      categories.push(category);
      console.log('✅ تم إنشاء قسم:', category.name);
    }

    // 3. Create courses
    console.log('📚 إنشاء الكورسات...');
    const courses = [];
    for (let i = 0; i < seedData.courses.length; i++) {
      const courseData = seedData.courses[i];
      const categoryId = categories[i % categories.length].id; // Distribute courses among categories
      
      const course = await supabaseService.createCourse({
        ...courseData,
        categoryId,
        instructorId: admin.id
      });
      courses.push(course);
      console.log('✅ تم إنشاء كورس:', course.title);
    }

    // 4. Create students
    console.log('👥 إنشاء حسابات الطلاب...');
    const students = [];
    for (const studentData of seedData.students) {
      const student = await supabaseService.createStudent(studentData);
      students.push(student);
      console.log('✅ تم إنشاء طالب:', student.name, '- كود الدخول:', student.access_code);
    }

    // 5. Enroll students in courses
    console.log('📝 تسجيل الطلاب في الكورسات...');
    for (let i = 0; i < students.length; i++) {
      const student = students[i];
      // Enroll each student in 1-3 random courses
      const numCourses = Math.floor(Math.random() * 3) + 1;
      const shuffledCourses = [...courses].sort(() => 0.5 - Math.random());
      
      for (let j = 0; j < numCourses; j++) {
        const course = shuffledCourses[j];
        await supabaseService.enrollStudent(student.id, course.id);
        console.log(`✅ تم تسجيل ${student.name} في كورس ${course.title}`);
      }
    }

    // 6. Create sample videos for courses
    console.log('🎥 إنشاء فيديوهات تعليمية...');
    for (const course of courses) {
      // Create 3-5 videos per course
      const numVideos = Math.floor(Math.random() * 3) + 3;
      for (let i = 1; i <= numVideos; i++) {
        await supabaseService.createVideo({
          courseId: course.id,
          title: `الدرس ${i}: مقدمة في ${course.title}`,
          description: `شرح مفصل للدرس رقم ${i} من كورس ${course.title}`,
          videoUrl: `https://example.com/videos/${course.id}/lesson-${i}.mp4`,
          duration: Math.floor(Math.random() * 30) + 10, // 10-40 minutes
          orderIndex: i
        });
      }
      console.log(`✅ تم إنشاء ${numVideos} فيديو لكورس ${course.title}`);
    }

    // 7. Create sample quizzes
    console.log('📝 إنشاء اختبارات...');
    for (const course of courses) {
      const quiz = await supabaseService.createQuiz({
        courseId: course.id,
        title: `اختبار ${course.title}`,
        description: `اختبار شامل لقياس فهمك لمحتوى كورس ${course.title}`,
        passingScore: 70,
        timeLimit: 30
      });

      // Create 5-10 questions per quiz
      const numQuestions = Math.floor(Math.random() * 6) + 5;
      for (let i = 1; i <= numQuestions; i++) {
        await supabaseService.createQuestion({
          quizId: quiz.id,
          question: `السؤال ${i}: ما هو المفهوم الأساسي في ${course.title}؟`,
          options: [
            'الخيار الأول',
            'الخيار الثاني (الصحيح)',
            'الخيار الثالث',
            'الخيار الرابع'
          ],
          correctAnswer: 1, // Second option is correct
          points: 1,
          orderIndex: i
        });
      }
      console.log(`✅ تم إنشاء اختبار مع ${numQuestions} سؤال لكورس ${course.title}`);
    }

    console.log('🎉 تم إدخال جميع البيانات بنجاح!');
    console.log('\n📋 ملخص البيانات المدخلة:');
    console.log(`- ${1} مدير`);
    console.log(`- ${categories.length} أقسام`);
    console.log(`- ${courses.length} كورسات`);
    console.log(`- ${students.length} طلاب`);
    console.log(`- فيديوهات تعليمية لجميع الكورسات`);
    console.log(`- اختبارات وأسئلة لجميع الكورسات`);

    console.log('\n🔑 بيانات تسجيل الدخول:');
    console.log('المدير:');
    console.log(`  البريد الإلكتروني: ${seedData.admin.email}`);
    console.log(`  كلمة المرور: Admin@123456`);
    console.log('\nالطلاب (أكواد الدخول):');
    seedData.students.forEach(student => {
      console.log(`  ${student.name}: ${student.accessCode}`);
    });

  } catch (error) {
    console.error('❌ حدث خطأ في إدخال البيانات:', error);
    throw error;
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ تمت عملية إدخال البيانات بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشلت عملية إدخال البيانات:', error);
      process.exit(1);
    });
}
