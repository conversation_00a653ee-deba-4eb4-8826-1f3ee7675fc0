import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  UserIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface StudentSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const StudentSidebar: React.FC<StudentSidebarProps> = ({ isOpen, onClose }) => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'الرئيسية',
      href: '/student',
      icon: HomeIcon,
      exact: true
    },
    {
      name: 'كورساتي',
      href: '/student/courses',
      icon: AcademicCapIcon
    },
    {
      name: 'الاختبارات',
      href: '/student/quizzes',
      icon: ClipboardDocumentListIcon
    },
    {
      name: 'شهاداتي',
      href: '/student/certificates',
      icon: DocumentTextIcon
    },
    {
      name: 'الملف الشخصي',
      href: '/student/profile',
      icon: UserIcon
    }
  ];

  const isActive = (href: string, exact?: boolean) => {
    if (exact) {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64 xl:w-72">
          <div className="flex flex-col flex-grow bg-white border-l border-gray-200 pt-4 sm:pt-5 pb-4 overflow-y-auto shadow-sm">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-3 sm:px-4 mb-6 sm:mb-8">
              <div className="bg-primary-600 rounded-lg p-2 shadow-md">
                <AcademicCapIcon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <div className="mr-2 sm:mr-3">
                <h2 className="text-base sm:text-lg font-bold text-gray-900 font-display">ALaa Abd Hamied</h2>
                <p className="text-xs sm:text-sm text-gray-500 font-body">منصة التعلم</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="mt-3 sm:mt-5 flex-1 px-2 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.href, item.exact);

                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={`
                      group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105
                      ${active
                        ? 'bg-gradient-to-r from-primary-100 to-primary-50 text-primary-900 border-l-4 border-primary-600 shadow-sm'
                        : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-25 hover:text-gray-900 hover:shadow-sm'
                      }
                    `}
                  >
                    <Icon
                      className={`
                        ml-3 flex-shrink-0 h-5 w-5 sm:h-6 sm:w-6 transition-all duration-200
                        ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                      `}
                    />
                    <span className="font-body">{item.name}</span>
                  </NavLink>
                );
              })}
            </nav>

            {/* Student Info Card */}
            <div className="px-4 py-4 border-t border-gray-200">
              <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                    <UserIcon className="w-5 h-5 text-white" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">طالب نشط</p>
                    <p className="text-xs text-gray-500">استمر في التعلم!</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: isOpen ? 0 : -300 }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl lg:hidden"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="bg-primary-600 rounded-lg p-2">
                <AcademicCapIcon className="w-6 h-6 text-white" />
              </div>
              <div className="mr-3">
                <h2 className="text-lg font-bold text-gray-900">ALaa Abd Hamied</h2>
                <p className="text-sm text-gray-500">منصة التعلم</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href, item.exact);
              
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200
                    ${active
                      ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon
                    className={`
                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200
                      ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </NavLink>
              );
            })}
          </nav>

          {/* Student Info Card */}
          <div className="px-4 py-4 border-t border-gray-200">
            <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                  <UserIcon className="w-5 h-5 text-white" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">طالب نشط</p>
                  <p className="text-xs text-gray-500">استمر في التعلم!</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default StudentSidebar;
