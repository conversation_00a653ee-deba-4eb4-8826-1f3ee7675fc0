{"ast": null, "code": "export const mockCourses=[{id:'1',title:'أساسيات البرمجة بـ JavaScript',description:'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',categoryId:'cat-1',instructorId:'admin-001',thumbnailUrl:'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',price:299,duration:25,level:'beginner',videos:[],pdfs:[],quizzes:[],enrolledStudents:15,isActive:true,createdAt:new Date('2024-01-01'),updatedAt:new Date('2024-01-01')},{id:'2',title:'تطوير المواقع بـ React',description:'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',categoryId:'cat-1',instructorId:'admin-001',thumbnailUrl:'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',price:499,duration:40,level:'intermediate',videos:[],pdfs:[],quizzes:[],enrolledStudents:12,isActive:true,createdAt:new Date('2024-01-15'),updatedAt:new Date('2024-01-15')},{id:'3',title:'تطوير تطبيقات الهاتف بـ React Native',description:'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',categoryId:'cat-2',instructorId:'admin-001',thumbnailUrl:'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',price:699,duration:50,level:'intermediate',videos:[],pdfs:[],quizzes:[],enrolledStudents:8,isActive:true,createdAt:new Date('2024-02-01'),updatedAt:new Date('2024-02-01')}];export const mockVideos=[{id:'video-1',title:'مقدمة في JavaScript',url:'https://example.com/video1.mp4',duration:15,order:1},{id:'video-2',title:'المتغيرات والثوابت',url:'https://example.com/video2.mp4',duration:20,order:2}];export const mockQuizzes=[{id:'quiz-1',courseId:'1',title:'اختبار JavaScript الأساسي',description:'اختبار لقياس فهمك لأساسيات JavaScript',questions:[{id:'q1',question:'ما هو JavaScript؟',type:'multiple-choice',options:['لغة برمجة','قاعدة بيانات','نظام تشغيل','متصفح'],correctAnswer:0,points:1}],passingScore:70,timeLimit:30,attempts:3,isActive:true,createdAt:new Date('2024-01-01')}];export const mockCertificates=[{id:'cert-001',studentId:'student-001',courseId:'1',templateUrl:'/templates/cert-template.pdf',certificateUrl:'/certificates/cert-001.pdf',issuedAt:new Date('2024-01-20'),verificationCode:'CERT-2024-001'}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "thumbnailUrl", "price", "duration", "level", "videos", "pdfs", "quizzes", "enrolledStudents", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "url", "order", "mockQuizzes", "courseId", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "points", "passingScore", "timeLimit", "attempts", "mockCertificates", "studentId", "templateUrl", "certificateUrl", "issuedAt", "verificationCode"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة بـ JavaScript',\n    description: 'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',\n    categoryId: 'cat-1',\n    instructorId: 'admin-001',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',\n    price: 299,\n    duration: 25,\n    level: 'beginner',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 15,\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-01')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع بـ React',\n    description: 'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',\n    categoryId: 'cat-1',\n    instructorId: 'admin-001',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n    price: 499,\n    duration: 40,\n    level: 'intermediate',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 12,\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '3',\n    title: 'تطوير تطبيقات الهاتف بـ React Native',\n    description: 'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',\n    categoryId: 'cat-2',\n    instructorId: 'admin-001',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',\n    price: 699,\n    duration: 50,\n    level: 'intermediate',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 8,\n    isActive: true,\n    createdAt: new Date('2024-02-01'),\n    updatedAt: new Date('2024-02-01')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: 'video-1',\n    title: 'مقدمة في JavaScript',\n    url: 'https://example.com/video1.mp4',\n    duration: 15,\n    order: 1\n  },\n  {\n    id: 'video-2',\n    title: 'المتغيرات والثوابت',\n    url: 'https://example.com/video2.mp4',\n    duration: 20,\n    order: 2\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: 'quiz-1',\n    courseId: '1',\n    title: 'اختبار JavaScript الأساسي',\n    description: 'اختبار لقياس فهمك لأساسيات JavaScript',\n    questions: [\n      {\n        id: 'q1',\n        question: 'ما هو JavaScript؟',\n        type: 'multiple-choice',\n        options: ['لغة برمجة', 'قاعدة بيانات', 'نظام تشغيل', 'متصفح'],\n        correctAnswer: 0,\n        points: 1\n      }\n    ],\n    passingScore: 70,\n    timeLimit: 30,\n    attempts: 3,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    templateUrl: '/templates/cert-template.pdf',\n    certificateUrl: '/certificates/cert-001.pdf',\n    issuedAt: new Date('2024-01-20'),\n    verificationCode: 'CERT-2024-001'\n  }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,WAAqB,CAAG,CACnC,CACEC,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,+BAA+B,CACtCC,WAAW,CAAE,oEAAoE,CACjFC,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,EAAE,CACpBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,4DAA4D,CACzEC,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,cAAc,CACrBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,EAAE,CACpBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,sCAAsC,CAC7CC,WAAW,CAAE,wDAAwD,CACrEC,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,cAAc,CACrBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,CAAC,CACnBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,UAAmB,CAAG,CACjC,CACEjB,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,qBAAqB,CAC5BiB,GAAG,CAAE,gCAAgC,CACrCX,QAAQ,CAAE,EAAE,CACZY,KAAK,CAAE,CACT,CAAC,CACD,CACEnB,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,oBAAoB,CAC3BiB,GAAG,CAAE,gCAAgC,CACrCX,QAAQ,CAAE,EAAE,CACZY,KAAK,CAAE,CACT,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,WAAmB,CAAG,CACjC,CACEpB,EAAE,CAAE,QAAQ,CACZqB,QAAQ,CAAE,GAAG,CACbpB,KAAK,CAAE,2BAA2B,CAClCC,WAAW,CAAE,uCAAuC,CACpDoB,SAAS,CAAE,CACT,CACEtB,EAAE,CAAE,IAAI,CACRuB,QAAQ,CAAE,mBAAmB,CAC7BC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CAAC,WAAW,CAAE,cAAc,CAAE,YAAY,CAAE,OAAO,CAAC,CAC7DC,aAAa,CAAE,CAAC,CAChBC,MAAM,CAAE,CACV,CAAC,CACF,CACDC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,CAAC,CACXjB,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAgB,gBAA+B,CAAG,CAC7C,CACE/B,EAAE,CAAE,UAAU,CACdgC,SAAS,CAAE,aAAa,CACxBX,QAAQ,CAAE,GAAG,CACbY,WAAW,CAAE,8BAA8B,CAC3CC,cAAc,CAAE,4BAA4B,CAC5CC,QAAQ,CAAE,GAAI,CAAApB,IAAI,CAAC,YAAY,CAAC,CAChCqB,gBAAgB,CAAE,eACpB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}